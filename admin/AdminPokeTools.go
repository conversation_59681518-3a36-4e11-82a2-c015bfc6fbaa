package admin

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/trainer"
	"go-nakama-poke/user"
	"sort"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RpcAdminSaveAllPokemonToTrainer 将图鉴中所有宝可梦保存给指定训练师
func RpcAdminSaveAllPokemonToTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证管理员权限
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}

	// 解析请求数据
	var request struct {
		TrainerID int64 `json:"trainer_id"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求数据", 400)
	}

	// 获取训练师信息
	trainerInfo, err := trainer.SelectTrainerProto(ctx, db, request.TrainerID)
	if err != nil {
		return "", fmt.Errorf("获取训练师信息失败: %v", err)
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()

	// 获取所有宝可梦信息
	pokedex := poke.GetAllPokemonInfo()
	successCount := 0
	failCount := 0
	// 先拿到所有值，放到 slice
	var pokemonList []*MainServer.PSPokemonData
	for key, data := range pokedex {
		data.Name = key
		if data.BaseSpecies == "" {
			pokemonList = append(pokemonList, data)
		}
	}

	// 排序
	sort.Slice(pokemonList, func(i, j int) bool {
		return pokemonList[i].Num < pokemonList[j].Num
	})
	logger.Info("正常创建宝可梦")
	for _, p := range pokemonList {
		pokeName := p.Name
		// 创建一个新的宝可梦实例
		newPoke, err := poke.CreatePoke(ctx, tx, pokeName, "", 50) // 默认等级50
		if err != nil {
			logger.Error("创建宝可梦失败 %s: %v", pokeName, err)
			failCount++
			continue
		}

		// 设置训练师ID
		newPoke.Tid = request.TrainerID
		err = trainer.TakeWildPoke(ctx, logger, tx, trainerInfo, newPoke)
		if err != nil {
			logger.Error("保存到盒子失败 %s: %v", pokeName, err)
			failCount++
			continue
		}

		successCount++
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"fail_count":    failCount,
		"total":         len(pokedex),
	}
	resultJSON, _ := json.Marshal(result)
	logger.Info("创建宝可梦完成")
	return string(resultJSON), nil
}

// RpcAdminSavePokemonToTrainer 将指定宝可梦保存给训练师
func RpcAdminSavePokemonToTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证管理员权限
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}

	// 解析请求数据
	var request struct {
		TrainerID int64  `json:"trainer_id"`
		PokeName  string `json:"poke_name"`
		Level     int    `json:"level"`
		Shiny     int32  `json:"shiny"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求数据", 400)
	}

	// 验证宝可梦是否存在
	if _, exists := poke.GetPokemonInfo(request.PokeName); !exists {
		return "", runtime.NewError("宝可梦不存在", 404)
	}

	// 获取训练师信息
	trainerInfo, err := trainer.SelectTrainerProto(ctx, db, request.TrainerID)
	if err != nil {
		return "", fmt.Errorf("获取训练师信息失败: %v", err)
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()

	// 创建宝可梦
	newPoke, err := poke.CreatePoke(ctx, tx, request.PokeName, "", request.Level)
	if err != nil {
		return "", fmt.Errorf("创建宝可梦失败: %v", err)
	}
	// 保存到box中
	err = trainer.TakeWildPoke(ctx, logger, tx, trainerInfo, newPoke)
	if err != nil {
		return "", fmt.Errorf("保存到盒子失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}

	return "保存成功", nil
}
