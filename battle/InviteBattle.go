package battle

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"sync"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 存储战斗邀请记录的内存缓存
type InviteBattleCache struct {
	// 记录Normal类型邀请的拒绝记录，key为"proposerId_targetId"，value为拒绝时间戳
	normalRejections map[string]int64
	// 记录Force类型邀请的记录，key为targetId，value为该玩家被Force邀请的记录
	forceInvites map[int64][]*MainServer.InviteBattleRecord
	mutex        sync.RWMutex
}

var inviteBattleCache = &InviteBattleCache{
	normalRejections: make(map[string]int64),
	forceInvites:     make(map[int64][]*MainServer.InviteBattleRecord),
}

// 检查是否可以发送Normal类型邀请
func (cache *InviteBattleCache) canSendNormalInvite(proposerId, targetId int64) bool {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	key := fmt.Sprintf("%d_%d", proposerId, targetId)
	rejectTime, exists := cache.normalRejections[key]
	if !exists {
		return true
	}

	// 检查是否在30分钟冷却期内
	return time.Now().Unix()-rejectTime > 30*60
}

// 记录Normal类型邀请被拒绝
func (cache *InviteBattleCache) recordNormalRejection(proposerId, targetId int64) {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	key := fmt.Sprintf("%d_%d", proposerId, targetId)
	cache.normalRejections[key] = time.Now().Unix()
}

// 检查是否可以发送Force类型邀请
func (cache *InviteBattleCache) canSendForceInvite(targetId int64) (bool, string) {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	records, exists := cache.forceInvites[targetId]
	if !exists {
		return true, ""
	}

	// 检查今天的Force邀请次数
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayCount := 0
	var lastInviteTime int64

	for _, record := range records {
		// 如果记录是今天的
		if record.InviteTime >= today.Unix() {
			todayCount++
		}

		// 更新最后一次邀请时间
		if record.InviteTime > lastInviteTime {
			lastInviteTime = record.InviteTime
		}
	}

	// 检查30分钟冷却期
	if now.Unix()-lastInviteTime < 30*60 {
		return false, "上一次强制战斗邀请在30分钟内，无法再次发起"
	}

	// 检查每日限制
	if todayCount >= 5 {
		return false, "该玩家今日已被强制邀请5次，无法再次发起"
	}

	return true, ""
}

// 记录Force类型邀请
func (cache *InviteBattleCache) recordForceInvite(proposerId, targetId int64, inviteBattleType MainServer.BattleType) {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	record := &MainServer.InviteBattleRecord{
		ProposerId:       proposerId,
		TargetId:         targetId,
		InviteType:       MainServer.InviteBattleType_InviteBattleType_Force,
		InviteTime:       time.Now().Unix(),
		Responded:        false,
		InviteBattleType: inviteBattleType,
	}

	if _, exists := cache.forceInvites[targetId]; !exists {
		cache.forceInvites[targetId] = make([]*MainServer.InviteBattleRecord, 0)
	}
	cache.forceInvites[targetId] = append(cache.forceInvites[targetId], record)

	// 清理过期记录
	cache.cleanupOldRecords(targetId)
}

// 记录Normal类型邀请
func (cache *InviteBattleCache) recordNormalInvite(proposerId, targetId int64, inviteBattleType MainServer.BattleType) {
	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	record := &MainServer.InviteBattleRecord{
		ProposerId:       proposerId,
		TargetId:         targetId,
		InviteType:       MainServer.InviteBattleType_InviteBattleType_Normal,
		InviteTime:       time.Now().Unix(),
		Responded:        false,
		InviteBattleType: inviteBattleType,
	}

	if _, exists := cache.forceInvites[targetId]; !exists {
		cache.forceInvites[targetId] = make([]*MainServer.InviteBattleRecord, 0)
	}
	cache.forceInvites[targetId] = append(cache.forceInvites[targetId], record)
}

// 获取最近的邀请记录
func (cache *InviteBattleCache) getLatestInvite(proposerId, targetId int64) (*MainServer.InviteBattleRecord, bool) {
	cache.mutex.RLock()
	defer cache.mutex.RUnlock()

	records, exists := cache.forceInvites[targetId]
	if !exists || len(records) == 0 {
		return nil, false
	}

	// 查找最近的由proposerId发起的邀请
	var latestRecord *MainServer.InviteBattleRecord
	var latestTime int64

	for _, record := range records {
		if record.ProposerId == proposerId && record.InviteTime > latestTime {
			latestRecord = record
			latestTime = record.InviteTime
		}
	}

	if latestRecord == nil {
		return nil, false
	}

	return latestRecord, true
}

// 清理过期的Force邀请记录
func (cache *InviteBattleCache) cleanupOldRecords(targetId int64) {
	records := cache.forceInvites[targetId]
	if len(records) == 0 {
		return
	}

	// 保留最近3天的记录
	now := time.Now()
	cutoff := now.AddDate(0, 0, -3).Unix()
	newRecords := make([]*MainServer.InviteBattleRecord, 0)

	for _, record := range records {
		if record.InviteTime >= cutoff {
			newRecords = append(newRecords, record)
		}
	}

	cache.forceInvites[targetId] = newRecords
}

// 发起战斗邀请
func RpcInviteBattle(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取发起邀请的训练师
	proposer := tool.GetActiveTrainerByCtx(ctx)
	if proposer == nil {
		return "", runtime.NewError("未找到发起邀请的训练师", 400)
	}

	// 检查发起者是否是队长
	proposerParty, proposerInParty := tool.GetGlobalPartyMap().Get(proposer.Id)
	if proposerInParty && proposerParty.Leader.Id != proposer.Id {
		return "", runtime.NewError("只有队长才能发起战斗邀请", 403)
	}

	// 解析请求参数
	var param MainServer.InviteBattleParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析InviteBattleParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 获取目标训练师
	target := tool.GetActiveTrainerByTid(param.TrainerId)
	if target == nil {
		return "", runtime.NewError("目标训练师不存在或不在线", 404)
	}

	// 检查目标训练师是否处于idle状态
	if target.Action != MainServer.TrainerActionType_idle {
		return "", runtime.NewError("目标训练师当前无法接受战斗邀请", 403)
	}

	// 获取目标训练师所在队伍的队长
	targetParty, targetInParty := tool.GetGlobalPartyMap().Get(target.Id)
	var targetLeader *MainServer.Trainer
	if targetInParty {
		targetLeader = targetParty.Leader
		// 如果目标不是队长，则将邀请重定向到队长
		if target.Id != targetLeader.Id {
			target = targetLeader
		}
	} else {
		targetLeader = target
	}

	// 根据邀请类型处理
	switch param.InviteType {
	case MainServer.InviteBattleType_InviteBattleType_Normal:
		// 检查是否在冷却期内
		if !inviteBattleCache.canSendNormalInvite(proposer.Id, target.Id) {
			return "", runtime.NewError("你最近被该训练师拒绝过，需等待30分钟后再次邀请", 403)
		}

		// 记录Normal邀请
		inviteBattleCache.recordNormalInvite(proposer.Id, target.Id, param.BattleType)

		// 发送通知给目标训练师
		if err := tool.SendInviteBattleNotification(ctx, logger, nk, proposer, target, MainServer.InviteBattleType_InviteBattleType_Normal); err != nil {
			logger.Error("发送通知失败: %v", err)
			return "", runtime.NewError("发送邀请通知失败", 500)
		}

	case MainServer.InviteBattleType_InviteBattleType_Force:
		// 检查是否可以发送Force邀请
		canSend, reason := inviteBattleCache.canSendForceInvite(target.Id)
		if !canSend {
			return "", runtime.NewError(reason, 403)
		}

		// 记录Force邀请
		inviteBattleCache.recordForceInvite(proposer.Id, target.Id, param.BattleType)

		// 直接创建Match并通知双方
		return createBattleMatch(ctx, logger, nk, proposer, target, param.BattleType)

	default:
		return "", runtime.NewError("无效的邀请类型", 400)
	}

	response := &MainServer.InviteBattleResponse{
		Success: true,
		Message: "战斗邀请已发送",
	}

	// responseBytes, err := proto.Marshal(response)
	// if err != nil {
	// 	logger.Error("序列化响应失败: %v", err)
	// 	return "", runtime.NewError("内部服务器错误", 500)
	// }

	return tool.ProtoToBase64(response)
}

// 接受或拒绝战斗邀请
func RpcAcceptBattleInvite(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取接收邀请的训练师
	target := tool.GetActiveTrainerByCtx(ctx)
	if target == nil {
		return "", runtime.NewError("未找到接收邀请的训练师", 400)
	}

	// 检查接收者是否是队长
	targetParty, targetInParty := tool.GetGlobalPartyMap().Get(target.Id)
	if targetInParty && targetParty.Leader.Id != target.Id {
		return "", runtime.NewError("只有队长才能接受或拒绝战斗邀请", 403)
	}

	// 解析请求参数
	var param MainServer.InviteBattleAcceptParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析InviteBattleAcceptParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 获取发起邀请的训练师
	proposer := tool.GetActiveTrainerByTid(param.ProposerId)
	if proposer == nil {
		return "", runtime.NewError("发起邀请的训练师不存在或不在线", 404)
	}

	// 获取发起者所在队伍
	proposerParty, proposerInParty := tool.GetGlobalPartyMap().Get(proposer.Id)

	// 如果拒绝邀请
	if !param.Accept {
		// 记录拒绝
		inviteBattleCache.recordNormalRejection(proposer.Id, target.Id)

		response := &MainServer.InviteBattleAcceptResponse{
			Success: true,
			Message: "已拒绝战斗邀请",
		}
		return tool.ProtoToBase64(response)
	}

	// 获取邀请记录
	inviteRecord, exists := inviteBattleCache.getLatestInvite(proposer.Id, target.Id)
	if !exists {
		return "", runtime.NewError("未找到有效的邀请记录", 404)
	}

	// 如果接受邀请，创建Match
	matchId, err := createBattleMatch(ctx, logger, nk, proposer, target, inviteRecord.InviteBattleType)
	if err != nil {
		return "", err
	}

	// 通知双方队伍的所有成员
	if err := notifyAllTeamMembers(ctx, logger, nk, matchId, proposer, target, proposerParty, targetParty, proposerInParty, targetInParty); err != nil {
		logger.Error("通知队伍成员失败: %v", err)
		// 不中断流程，继续返回成功
	}

	response := &MainServer.InviteBattleAcceptResponse{
		Success: true,
		Message: "已接受战斗邀请",
		MatchId: matchId,
	}
	return tool.ProtoToBase64(response)
}

// 创建战斗Match并通知双方
func createBattleMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, proposer, target *MainServer.Trainer, inviteBattleType MainServer.BattleType) (string, error) {
	// 确定战斗类型
	battleType := determineBattleType(proposer, target, inviteBattleType)

	// 创建战斗准备信息
	prepareInfo := &MainServer.BattlePrepare{
		BattleType: battleType,
		BattleMatchMaker: &MainServer.BattleMatchMaker{
			MatchStyle:       MainServer.BattleMatchMatchStyle_free,
			ThreeTrainerType: MainServer.BattleMatchThreeTrainerType_accept,
		},
	}
	matchId, err := startBattleMatch(ctx, logger, nk, proposer, prepareInfo)
	if err != nil {
		logger.Error("创建Match失败: %v", err)
		return "", runtime.NewError("创建战斗失败", 500)
	}

	// // 创建战斗准备信息
	// // 创建匹配器
	// matchMaker := &MainServer.BattleMatchMaker{
	// 	MatchStyle:       MainServer.BattleMatchMatchStyle_free,
	// 	ThreeTrainerType: MainServer.BattleMatchThreeTrainerType_accept,
	// }
	// 使用matchLabelBy函数创建匹配标签
	// matchLabel := matchLabelBy(proposer, battleType, prepareInfo.BattleMatchMaker)
	// // matchLabel.Min = 2
	// // 创建Match
	// matchId, err := createCustomMatch(ctx, logger, nk, battleType, matchLabel)
	// if err != nil {
	// 	logger.Error("创建Match失败: %v", err)
	// 	return "", runtime.NewError("创建战斗失败", 500)
	// }

	// // 创建战斗信息
	// battleInfo := &tool.BattleInfo{
	// 	BattleID:      matchId,
	// 	MatchLabel:    matchLabel,
	// 	CreateTrainer: proposer,
	// 	BattlePrepare: prepareInfo,
	// 	BattlePokes:   map[int64]map[int64]*MainServer.Poke{},
	// 	CreateTs:      time.Now().Unix(),
	// 	UpdateTs:      time.Now().Unix(),
	// 	PartyInfos:    make(map[string]*tool.PartyInfo),
	// }

	// // 创建双方的PartyInfo
	// proposerPartyInfo := &tool.PartyInfo{
	// 	Trainers: map[int64]*MainServer.Trainer{proposer.Id: proposer},
	// 	PartyId:  proposer.Uid,
	// 	Leader:   proposer,
	// 	CreateTs: time.Now().Unix(),
	// 	UpdateTs: time.Now().Unix(),
	// }

	// targetPartyInfo := &tool.PartyInfo{
	// 	Trainers: map[int64]*MainServer.Trainer{target.Id: target},
	// 	PartyId:  target.Uid,
	// 	Leader:   target,
	// 	CreateTs: time.Now().Unix(),
	// 	UpdateTs: time.Now().Unix(),
	// }

	// battleInfo.PartyInfos[proposer.Uid] = proposerPartyInfo
	// battleInfo.PartyInfos[target.Uid] = targetPartyInfo

	// // 保存战斗信息
	// tool.GetGlobalBattleMap().Set(matchId, battleInfo)

	// 通知队长加入Match
	if err := tool.SendMatchJoinNotification(ctx, logger, nk, matchId, proposer, target); err != nil {
		logger.Error("发送Match通知失败: %v", err)
		return "", runtime.NewError("发送Match通知失败", 500)
	}

	return matchId, nil
}
