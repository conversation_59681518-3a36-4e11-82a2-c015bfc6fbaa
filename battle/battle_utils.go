package battle

import (
	"context"
	"errors"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)
const (
    ErrTrainerNotFound    = "trainer not found"
    ErrBattleCountError   = "battle count error"
    ErrPayloadError       = "payload error"
    ErrBattleInfoError    = "battle info error"
)
// 辅助函数
func validateTrainer(ctx context.Context) (*MainServer.Trainer, error) {
    trainer := tool.GetActiveTrainerByCtx(ctx)
    if trainer == nil {
        return nil, runtime.NewError(ErrTrainerNotFound, 400)
    }
    if !LimitCallRate(trainer.Id) {
        return nil, runtime.NewError(ErrBattleCountError, 400)
    }
    return trainer, nil
}

func parseBattlePrepare(payload string) (*MainServer.BattlePrepare, error) {
    prepareInfo := &MainServer.BattlePrepare{}
    if err := tool.Base64ToProto(payload, prepareInfo); err != nil {
        return nil, runtime.NewError(ErrPayloadError, 400)
    }
    if prepareInfo.BattleMatchMaker == nil {
        return nil, runtime.NewError(ErrBattleInfoError, 400)
    }
    return prepareInfo, nil
}

// 添加超时控制的channel操作
func receiveMessageWithTimeout(ch chan runtime.MatchData, timeout time.Duration) (*runtime.MatchData, error) {
    select {
    case msg := <-ch:
        return &msg, nil
    case <-time.After(timeout):
        return nil, errors.New("receive message timeout")
    }
}