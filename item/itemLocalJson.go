package item

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
)

// 定义 localItems 的数据结构
var localItems = make(map[string]map[string]LocalItem)

// Item 结构体，表示一个物品的属性
type LocalItem struct {
	Name string `json:"name"` // 物品名称
	Cost int32  `json:"cost"` // 物品成本
	Type int32  `json:"type"`
}
func IsPokeBall(item LocalItem) bool {
	return item.Type == 2
}
// LoadLocalItems 负责加载和解析 items.json 数据
func LoadLocalItems() {
	// 从文件读取 JSON 数据
	jsonFile, err := os.ReadFile("/nakama/data/items.json")
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
	}

	// 解析 JSON 数据并存储到全局变量中
	err = parse(string(jsonFile))
	if err != nil {
		log.Fatalf("Error parsing JSON: %v", err)
	}

	fmt.Printf("Loaded items for %d categories\n", len(localItems))
}

// parse 函数负责解析 JSON 数据
func parse(data string) error {
	// 定义临时数据结构，用于解析 items.json
	var rawItems map[string]map[string]struct {
		Cost int32 `json:"cost"`
		Type int32 `json:"type"`
	}
	if err := json.Unmarshal([]byte(data), &rawItems); err != nil {
		return fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	// 将解析后的数据转换为 localItems 的结构
	for category, items := range rawItems {
		// 初始化 category map
		localItems[category] = make(map[string]LocalItem)

		for itemName, item := range items {
			localItems[category][itemName] = LocalItem{
				Name: itemName,
				Cost: item.Cost,
				Type: item.Type,
			}
		}
	}

	return nil
}

// 获取指定分类下的所有物品
func GetItemsByKey(categoryKey string) (map[string]LocalItem, bool) {
	items, exists := localItems[categoryKey]
	return items, exists
}

// 根据物品名称全局查找物品
func GetItemByName(itemName string) (*LocalItem, bool) {
	// 首先遍历除了 "7" 键的所有项 //这边后续可以改成用carry类型进行判断
	for key, items := range localItems {
		if key != "7" {
			if item, exists := items[itemName]; exists {
				return &item, true
			}
		}
	}

	// 如果没有找到，再检查 "7" 键的项
	if items, exists := localItems["7"]; exists {
		if item, exists := items[itemName]; exists {
			return &item, true
		}
	}

	// 如果没有找到，返回 nil 和 false
	return nil, false
}

// 根据分类和物品名称获取物品
func GetItemByKeyAndName(categoryKey, itemName string) (*LocalItem, bool) {
	items, exists := GetItemsByKey(categoryKey)
	if !exists {
		return nil, false
	}

	item, exists := items[itemName]
	if !exists {
		return nil, false
	}

	return &item, true
}
