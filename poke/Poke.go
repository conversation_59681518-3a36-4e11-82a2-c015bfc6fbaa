package poke

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/proto/MainServer"
	"math/rand"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TablePokesName = "pokes"

func InitPokes(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createPokesTableIfNotExists(ctx, logger, db)
	LoadLearnsets()
	LoadGrowthData()
	LoadPokemonInfos()
	LoadRegionMap()
}
func SearchPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, collectionName string, filter *MainServer.PokeFilter, targetUserId string) (string, error) {
	// var pokeStat MainServer.PokeStat
	// var filter MainServer.MarketPokeFilter
	// if err := json.Unmarshal([]byte(payload), &filter); err != nil {
	// 	logger.Error("解析道具 payload 失败: %v", err) // 记录解析失败
	// 	return "", runtime.NewError("无效的请求数据", 400)
	// }
	// itemPayload.Quantity = 1
	// return tryUseItem(ctx, logger, db, nk, itemPayload)
	return "", nil
}

// func GetPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, pokeId string) {

// }

//	func GetPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, update_ts double) {
//		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
//		//获取
//	}
func TestCreateRandomPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 初始化随机数种子
	rand.Seed(time.Now().UnixMilli())

	// 生成测试数据
	for i := 0; i < 10; i++ { // 创建 10 个宝可梦
		poke := createRandomPoke(i)
		err := insertPokeData(ctx, db, &poke)
		if err != nil {
			return "", fmt.Errorf("failed to save poke %d: %w", poke.Id, err)
		}
	}

	return "Pokes saved successfully", nil
}

// 创建表格并建立索引，如果表不存在
func createPokesTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	tableName := TablePokesName
	// 定义 SQL 语句，创建表格
	createTableSQL := fmt.Sprintf(`
    CREATE TABLE IF NOT EXISTS %s (
        id BIGSERIAL PRIMARY KEY,            -- id 改为自增长的整数
        tid BIGINT NOT NULL,                 -- 存储 tid (User) 信息, 不可为空
        name VARCHAR(30) NOT NULL,           -- 宝可梦名字, 不可为空
        nick_name VARCHAR(20),               -- 宝可梦昵称
        ball_name VARCHAR(20),               -- 捕获使用的球
        item_name VARCHAR(20) NOT NULL,      -- 携带的道具, 不可为空
        ability VARCHAR(50) NOT NULL,        -- 特性
        evs JSONB NOT NULL,                  -- evs 存储为 NOT NULL DEFAULT '{}'::jsonb, 不可为空
        ivs JSONB NOT NULL,                  -- ivs 存储为 NOT NULL DEFAULT '{}'::jsonb, 不可为空
        sale BOOLEAN NOT NULL,               -- 是否在售, 不可为空
        sale_info JSONB NOT NULL DEFAULT '{}'::jsonb, -- 售价信息
        status JSONB NOT NULL DEFAULT '{}'::jsonb, -- 宝可梦状态信息
        borrow_info JSONB NOT NULL DEFAULT '{}'::jsonb, -- 借用信息
        moves JSONB NOT NULL,                -- 招式，使用 JSONB 存储 moves 信息, 不可为空
        level INT NOT NULL,                  -- 宝可梦等级, 不可为空
        nature VARCHAR(20) NOT NULL,         -- 宝可梦性格, 不可为空
        experience BIGINT NOT NULL,          -- 宝可梦经验值, 不可为空
        born JSONB NOT NULL,                 -- 出生信息 BornInfo, 不可为空
        egg BOOLEAN NOT NULL DEFAULT false,  -- 是否为蛋状态, 不可为空，默认 false
        shiny INT NOT NULL DEFAULT 0,        -- 异色状态，使用 INT 类型，默认值为 0（非异色）
        gender VARCHAR(5) NOT NULL,          -- 性别字段
        hp_sub INT NOT NULL DEFAULT 0,       -- 新增字段：hp 剩余值
        happiness INT NOT NULL DEFAULT 0,    -- 新增字段：亲密度
        sys_extra JSONB NOT NULL DEFAULT '{}'::jsonb, -- 系统额外信息
        extra JSONB NOT NULL DEFAULT '{}'::jsonb, -- 额外信息 extra 字段，使用 JSONB 存储
        release BOOLEAN NOT NULL DEFAULT false, -- 是否已释放
        honor_info JSONB NOT NULL DEFAULT '{}'::jsonb, -- 荣誉信息
        breed_count INT NOT NULL DEFAULT 0,  -- 繁殖次数，影响死亡概率
        create_ts BIGINT NOT NULL,           -- 创建时间戳, 不可为空
        update_ts BIGINT NOT NULL            -- 更新时间戳, 不可为空
    );`, tableName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("failed to create table %s: %w", tableName, err)
		return fmt.Errorf("failed to create table %s: %w", tableName, err)
	}

	// 为 id, tid.id, name, sale, price, evs, ivs, ability 创建索引
	createIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_%s_tid ON %s (tid);",
		"CREATE INDEX IF NOT EXISTS idx_%s_name ON %s (name);",
		"CREATE INDEX IF NOT EXISTS idx_%s_sale ON %s (sale);",
		"CREATE INDEX IF NOT EXISTS idx_%s_sale_info_price ON %s (((sale_info->>'price')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_sale_info_special_coin ON %s (((sale_info->>'special_coin')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_update_ts ON %s (update_ts);",
		"CREATE INDEX IF NOT EXISTS idx_%s_level ON %s (level);",
		"CREATE INDEX IF NOT EXISTS idx_%s_nature ON %s (nature);",
		"CREATE INDEX IF NOT EXISTS idx_%s_gender ON %s (gender);",
		"CREATE INDEX IF NOT EXISTS idx_%s_ability ON %s (ability);",
		"CREATE INDEX IF NOT EXISTS idx_%s_release ON %s (release);",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_hp ON %s (((evs->>'hp')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_atk ON %s (((evs->>'atk')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_def ON %s (((evs->>'def')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_spa ON %s (((evs->>'spa')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_spd ON %s (((evs->>'spd')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_evs_spe ON %s (((evs->>'spe')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_hp ON %s (((ivs->>'hp')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_atk ON %s (((ivs->>'atk')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_def ON %s (((ivs->>'def')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_spa ON %s (((ivs->>'spa')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_spd ON %s (((ivs->>'spd')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_ivs_spe ON %s (((ivs->>'spe')::int));",
		"CREATE INDEX IF NOT EXISTS idx_%s_status_is_only ON %s (((status->>'is_only')::boolean));",
		"CREATE INDEX IF NOT EXISTS idx_%s_status_sealed ON %s ((status->>'sealed'));",
	}

	var createIndexSQL string

	// 遍历数组，每一条索引语句都会被格式化为带有 tableName 的 SQL 语句
	for _, indexStmt := range createIndexes {
		createIndexSQL += fmt.Sprintf(indexStmt, tableName, tableName) + "\n"
	}

	_, err = db.ExecContext(ctx, createIndexSQL)
	if err != nil {
		logger.Error("failed to create index on table %s: %w", tableName, err)
		return fmt.Errorf("failed to create index on table %s: %w", tableName, err)
	}

	logger.Info("createPokesTableIfNotExists 成功")
	return nil
}

// UpdatePokeData 封装了 updatePoke 函数，可以在事务和非事务上下文中运行
func UpdatePokeData(ctx context.Context, dbOrTx interface{}, poke *MainServer.Poke) error {
	return updatePoke(ctx, dbOrTx, TablePokesName, poke)
}
func insertPokeData(ctx context.Context, dbOrTx interface{}, poke *MainServer.Poke) error {
	return insertPoke(ctx, dbOrTx, TablePokesName, poke)
}

// 序列化复杂字段的辅助函数
func serializePokeFields(poke *MainServer.Poke) (map[string]interface{}, error) {
	fields := make(map[string]interface{})
	var err error

	// 序列化各个字段
	fields["tid"] = poke.Tid // tid 现在是 int64 类型，不需要序列化

	if fields["evs"], err = json.Marshal(poke.Evs); err != nil {
		return nil, fmt.Errorf("failed to serialize evs: %w", err)
	}
	if fields["ivs"], err = json.Marshal(poke.Ivs); err != nil {
		return nil, fmt.Errorf("failed to serialize ivs: %w", err)
	}
	if fields["sale_info"], err = json.Marshal(poke.SaleInfo); err != nil {
		return nil, fmt.Errorf("failed to serialize sale_info: %w", err)
	}
	if fields["status"], err = json.Marshal(poke.Status); err != nil {
		return nil, fmt.Errorf("failed to serialize status: %w", err)
	}
	if fields["borrow_info"], err = json.Marshal(poke.BorrowInfo); err != nil {
		return nil, fmt.Errorf("failed to serialize borrow_info: %w", err)
	}
	if fields["moves"], err = json.Marshal(poke.Moves); err != nil {
		return nil, fmt.Errorf("failed to serialize moves: %w", err)
	}
	if fields["born"], err = json.Marshal(poke.Born); err != nil {
		return nil, fmt.Errorf("failed to serialize born: %w", err)
	}
	if fields["sys_extra"], err = json.Marshal(poke.SysExtra); err != nil {
		return nil, fmt.Errorf("failed to serialize sys_extra: %w", err)
	}
	if fields["extra"], err = json.Marshal(poke.Extra); err != nil {
		return nil, fmt.Errorf("failed to serialize extra: %w", err)
	}
	if fields["honor_info"], err = json.Marshal(poke.HonorInfo); err != nil {
		return nil, fmt.Errorf("failed to serialize honor_info: %w", err)
	}

	return fields, nil
}
func insertPoke(ctx context.Context, dbOrTx interface{}, tableName string, poke *MainServer.Poke) error {
	poke.CreateTs = time.Now().UnixMilli()
	if len(poke.Moves) > 4 {
		return fmt.Errorf("move count can not be more than 4")
	}
	// 补全 moves
	if len(poke.Moves) < 4 {
		poke.Moves = append(poke.Moves, make([]*MainServer.PokeSimpleMove, 4-len(poke.Moves))...)
	}
	for i, move := range poke.Moves {
		if move == nil {
			poke.Moves[i] = &MainServer.PokeSimpleMove{}
		}
	}
	// 序列化复杂字段
	fields, err := serializePokeFields(poke)
	if err != nil {
		return fmt.Errorf("failed to serialize poke fields: %w", err)
	}

	// 插入 SQL 语句
	sqlStmt := fmt.Sprintf(`
        INSERT INTO %s (tid, name, nick_name, ball_name, item_name, ability, evs, ivs, sale, sale_info, status, borrow_info, moves, level, nature, experience, born, egg, shiny, gender, hp_sub, happiness, sys_extra, extra, release, honor_info, breed_count, create_ts, update_ts)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29)
        ON CONFLICT (id) DO UPDATE
        SET tid = EXCLUDED.tid,
            name = EXCLUDED.name,
            nick_name = EXCLUDED.nick_name,
            ball_name = EXCLUDED.ball_name,
            item_name = EXCLUDED.item_name,
            ability = EXCLUDED.ability,
            evs = EXCLUDED.evs,
            ivs = EXCLUDED.ivs,
            sale = EXCLUDED.sale,
            sale_info = EXCLUDED.sale_info,
            stats = EXCLUDED.stats,
            moves = EXCLUDED.moves,
            level = EXCLUDED.level,
            nature = EXCLUDED.nature,
            status = EXCLUDED.status,
            experience = EXCLUDED.experience,
            born = EXCLUDED.born,
            egg = EXCLUDED.egg,
            shiny = EXCLUDED.shiny,
            gender = EXCLUDED.gender,
            hp_sub = EXCLUDED.hp_sub,
            happiness = EXCLUDED.happiness,
            sys_extra = EXCLUDED.sys_extra,
            extra = EXCLUDED.extra,
            release = EXCLUDED.release,
            honor_info = EXCLUDED.honor_info,
			breed_count = EXCLUDED.breed_count,
            create_ts = EXCLUDED.create_ts,
            update_ts = EXCLUDED.update_ts
			 RETURNING id
    `, tableName)

	return executeSQL(ctx, dbOrTx, sqlStmt, fields, poke, true)
}

func updatePoke(ctx context.Context, dbOrTx interface{}, tableName string, poke *MainServer.Poke) error {
	// 序列化复杂字段
	fields, err := serializePokeFields(poke)
	if err != nil {
		return fmt.Errorf("failed to serialize poke fields: %w", err)
	}

	// 更新 SQL 语句
	sqlStmt := fmt.Sprintf(`
        UPDATE %s
        SET tid = $1, name = $2, nick_name = $3, ball_name = $4, item_name = $5, ability = $6, evs = $7, ivs = $8, sale = $9, sale_info = $10, status = $11, borrow_info = $12, moves = $13, level = $14, nature = $15, experience = $16, born = $17, egg = $18, shiny = $19, gender = $20, hp_sub = $21, happiness = $22, sys_extra = $23, extra = $24, release = $25, honor_info = $26, breed_count = $27, create_ts = $28, update_ts = $29
        WHERE id = $30 AND update_ts = $31;
    `, tableName)

	return executeSQL(ctx, dbOrTx, sqlStmt, fields, poke, false)
}

func executeSQL(ctx context.Context, dbOrTx interface{}, sqlStmt string, fields map[string]interface{}, poke *MainServer.Poke, isInsert bool) error {
	var args []interface{}
	updateTs := time.Now().UnixMilli()
	args = append(args,
		fields["tid"], poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability,
		fields["evs"], fields["ivs"], poke.Sale, fields["sale_info"],
		fields["status"], fields["borrow_info"], fields["moves"], poke.Level, poke.Nature.String(), poke.Experience,
		fields["born"], poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness,
		fields["sys_extra"], fields["extra"], poke.Release, fields["honor_info"], poke.BreedCount, poke.CreateTs, updateTs,
	)

	// 插入场景不需要 ID 和 UpdateTs
	if !isInsert {
		args = append(args, poke.Id, poke.UpdateTs)
	}

	var err error
	if isInsert {
		// 插入场景：需要获取自增 ID
		var lastInsertId int64
		switch tx := dbOrTx.(type) {
		case *sql.DB:
			err = tx.QueryRowContext(ctx, sqlStmt, args...).Scan(&lastInsertId)
		case *sql.Tx:
			err = tx.QueryRowContext(ctx, sqlStmt, args...).Scan(&lastInsertId)
		default:
			return fmt.Errorf("unsupported dbOrTx type: %T", dbOrTx)
		}

		if err != nil {
			return fmt.Errorf("failed to execute SQL statement: %w", err)
		}
		poke.Id = lastInsertId // 设置到 poke.Id
	} else {
		// 更新场景：使用 Exec 执行 SQL
		var res sql.Result
		switch tx := dbOrTx.(type) {
		case *sql.DB:
			res, err = tx.ExecContext(ctx, sqlStmt, args...)
		case *sql.Tx:
			res, err = tx.ExecContext(ctx, sqlStmt, args...)
		default:
			return fmt.Errorf("unsupported dbOrTx type: %T", dbOrTx)
		}

		if err != nil {
			return fmt.Errorf("failed to execute SQL statement: %w", err)
		}

		// 检查影响行数
		rowsAffected, err := res.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to check rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("update failed for poke %d: version conflict", poke.Id)
		}
	}

	poke.UpdateTs = updateTs
	return nil
}

// 保存或更新宝可梦数据
// func saveOrUpdatePoke(ctx context.Context, dbOrTx interface{}, tableName string, poke *MainServer.Poke, isInsert bool) error {
// 	// 序列化复杂字段
// 	fields, err := serializePokeFields(poke)
// 	if err != nil {
// 		return err
// 	}

// 	var sqlStmt string
// 	if isInsert {
// 		// 插入 SQL 语句
// 		sqlStmt = fmt.Sprintf(`
//             INSERT INTO %s (tid, name, nick_name, ball_name, item_name, ability, evs, ivs, sale, price, stats, moves, level, nature, status, experience, born, egg, shiny, gender, hp_sub, happiness, sys_extra, extra, create_ts, update_ts)
//             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26)
//             ON CONFLICT (id) DO UPDATE
//             SET tid = EXCLUDED.tid,
//                 name = EXCLUDED.name,
//                 nick_name = EXCLUDED.nick_name,
//                 ball_name = EXCLUDED.ball_name,
//                 item_name = EXCLUDED.item_name,
//                 ability = EXCLUDED.ability,
//                 evs = EXCLUDED.evs,
//                 ivs = EXCLUDED.ivs,
//                 sale = EXCLUDED.sale,
//                 price = EXCLUDED.price,
//                 stats = EXCLUDED.stats,
//                 moves = EXCLUDED.moves,
//                 level = EXCLUDED.level,
//                 nature = EXCLUDED.nature,
//                 status = EXCLUDED.status,
//                 experience = EXCLUDED.experience,
//                 born = EXCLUDED.born,
//                 egg = EXCLUDED.egg,
//                 shiny = EXCLUDED.shiny,
//                 gender = EXCLUDED.gender,
//                 hp_sub = EXCLUDED.hp_sub,
//                 happiness = EXCLUDED.happiness,
//                 sys_extra = EXCLUDED.sys_extra,
//                 extra = EXCLUDED.extra,
//                 create_ts = EXCLUDED.create_ts,
//                 update_ts = EXCLUDED.update_ts;
//         `, tableName)
// 	} else {
// 		// 更新 SQL 语句
// 		sqlStmt = fmt.Sprintf(`
//             UPDATE %s
//             SET tid = $1, name = $2, nick_name = $3, ball_name = $4, item_name = $5, ability = $6, evs = $7, ivs = $8, sale = $9, price = $10, stats = $11, moves = $12, level = $13, nature = $14, status = $15, experience = $16, born = $17, egg = $18, shiny = $19, gender = $20, hp_sub = $21, happiness = $22, sys_extra = $23, extra = $24, create_ts = $25, update_ts = $26
//             WHERE id = $27 AND update_ts = $28;
//         `, tableName)
// 	}

// 	// 执行 SQL 语句
// 	var res sql.Result
// 	switch tx := dbOrTx.(type) {
// 	case *sql.DB:
// 		res, err = tx.ExecContext(ctx, sqlStmt, fields["tid"], poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability, fields["evs"], fields["ivs"], poke.Sale, poke.Price, fields["stats"], fields["moves"], poke.Level, poke.Nature, poke.Status, poke.Experience, fields["born"], poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness, fields["sys_extra"], fields["extra"], poke.CreateTs, time.Now().UnixMilli(), poke.Id, poke.UpdateTs)
// 	case *sql.Tx:
// 		res, err = tx.ExecContext(ctx, sqlStmt, fields["tid"], poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability, fields["evs"], fields["ivs"], poke.Sale, poke.Price, fields["stats"], fields["moves"], poke.Level, poke.Nature, poke.Status, poke.Experience, fields["born"], poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness, fields["sys_extra"], fields["extra"], poke.CreateTs, time.Now().UnixMilli(), poke.Id, poke.UpdateTs)
// 	default:
// 		return fmt.Errorf("unsupported dbOrTx type: %T", dbOrTx)
// 	}

// 	if err != nil {
// 		return fmt.Errorf("failed to execute SQL statement: %w", err)
// 	}

// 	// 检查影响行数
// 	rowsAffected, err := res.RowsAffected()
// 	if err != nil {
// 		return fmt.Errorf("failed to check rows affected: %w", err)
// 	}

// 	if !isInsert && rowsAffected == 0 {
// 		return fmt.Errorf("update failed for poke %d: version conflict", poke.Id)
// 	}

// 	return nil
// }

// // 将宝可梦数据存储到数据库表中（插入新记录或更新已有记录）
// func savePokeToTable(ctx context.Context, db *sql.DB, tableName string, poke *MainServer.Poke) error {
// 	// 序列化复杂字段
// 	tid, err := json.Marshal(poke.Tid)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize tid: %w", err)
// 	}

// 	evsJSON, err := json.Marshal(poke.Evs)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize evs: %w", err)
// 	}

// 	ivsJSON, err := json.Marshal(poke.Ivs)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize ivs: %w", err)
// 	}

// 	statsJSON, err := json.Marshal(poke.Stats)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize stats: %w", err)
// 	}

// 	movesJSON, err := json.Marshal(poke.Moves)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize moves: %w", err)
// 	}

// 	bornJSON, err := json.Marshal(poke.Born)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize born info: %w", err)
// 	}

// 	extraJSON, err := json.Marshal(poke.Extra)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize extra: %w", err)
// 	}

// 	sysExtraJSON, err := json.Marshal(poke.SysExtra)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize sys_extra: %w", err)
// 	}

// 	// 插入数据到表格中，使用 ON CONFLICT 来避免重复主键错误
// 	insertSQL := fmt.Sprintf(`
//     INSERT INTO %s (tid, name, nick_name, ball_name, item_name, ability, evs, ivs, sale, price, stats, moves, level, nature, status, experience, born, egg, shiny, gender, hp_sub, happiness, sys_extra, extra, create_ts, update_ts)
//     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26)
//     ON CONFLICT (id) DO UPDATE
//     SET tid = EXCLUDED.tid,
//         name = EXCLUDED.name,
//         nick_name = EXCLUDED.nick_name,
//         ball_name = EXCLUDED.ball_name,
//         item_name = EXCLUDED.item_name,
//         ability = EXCLUDED.ability,
//         evs = EXCLUDED.evs,
//         ivs = EXCLUDED.ivs,
//         sale = EXCLUDED.sale,
//         price = EXCLUDED.price,
//         stats = EXCLUDED.stats,
//         moves = EXCLUDED.moves,
//         level = EXCLUDED.level,
//         nature = EXCLUDED.nature,
//         status = EXCLUDED.status,
//         experience = EXCLUDED.experience,
//         born = EXCLUDED.born,
//         egg = EXCLUDED.egg,
//         shiny = EXCLUDED.shiny,
//         gender = EXCLUDED.gender,
//         hp_sub = EXCLUDED.hp_sub,           -- 新增字段
//         happiness = EXCLUDED.happiness,     -- 新增字段
//         sys_extra = EXCLUDED.sys_extra,     -- 新增字段
//         extra = EXCLUDED.extra,
//         create_ts = EXCLUDED.create_ts,
//         update_ts = EXCLUDED.update_ts;
//     `, tableName)

// 	res, err := db.ExecContext(ctx, insertSQL, tid, poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability, evsJSON, ivsJSON, poke.Sale, poke.Price, statsJSON, movesJSON, poke.Level, poke.Nature, poke.Status, poke.Experience, bornJSON, poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness, sysExtraJSON, extraJSON, poke.CreateTs, poke.UpdateTs)
// 	if err != nil {
// 		return fmt.Errorf("failed to save poke to table %s: %w", tableName, err)
// 	}

// 	rowsAffected, err := res.RowsAffected()
// 	if err != nil {
// 		return fmt.Errorf("failed to check rows affected: %w", err)
// 	}

// 	if rowsAffected == 0 {
// 		return fmt.Errorf("failed to update poke %d: version conflict", poke.Id)
// 	}

// 	return nil
// }
// // 更新宝可梦数据，支持事务和非事务调用，带有乐观锁控制
// func updatePoke(ctx context.Context, dbOrTx interface{}, tableName string, poke *MainServer.Poke) error {
// 	// 准备更新的 SQL 语句，带有乐观锁检查（基于 update_ts）
// 	updateSQL := fmt.Sprintf(`
// 		UPDATE %s
// 		SET tid = $1, name = $2, nick_name = $3, ball_name = $4, item_name = $5, ability = $6, evs = $7, ivs = $8, sale = $9, price = $10, stats = $11, moves = $12, level = $13, nature = $14, status = $15, experience = $16, born = $17, egg = $18, shiny = $19, gender = $20, hp_sub = $21, happiness = $22, sys_extra = $23, extra = $24, create_ts = $25, update_ts = $26
// 		WHERE id = $27 AND update_ts = $28 -- 乐观锁检查
// 	`, tableName)

// 	// 序列化复杂的字段
// 	evsJSON, err := json.Marshal(poke.Evs)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize evs: %w", err)
// 	}
// 	ivsJSON, err := json.Marshal(poke.Ivs)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize ivs: %w", err)
// 	}
// 	statsJSON, err := json.Marshal(poke.Stats)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize stats: %w", err)
// 	}
// 	movesJSON, err := json.Marshal(poke.Moves)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize moves: %w", err)
// 	}
// 	bornJSON, err := json.Marshal(poke.Born)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize born info: %w", err)
// 	}
// 	sysExtraJSON, err := json.Marshal(poke.SysExtra)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize sys_extra: %w", err)
// 	}
// 	extraJSON, err := json.Marshal(poke.Extra)
// 	if err != nil {
// 		return fmt.Errorf("failed to serialize extra: %w", err)
// 	}

// 	// 执行更新语句
// 	var res sql.Result
// 	switch tx := dbOrTx.(type) {
// 	case *sql.DB:
// 		res, err = tx.ExecContext(ctx, updateSQL, poke.Tid, poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability, evsJSON, ivsJSON, poke.Sale, poke.Price, statsJSON, movesJSON, poke.Level, poke.Nature, poke.Status, poke.Experience, bornJSON, poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness, sysExtraJSON, extraJSON, poke.CreateTs, time.Now().UnixMilli(), poke.Id, poke.UpdateTs)
// 	case *sql.Tx:
// 		res, err = tx.ExecContext(ctx, updateSQL, poke.Tid, poke.Name, poke.NickName, poke.BallName, poke.ItemName, poke.Ability, evsJSON, ivsJSON, poke.Sale, poke.Price, statsJSON, movesJSON, poke.Level, poke.Nature, poke.Status, poke.Experience, bornJSON, poke.Egg, poke.Shiny, poke.Gender, poke.HpSub, poke.Happiness, sysExtraJSON, extraJSON, poke.CreateTs, time.Now().UnixMilli(), poke.Id, poke.UpdateTs)
// 	default:
// 		return fmt.Errorf("unsupported dbOrTx type: %T", dbOrTx)
// 	}

// 	if err != nil {
// 		return fmt.Errorf("failed to update poke %d: %w", poke.Id, err)
// 	}

// 	// 检查是否有行被更新（如果没有，说明版本冲突）
// 	rowsAffected, err := res.RowsAffected()
// 	if err != nil {
// 		return fmt.Errorf("failed to check rows affected: %w", err)
// 	}
// 	if rowsAffected == 0 {
// 		return fmt.Errorf("update failed for poke %d: version conflict", poke.Id)
// 	}

// 	return nil
// }

// 辅助函数，用于创建随机宝可梦
// RemoveItemFromPoke 从宝可梦中移除物品
// 参数：
// - ctx: 上下文
// - tx: 数据库事务
// - pokemon: 宝可梦对象
// - destroy: 是否销毁物品（true：销毁物品，false：将物品添加到库存）
// - trainerId: 训练师ID（当destroy=false时需要）
// 返回：
// - error: 错误信息
func RemoveItemFromPoke(ctx context.Context, tx *sql.Tx, pokemon *MainServer.Poke, destroy bool, trainerId int64) error {
	// 如果宝可梦没有携带物品，直接返回
	if pokemon.ItemName == "" {
		return nil
	}

	// 保存当前物品名称
	itemName := pokemon.ItemName

	// 如果不销毁物品，则添加到库存
	if !destroy {
		// 添加物品到库存
		err := inventory.AddItem(ctx, tx, trainerId, itemName, int32(1))
		if err != nil {
			return fmt.Errorf("添加物品到库存失败: %w", err)
		}
	}

	// 清空宝可梦的当前物品
	pokemon.ItemName = ""

	// 更新宝可梦数据
	err := UpdatePokeData(ctx, tx, pokemon)
	if err != nil {
		return fmt.Errorf("更新宝可梦数据失败: %w", err)
	}

	return nil
}

// AddItemToPoke 给宝可梦添加物品
// 参数：
// - ctx: 上下文
// - tx: 数据库事务
// - pokemon: 宝可梦对象
// - itemName: 物品名称
// - trainerId: 训练师ID
// 返回：
// - error: 错误信息
func AddItemToPoke(ctx context.Context, tx *sql.Tx, pokemon *MainServer.Poke, itemName string, trainerId int64) error {
	// 如果宝可梦已经有物品，先移除
	if pokemon.ItemName != "" {
		err := RemoveItemFromPoke(ctx, tx, pokemon, false, trainerId)
		if err != nil {
			return fmt.Errorf("移除宝可梦当前物品失败: %w", err)
		}
	}

	// 从库存中使用物品
	err := inventory.UseItem(ctx, tx, trainerId, itemName, 1)
	if err != nil {
		return fmt.Errorf("使用物品失败: %w", err)
	}

	// 设置宝可梦的物品
	pokemon.ItemName = itemName

	// 更新宝可梦数据
	err = UpdatePokeData(ctx, tx, pokemon)
	if err != nil {
		return fmt.Errorf("更新宝可梦数据失败: %w", err)
	}

	return nil
}

func createRandomPoke(index int) MainServer.Poke {
	names := []string{"Pikachu", "Charmander", "Bulbasaur", "Squirtle", "Jigglypuff", "Gengar", "Eevee", "Snorlax", "Mewtwo", "Charizard"}
	moves := []MainServer.PokeSimpleMove{
		{Name: "Thunderbolt", PpPro: rand.Int31n(40), PpSub: 0},
		{Name: "Flamethrower", PpPro: rand.Int31n(40), PpSub: 0},
		{Name: "Hydro Pump", PpPro: rand.Int31n(40), PpSub: 0},
	}
	natures := []MainServer.Nature{ // 使用枚举类型
		MainServer.Nature_ADAMANT,
		MainServer.Nature_BASHFUL,
		MainServer.Nature_BOLD,
		MainServer.Nature_BRAVE,
		MainServer.Nature_CALM,
		MainServer.Nature_CAREFUL,
		MainServer.Nature_DOCILE,
		MainServer.Nature_GENTLE,
		MainServer.Nature_HARDY,
		MainServer.Nature_HASTY,
		MainServer.Nature_IMPISH,
		MainServer.Nature_JOLLY,
		MainServer.Nature_LAX,
		MainServer.Nature_LONELY,
		MainServer.Nature_MILD,
		MainServer.Nature_MODEST,
		MainServer.Nature_NAIVE,
		MainServer.Nature_NAUGHTY,
		MainServer.Nature_QUIET,
		MainServer.Nature_QUIRKY,
		MainServer.Nature_RASH,
		MainServer.Nature_RELAXED,
		MainServer.Nature_SASSY,
		MainServer.Nature_SERIOUS,
		MainServer.Nature_TIMID,
	}
	var pokeMoves []*MainServer.PokeSimpleMove
	for i := range moves[:rand.Intn(len(moves))] {
		pokeMoves = append(pokeMoves, &moves[i])
	}

	return MainServer.Poke{
		// Id:         int64(index),                                              // 使用 index 作为 ID
		Tid:      rand.Int63n(100),             // 随机生成 TrainerId 信息
		Name:     names[rand.Intn(len(names))], // 随机选择宝可梦名字
		NickName: "",
		BallName: "ball",
		ItemName: "", // 默认空 item
		// Ability:  rand.Int31n(3), // 随机选择特性数量
		Evs: &MainServer.PokeStat{ // 随机生成 EVs 数据
			Hp:  rand.Int31n(256),
			Atk: rand.Int31n(256),
			Def: rand.Int31n(256),
			Spa: rand.Int31n(256),
			Spd: rand.Int31n(256),
			Spe: rand.Int31n(256),
		},
		Ivs: &MainServer.PokeStat{ // 随机生成 IVs 数据
			Hp:  rand.Int31n(32),
			Atk: rand.Int31n(32),
			Def: rand.Int31n(32),
			Spa: rand.Int31n(32),
			Spd: rand.Int31n(32),
			Spe: rand.Int31n(32),
		},
		Sale: rand.Intn(2) == 1, // 随机生成 Sale 状态
		SaleInfo: &MainServer.SaleInfo{
			Price:       int32(rand.Float64() * 100), // 随机生成价格
			SpecialCoin: int32(rand.Float64() * 10),  // 随机生成特殊币
		},
		HpSub:      rand.Int31n(22),
		Moves:      pokeMoves,                        // 随机分配 Moves
		Level:      rand.Int31n(100),                 // 随机生成等级
		Nature:     natures[rand.Intn(len(natures))], // 随机选择Nature枚举值
		Status:     &MainServer.PokeStatusInfo{},     // 默认状态信息
		BorrowInfo: &MainServer.PokeBorrowInfo{},     // 默认借用信息
		Experience: rand.Int63n(10000),               // 随机生成经验值
		Born:       &MainServer.BornInfo{},           // 默认出生信息
		Egg:        rand.Intn(2) == 1,                // 随机生成 egg 状态
		Shiny:      int32(rand.Intn(2)),              // 随机生成 shiny 状态，0 = 非异色，1 = 异色
		Gender:     "",                               // 随机生成 gender 状态
		SysExtra:   &MainServer.PokeSysExtra{},
		Happiness:  33,
		BreedCount: 0,                       // 默认繁殖次数为0
		Extra:      &MainServer.PokeExtra{}, // 默认空 Extra 信息
		Release:    false,                   // 默认未释放
		HonorInfo:  &MainServer.HonorInfo{}, // 默认空荣誉信息
		CreateTs:   time.Now().UnixMilli(),
		UpdateTs:   time.Now().UnixMilli(),
	}
}
