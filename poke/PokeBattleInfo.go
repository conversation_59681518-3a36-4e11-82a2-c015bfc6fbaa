package poke

import (
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strconv"
	"strings"
)

// ConvertPokesToPackedFormat 将 []*MainServer.Poke 转换为 Packed 格式

func ConvertPokesToPackedFormat(pokes []*MainServer.Poke) string {
	var result []string

	for _, poke := range pokes {
		// 获取 Moves 列表
		var moveNames []string
		for _, move := range poke.Moves {
			moveNames = append(moveNames, move.Name)
		}

		// 构造 EVs 字段
		evs := fmt.Sprintf("%d,%d,%d,%d,%d,%d",
			poke.Evs.Hp, poke.Evs.Atk, poke.Evs.Def,
			poke.Evs.Spa, poke.Evs.Spd, poke.Evs.Spe)

		// 构造 IVs 字段
		ivs := fmt.Sprintf("%d,%d,%d,%d,%d,%d",
			poke.Ivs.Hp, poke.Ivs.Atk, poke.Ivs.Def,
			poke.Ivs.Spa, poke.Ivs.Spd, poke.Ivs.Spe)

		// 构造其他字段
		nickName := poke.NickName
		if nickName == "" {
			nickName = poke.Name // 使用宝可梦名称作为默认昵称
		}
		subHpStr := strconv.FormatInt(int64(poke.HpSub), 10)
		tidStr := strconv.FormatInt(poke.Tid, 10)
		idStr := strconv.FormatInt(poke.Id, 10)
		nickName = nickName + "+" + subHpStr + "+" + tidStr + "+" + idStr
		nature := poke.Nature
		if nature == MainServer.Nature_NATURE_UNSPECIFIED {
			nature = MainServer.Nature_BASHFUL
		}
		sysExtra := &MainServer.PokeSysExtra{}
		if poke.SysExtra != nil {
			sysExtra = poke.SysExtra
		}
		shiny := ""
		if poke.Shiny != 0 {
			shiny = "S"
		}
		// 构造一只宝可梦的字符串
		pokeStr := fmt.Sprintf("%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%d|%d,%s,%s,%s,%d",
			nickName,
			poke.Name,
			poke.ItemName,
			poke.Ability,
			strings.Join(moveNames, ","),
			nature,
			evs,
			poke.Gender,
			ivs,
			shiny,
			poke.Level,
			poke.Happiness,
			poke.BallName,
			sysExtra.Terastal,
			"",
			sysExtra.DynamaxLevel)

		// 将宝可梦字符串加入结果列表
		result = append(result, pokeStr)
	}

	// 使用 `]` 分隔不同的宝可梦
	return strings.Join(result, "]")
}

// genderToString 将性别转换为字符串
func genderToString(gender int32) string {
	switch gender {
	case 0:
		return "M" // Male
	case 1:
		return "F" // Female
	default:
		return "" // Genderless
	}
}
