package poke

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strings"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	pokeFields = "id, tid, name, nick_name, ball_name, item_name, ability, evs, ivs, sale, sale_info, stats, moves, level, nature, status, experience, born, egg, shiny, gender, hp_sub, happiness, sys_extra, extra, release, honor_info, create_ts, update_ts"
)

func pokesToBase64(pokes []*MainServer.Poke, logger runtime.Logger) (string, error) {
	content, err := json.Marshal(pokes)
	return string(content), err
	// messages := make([]proto.Message, len(pokes))
	// for i, poke := range pokes {
	// 	messages[i] = poke // poke 已经实现了 proto.Message 接口
	// }
	// return tool.ProtosToJson(messages)
	// return tool.ProtosToBase64(messages, logger)
}

// CheckPokemonsOwnership 检查一组Pokemon是否都属于指定的训练师
// 返回true表示所有Pokemon都属于该训练师，false表示至少有一个Pokemon不属于该训练师
func CheckPokemonsOwnership(ctx context.Context, tx *sql.Tx, pokeIds []int64, tId int64) (bool, error) {
	if len(pokeIds) == 0 {
		return true, nil // 如果没有Pokemon需要检查，则默认返回true
	}

	// 构建IN子句的参数占位符
	placeholders := make([]string, len(pokeIds))
	args := make([]interface{}, len(pokeIds)+1)

	// 第一个参数是tId
	args[0] = tId

	// 构建参数和占位符
	for i, pokeId := range pokeIds {
		placeholders[i] = fmt.Sprintf("$%d", i+2) // 从$2开始，因为$1是tId
		args[i+1] = pokeId
	}

	// 构建查询语句
	query := fmt.Sprintf(
		"SELECT COUNT(*) FROM %s WHERE tid = $1 AND id IN (%s)",
		TablePokesName,
		strings.Join(placeholders, ","),
	)

	// 执行查询
	var count int
	err := tx.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("检查Pokemon所有权失败: %w", err)
	}

	// 如果找到的Pokemon数量等于传入的ID数量，则所有Pokemon都属于该训练师
	return count == len(pokeIds), nil
}

func QueryPokeById(ctx context.Context, executor interface {
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
}, tid int64, pid int64) *MainServer.Poke {
	// 将单个 pid 包装为切片
	pokes, err := QueryPokesByIdsAndUpdateTs(ctx, executor, tid, []int64{pid}, 0)
	if err != nil || len(pokes) == 0 {
		return nil
	}
	return pokes[0] // 返回第一个结果
}

// QueryPokesByIdsAndUpdateTs - 根据多个 PokeId 和 updateTs 查询 Pokes
func QueryPokesByIdsAndUpdateTs(ctx context.Context, executor interface {
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
}, tid int64, pids []int64, updateTs int64) ([]*MainServer.Poke, error) {
	var conditions []string
	var args []interface{}

	// 基础查询语句
	query := fmt.Sprintf("SELECT %s FROM %s", pokeFields, TablePokesName)

	// 动态生成 pids 的占位符
	if len(pids) > 0 {
		placeholders := make([]string, len(pids))
		for i := range pids {
			placeholders[i] = fmt.Sprintf("$%d", len(args)+1)
			args = append(args, pids[i])
		}
		conditions = append(conditions, fmt.Sprintf("id IN (%s)", strings.Join(placeholders, ",")))
	} else {
		return make([]*MainServer.Poke, 0), nil
	}

	// 如果有 uid，将其添加到查询条件中
	// if uid != "" {
	// 	conditions = append(conditions, fmt.Sprintf("trainer->>'id' = $%d", len(args)+1))
	// 	args = append(args, uid)
	// }

	// 如果有 updateTs，将其添加到查询条件中
	if updateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts >= $%d", len(args)+1))
		args = append(args, updateTs)
	}

	if tid > 0 {
		conditions = append(conditions, fmt.Sprintf("tid = $%d", len(args)+1))
		args = append(args, tid)
	}

	// 将条件拼接到查询语句中
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 执行 SQL 查询
	rows, err := executor.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()

	var protoPokes []*MainServer.Poke
	for rows.Next() {
		poke, err := scanPokeRow(rows)
		if err != nil {
			return nil, err
		}
		protoPokes = append(protoPokes, poke)
	}

	return protoPokes, nil
}

// QueryPokesByPokeFilter - 通过 PokeFilter 进行筛选和排序 tid
func queryPokesByPokeFilter(ctx context.Context, executor interface {
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
}, tid int64, filter *MainServer.PokeFilter) ([]*MainServer.Poke, error) {
	query := fmt.Sprintf("SELECT %s FROM pokes WHERE 1=1", pokeFields)
	args := []interface{}{}

	// 根据 uid 进行筛选
	if tid > 0 {
		query += fmt.Sprintf(" AND tid = $%d", len(args)+1)
		args = append(args, tid)
	}

	// 过滤条件
	query, args = applyFilters(query, args, filter)

	// 设置排序
	orderBy := applySorting(filter)
	offset := filter.Page * filter.PageSize
	query += fmt.Sprintf(" %s LIMIT $%d OFFSET $%d", orderBy, len(args)+1, len(args)+2)
	args = append(args, filter.PageSize, offset)

	// 执行 SQL 查询
	rows, err := executor.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()

	var protoPokes []*MainServer.Poke
	for rows.Next() {
		poke, err := scanPokeRow(rows)
		if err != nil {
			return nil, err
		}
		protoPokes = append(protoPokes, poke)
	}
	return protoPokes, nil
}

// applyFilters - 应用筛选条件，并返回更新后的 SQL 查询和参数列表
func applyFilters(query string, args []interface{}, filter *MainServer.PokeFilter) (string, []interface{}) {
	if filter.Sale {
		query += " AND sale = true"
	}

	if len(filter.Names) > 0 {
		placeholders := make([]string, len(filter.Names))
		for i := range filter.Names {
			placeholders[i] = fmt.Sprintf("$%d", len(args)+1)
			args = append(args, filter.Names[i])
		}
		query += fmt.Sprintf(" AND name IN (%s)", strings.Join(placeholders, ","))
	}

	query, args = addStatFilters(query, args, "evs", filter.MinEvs)
	query, args = addStatFilters(query, args, "ivs", filter.MinIvs)

	if filter.MinPrice > 0 {
		query += fmt.Sprintf(" AND CAST(sale_info->>'price' AS INTEGER) >= $%d", len(args)+1)
		args = append(args, filter.MinPrice)
	}
	if filter.MaxPrice > 0 {
		query += fmt.Sprintf(" AND CAST(sale_info->>'price' AS INTEGER) <= $%d", len(args)+1)
		args = append(args, filter.MaxPrice)
	}

	if filter.MinSpecialCoin > 0 {
		query += fmt.Sprintf(" AND CAST(sale_info->>'special_coin' AS INTEGER) >= $%d", len(args)+1)
		args = append(args, filter.MinSpecialCoin)
	}
	if filter.MaxSpecialCoin > 0 {
		query += fmt.Sprintf(" AND CAST(sale_info->>'special_coin' AS INTEGER) <= $%d", len(args)+1)
		args = append(args, filter.MaxSpecialCoin)
	}

	return query, args
}

// addStatFilters - 添加数值筛选条件，并更新 SQL 查询和参数列表
func addStatFilters(query string, args []interface{}, field string, stats *MainServer.PokeStat) (string, []interface{}) {
	if stats == nil {
		stats = &MainServer.PokeStat{}
	}
	if stats.Hp > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'hp' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Hp)
	}
	if stats.Atk > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'atk' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Atk)
	}
	if stats.Def > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'def' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Def)
	}
	if stats.Spa > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'spa' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Spa)
	}
	if stats.Spd > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'spd' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Spd)
	}
	if stats.Spe > 0 {
		query += fmt.Sprintf(" AND CAST(%s->>'spe' AS INTEGER) >= $%d", field, len(args)+1)
		args = append(args, stats.Spe)
	}
	return query, args
}

// applySorting - 应用排序条件
func applySorting(filter *MainServer.PokeFilter) string {
	var orderBy string
	switch filter.Sort {
	case MainServer.PokeFilterSort_LEVEL_DESC:
		orderBy = "ORDER BY level DESC"
	case MainServer.PokeFilterSort_LEVEL_ASC:
		orderBy = "ORDER BY level ASC"
	case MainServer.PokeFilterSort_PRICE_DESC:
		orderBy = "ORDER BY CAST(sale_info->>'price' AS INTEGER) DESC"
	case MainServer.PokeFilterSort_PRICE_ASC:
		orderBy = "ORDER BY CAST(sale_info->>'price' AS INTEGER) ASC"
	case MainServer.PokeFilterSort_UPDATE_TS_DESC:
		orderBy = "ORDER BY update_ts DESC"
	case MainServer.PokeFilterSort_UPDATE_TS_ASC:
		orderBy = "ORDER BY update_ts ASC"
	default:
		// 默认按更新时间降序排序
		orderBy = "ORDER BY update_ts DESC"
	}
	return orderBy
}

// 扫描行并构建 Pokes 对象
func scanPokeRow(rows *sql.Rows) (*MainServer.Poke, error) {
	var (
		id         int64
		tid        int64
		name       string
		nickName   string
		ballName   string
		itemName   string
		ability    string
		evs        []byte // EVs JSON
		ivs        []byte // IVs JSON
		sale       bool
		saleInfo   []byte // SaleInfo JSON
		stats      string
		moves      []byte // Moves JSON
		level      int32
		nature     string
		status     string
		experience int64
		born       []byte // Born info JSON
		egg        bool
		shiny      int32
		gender     string
		hpSub      int32
		happiness  int32
		sysExtra   []byte // SysExtra JSON
		extra      []byte // Extra JSON
		release    bool
		honorInfo  []byte // HonorInfo JSON
		createTs   int64
		updateTs   int64
	)

	// 扫描数据
	if err := rows.Scan(&id, &tid, &name, &nickName, &ballName, &itemName, &ability, &evs, &ivs, &sale, &saleInfo, &stats, &moves, &level, &nature, &status, &experience, &born, &egg, &shiny, &gender, &hpSub, &happiness, &sysExtra, &extra, &release, &honorInfo, &createTs, &updateTs); err != nil {
		return nil, fmt.Errorf("row scan failed: %w", err)
	}
	enumValue, exists := MainServer.Nature_value[nature]
	if !exists {
		enumValue = 2 //MainServer.Nature_BASHFUL
	}
	// 构建 Pokes 对象
	poke := &MainServer.Poke{
		Id:         id,
		Tid:        tid,
		Name:       name,
		NickName:   nickName,
		BallName:   ballName,
		ItemName:   itemName,
		Ability:    ability,
		Sale:       sale,
		Level:      level,
		Nature:     MainServer.Nature(enumValue),
		Status:     status,
		Stats:      stats,
		Experience: experience,
		Egg:        egg,
		Shiny:      shiny,
		Gender:     gender,
		HpSub:      hpSub,
		Happiness:  happiness,
		Release:    release,
		CreateTs:   createTs,
		UpdateTs:   updateTs,
		Evs:        &MainServer.PokeStat{},
		Ivs:        &MainServer.PokeStat{},
		SaleInfo:   &MainServer.SaleInfo{},
		Moves:      []*MainServer.Moves{},
		Born:       &MainServer.BornInfo{},
		SysExtra:   &MainServer.PokeSysExtra{},
		Extra:      &MainServer.PokeExtra{},
		HonorInfo:  &MainServer.HonorInfo{},
	}

	// 反序列化 JSON 数据
	if err := jsonToProto(evs, poke.Evs); err != nil {
		return nil, fmt.Errorf("failed to unmarshal evs: %w", err)
	}
	if err := jsonToProto(ivs, poke.Ivs); err != nil {
		return nil, fmt.Errorf("failed to unmarshal ivs: %w", err)
	}
	if err := jsonToProto(saleInfo, poke.SaleInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal sale_info: %w", err)
	}
	if err := jsonToProto(moves, &poke.Moves); err != nil {
		return nil, fmt.Errorf("failed to unmarshal moves: %w", err)
	}
	if err := jsonToProto(born, poke.Born); err != nil {
		return nil, fmt.Errorf("failed to unmarshal born info: %w", err)
	}
	if err := jsonToProto(sysExtra, poke.SysExtra); err != nil {
		return nil, fmt.Errorf("failed to unmarshal sys_extra: %w", err)
	}
	if err := jsonToProto(extra, poke.Extra); err != nil {
		return nil, fmt.Errorf("failed to unmarshal extra: %w", err)
	}
	if err := jsonToProto(honorInfo, poke.HonorInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal honor_info: %w", err)
	}

	return poke, nil
}

// jsonToProto - 反序列化 JSON 到 Protobuf 对象
func jsonToProto[T any](data []byte, protoObj *T) error {
	if len(data) == 0 {
		return nil
	}
	return json.Unmarshal(data, protoObj)
}
