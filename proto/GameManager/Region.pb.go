// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: GameManager/Region.proto

package GameManager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RPokeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	P    float32 `protobuf:"fixed32,2,opt,name=p,proto3" json:"p,omitempty"`
}

func (x *RPokeItem) Reset() {
	*x = RPokeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameManager_Region_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RPokeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPokeItem) ProtoMessage() {}

func (x *RPokeItem) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPokeItem.ProtoReflect.Descriptor instead.
func (*RPokeItem) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{0}
}

func (x *RPokeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RPokeItem) GetP() float32 {
	if x != nil {
		return x.P
	}
	return 0
}

type RPoke struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	P     float32      `protobuf:"fixed32,2,opt,name=p,proto3" json:"p,omitempty"`       //概率
	Minl  int32        `protobuf:"varint,3,opt,name=minl,proto3" json:"minl,omitempty"`  // 最低等级
	Maxl  int32        `protobuf:"varint,4,opt,name=maxl,proto3" json:"maxl,omitempty"`  // 最高等级
	Items []*RPokeItem `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"` // 宝可梦的物品列表
}

func (x *RPoke) Reset() {
	*x = RPoke{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameManager_Region_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RPoke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPoke) ProtoMessage() {}

func (x *RPoke) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPoke.ProtoReflect.Descriptor instead.
func (*RPoke) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{1}
}

func (x *RPoke) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RPoke) GetP() float32 {
	if x != nil {
		return x.P
	}
	return 0
}

func (x *RPoke) GetMinl() int32 {
	if x != nil {
		return x.Minl
	}
	return 0
}

func (x *RPoke) GetMaxl() int32 {
	if x != nil {
		return x.Maxl
	}
	return 0
}

func (x *RPoke) GetItems() []*RPokeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type Region struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Minl  int32        `protobuf:"varint,2,opt,name=minl,proto3" json:"minl,omitempty"`  //最低等级
	Maxl  int32        `protobuf:"varint,3,opt,name=maxl,proto3" json:"maxl,omitempty"`  //最高等级
	Pokes []*RPoke     `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"` // 宝可梦列表
	Items []*RPokeItem `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"` // 区域的物品列表
}

func (x *Region) Reset() {
	*x = Region{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameManager_Region_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{2}
}

func (x *Region) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Region) GetMinl() int32 {
	if x != nil {
		return x.Minl
	}
	return 0
}

func (x *Region) GetMaxl() int32 {
	if x != nil {
		return x.Maxl
	}
	return 0
}

func (x *Region) GetPokes() []*RPoke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *Region) GetItems() []*RPokeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type RegionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Regions []*Region `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"` // 列表包含多个区域
	Name    string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RegionList) Reset() {
	*x = RegionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GameManager_Region_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionList) ProtoMessage() {}

func (x *RegionList) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionList.ProtoReflect.Descriptor instead.
func (*RegionList) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{3}
}

func (x *RegionList) GetRegions() []*Region {
	if x != nil {
		return x.Regions
	}
	return nil
}

func (x *RegionList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_GameManager_Region_proto protoreflect.FileDescriptor

var file_GameManager_Region_proto_rawDesc = []byte{
	0x0a, 0x18, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x50, 0x65, 0x74, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x09, 0x52, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x01, 0x70, 0x22, 0x7d, 0x0a, 0x05, 0x52, 0x50, 0x6f, 0x6b, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x0c, 0x0a, 0x01, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6e, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d,
	0x69, 0x6e, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x78, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x6d, 0x61, 0x78, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x50, 0x65, 0x74, 0x73, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x94, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x69, 0x6e, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x69,
	0x6e, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x78, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x6d, 0x61, 0x78, 0x6c, 0x12, 0x26, 0x0a, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x50, 0x65, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x12, 0x2a,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x50, 0x65, 0x74, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x50, 0x6f, 0x6b, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4d, 0x0a, 0x0a, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x65, 0x74, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_GameManager_Region_proto_rawDescOnce sync.Once
	file_GameManager_Region_proto_rawDescData = file_GameManager_Region_proto_rawDesc
)

func file_GameManager_Region_proto_rawDescGZIP() []byte {
	file_GameManager_Region_proto_rawDescOnce.Do(func() {
		file_GameManager_Region_proto_rawDescData = protoimpl.X.CompressGZIP(file_GameManager_Region_proto_rawDescData)
	})
	return file_GameManager_Region_proto_rawDescData
}

var file_GameManager_Region_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_GameManager_Region_proto_goTypes = []any{
	(*RPokeItem)(nil),  // 0: PetsProto.RPokeItem
	(*RPoke)(nil),      // 1: PetsProto.RPoke
	(*Region)(nil),     // 2: PetsProto.Region
	(*RegionList)(nil), // 3: PetsProto.RegionList
}
var file_GameManager_Region_proto_depIdxs = []int32{
	0, // 0: PetsProto.RPoke.items:type_name -> PetsProto.RPokeItem
	1, // 1: PetsProto.Region.pokes:type_name -> PetsProto.RPoke
	0, // 2: PetsProto.Region.items:type_name -> PetsProto.RPokeItem
	2, // 3: PetsProto.RegionList.regions:type_name -> PetsProto.Region
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_GameManager_Region_proto_init() }
func file_GameManager_Region_proto_init() {
	if File_GameManager_Region_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_GameManager_Region_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*RPokeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameManager_Region_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RPoke); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameManager_Region_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Region); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GameManager_Region_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RegionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_GameManager_Region_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_GameManager_Region_proto_goTypes,
		DependencyIndexes: file_GameManager_Region_proto_depIdxs,
		MessageInfos:      file_GameManager_Region_proto_msgTypes,
	}.Build()
	File_GameManager_Region_proto = out.File
	file_GameManager_Region_proto_rawDesc = nil
	file_GameManager_Region_proto_goTypes = nil
	file_GameManager_Region_proto_depIdxs = nil
}
