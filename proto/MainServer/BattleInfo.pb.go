// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/BattleInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BattleOutputMessageType int32

const (
	BattleOutputMessageType_Normal  BattleOutputMessageType = 0
	BattleOutputMessageType_Prepare BattleOutputMessageType = 1
	BattleOutputMessageType_TeamAck BattleOutputMessageType = 2
)

// Enum value maps for BattleOutputMessageType.
var (
	BattleOutputMessageType_name = map[int32]string{
		0: "Normal",
		1: "Prepare",
		2: "TeamAck",
	}
	BattleOutputMessageType_value = map[string]int32{
		"Normal":  0,
		"Prepare": 1,
		"TeamAck": 2,
	}
)

func (x BattleOutputMessageType) Enum() *BattleOutputMessageType {
	p := new(BattleOutputMessageType)
	*p = x
	return p
}

func (x BattleOutputMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleOutputMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[0].Descriptor()
}

func (BattleOutputMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[0]
}

func (x BattleOutputMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleOutputMessageType.Descriptor instead.
func (BattleOutputMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{0}
}

type BattleSpecialMove int32

const (
	BattleSpecialMove_SpecialMove_None BattleSpecialMove = 0
)

// Enum value maps for BattleSpecialMove.
var (
	BattleSpecialMove_name = map[int32]string{
		0: "SpecialMove_None",
	}
	BattleSpecialMove_value = map[string]int32{
		"SpecialMove_None": 0,
	}
)

func (x BattleSpecialMove) Enum() *BattleSpecialMove {
	p := new(BattleSpecialMove)
	*p = x
	return p
}

func (x BattleSpecialMove) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleSpecialMove) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[1].Descriptor()
}

func (BattleSpecialMove) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[1]
}

func (x BattleSpecialMove) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleSpecialMove.Descriptor instead.
func (BattleSpecialMove) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{1}
}

type BattleChoiceType int32

const (
	BattleChoiceType_Choice_GiveUp      BattleChoiceType = 0
	BattleChoiceType_Choice_Move        BattleChoiceType = 1
	BattleChoiceType_Choice_Titem       BattleChoiceType = 2
	BattleChoiceType_Choice_Switch      BattleChoiceType = 3
	BattleChoiceType_Choice_ForceSwitch BattleChoiceType = 4
	BattleChoiceType_Choice_TeamAck     BattleChoiceType = 5
	BattleChoiceType_Choice_Run         BattleChoiceType = 6
)

// Enum value maps for BattleChoiceType.
var (
	BattleChoiceType_name = map[int32]string{
		0: "Choice_GiveUp",
		1: "Choice_Move",
		2: "Choice_Titem",
		3: "Choice_Switch",
		4: "Choice_ForceSwitch",
		5: "Choice_TeamAck",
		6: "Choice_Run",
	}
	BattleChoiceType_value = map[string]int32{
		"Choice_GiveUp":      0,
		"Choice_Move":        1,
		"Choice_Titem":       2,
		"Choice_Switch":      3,
		"Choice_ForceSwitch": 4,
		"Choice_TeamAck":     5,
		"Choice_Run":         6,
	}
)

func (x BattleChoiceType) Enum() *BattleChoiceType {
	p := new(BattleChoiceType)
	*p = x
	return p
}

func (x BattleChoiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleChoiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[2].Descriptor()
}

func (BattleChoiceType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[2]
}

func (x BattleChoiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleChoiceType.Descriptor instead.
func (BattleChoiceType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{2}
}

type BattleDecisionType int32

const (
	BattleDecisionType_Choice BattleDecisionType = 0
	BattleDecisionType_Init   BattleDecisionType = 1
	BattleDecisionType_Exit   BattleDecisionType = 2 //投降 //表示这场战斗停止
)

// Enum value maps for BattleDecisionType.
var (
	BattleDecisionType_name = map[int32]string{
		0: "Choice",
		1: "Init",
		2: "Exit",
	}
	BattleDecisionType_value = map[string]int32{
		"Choice": 0,
		"Init":   1,
		"Exit":   2,
	}
)

func (x BattleDecisionType) Enum() *BattleDecisionType {
	p := new(BattleDecisionType)
	*p = x
	return p
}

func (x BattleDecisionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleDecisionType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[3].Descriptor()
}

func (BattleDecisionType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[3]
}

func (x BattleDecisionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleDecisionType.Descriptor instead.
func (BattleDecisionType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{3}
}

type BattleServerMessagePermission int32

const (
	BattleServerMessagePermission_Player      BattleServerMessagePermission = 0
	BattleServerMessagePermission_Audience    BattleServerMessagePermission = 1
	BattleServerMessagePermission_PlayerError BattleServerMessagePermission = 2
)

// Enum value maps for BattleServerMessagePermission.
var (
	BattleServerMessagePermission_name = map[int32]string{
		0: "Player",
		1: "Audience",
		2: "PlayerError",
	}
	BattleServerMessagePermission_value = map[string]int32{
		"Player":      0,
		"Audience":    1,
		"PlayerError": 2,
	}
)

func (x BattleServerMessagePermission) Enum() *BattleServerMessagePermission {
	p := new(BattleServerMessagePermission)
	*p = x
	return p
}

func (x BattleServerMessagePermission) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleServerMessagePermission) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[4].Descriptor()
}

func (BattleServerMessagePermission) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[4]
}

func (x BattleServerMessagePermission) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleServerMessagePermission.Descriptor instead.
func (BattleServerMessagePermission) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{4}
}

type BattleType int32

const (
	BattleType_BattleType_Unknow            BattleType = 0
	BattleType_AI_4                         BattleType = 99
	BattleType_AI_2                         BattleType = 98
	BattleType_Msg                          BattleType = 100
	BattleType_SingleWild                   BattleType = 101
	BattleType_SingleWildAndDoublePokemon   BattleType = 102
	BattleType_DoubleWild                   BattleType = 103
	BattleType_SingleNPC                    BattleType = 104
	BattleType_SingleNPCAndDoublePokemon    BattleType = 105
	BattleType_DoubleNPC                    BattleType = 106
	BattleType_SinglePlayer                 BattleType = 107
	BattleType_SinglePlayerAndDoublePokemon BattleType = 108
	BattleType_DoublePlayer                 BattleType = 109
)

// Enum value maps for BattleType.
var (
	BattleType_name = map[int32]string{
		0:   "BattleType_Unknow",
		99:  "AI_4",
		98:  "AI_2",
		100: "Msg",
		101: "SingleWild",
		102: "SingleWildAndDoublePokemon",
		103: "DoubleWild",
		104: "SingleNPC",
		105: "SingleNPCAndDoublePokemon",
		106: "DoubleNPC",
		107: "SinglePlayer",
		108: "SinglePlayerAndDoublePokemon",
		109: "DoublePlayer",
	}
	BattleType_value = map[string]int32{
		"BattleType_Unknow":            0,
		"AI_4":                         99,
		"AI_2":                         98,
		"Msg":                          100,
		"SingleWild":                   101,
		"SingleWildAndDoublePokemon":   102,
		"DoubleWild":                   103,
		"SingleNPC":                    104,
		"SingleNPCAndDoublePokemon":    105,
		"DoubleNPC":                    106,
		"SinglePlayer":                 107,
		"SinglePlayerAndDoublePokemon": 108,
		"DoublePlayer":                 109,
	}
)

func (x BattleType) Enum() *BattleType {
	p := new(BattleType)
	*p = x
	return p
}

func (x BattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleInfo_proto_enumTypes[5].Descriptor()
}

func (BattleType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleInfo_proto_enumTypes[5]
}

func (x BattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleType.Descriptor instead.
func (BattleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{5}
}

// client -> nakama
type BattlePrepare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleType         BattleType          `protobuf:"varint,1,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	BattleMatchAiMaker *BattleMatchAiMaker `protobuf:"bytes,2,opt,name=battleMatchAiMaker,proto3" json:"battleMatchAiMaker,omitempty"`
	BattleMatchMaker   *BattleMatchMaker   `protobuf:"bytes,3,opt,name=battleMatchMaker,proto3" json:"battleMatchMaker,omitempty"`
}

func (x *BattlePrepare) Reset() {
	*x = BattlePrepare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattlePrepare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePrepare) ProtoMessage() {}

func (x *BattlePrepare) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePrepare.ProtoReflect.Descriptor instead.
func (*BattlePrepare) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{0}
}

func (x *BattlePrepare) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattlePrepare) GetBattleMatchAiMaker() *BattleMatchAiMaker {
	if x != nil {
		return x.BattleMatchAiMaker
	}
	return nil
}

func (x *BattlePrepare) GetBattleMatchMaker() *BattleMatchMaker {
	if x != nil {
		return x.BattleMatchMaker
	}
	return nil
}

type BattleOutputMessageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleId   string                 `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType BattleType             `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	Messages   []*BattleOutputMessage `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages,omitempty"`
}

func (x *BattleOutputMessageInfo) Reset() {
	*x = BattleOutputMessageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleOutputMessageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleOutputMessageInfo) ProtoMessage() {}

func (x *BattleOutputMessageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleOutputMessageInfo.ProtoReflect.Descriptor instead.
func (*BattleOutputMessageInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{1}
}

func (x *BattleOutputMessageInfo) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleOutputMessageInfo) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleOutputMessageInfo) GetMessages() []*BattleOutputMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

// nakama -> client
type BattleOutputMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleId      string                            `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType    BattleType                        `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	MessageType   BattleOutputMessageType           `protobuf:"varint,3,opt,name=messageType,proto3,enum=MainServer.BattleOutputMessageType" json:"messageType,omitempty"`
	TrainerInfos  []*BattlePrepareOutputTrainerInfo `protobuf:"bytes,4,rep,name=trainerInfos,proto3" json:"trainerInfos,omitempty"`
	BattleContent string                            `protobuf:"bytes,5,opt,name=battleContent,proto3" json:"battleContent,omitempty"`
	Ts            int64                             `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`
	PsPlayerKey   string                            `protobuf:"bytes,7,opt,name=psPlayerKey,proto3" json:"psPlayerKey,omitempty"`
}

func (x *BattleOutputMessage) Reset() {
	*x = BattleOutputMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleOutputMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleOutputMessage) ProtoMessage() {}

func (x *BattleOutputMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleOutputMessage.ProtoReflect.Descriptor instead.
func (*BattleOutputMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{2}
}

func (x *BattleOutputMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleOutputMessage) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleOutputMessage) GetMessageType() BattleOutputMessageType {
	if x != nil {
		return x.MessageType
	}
	return BattleOutputMessageType_Normal
}

func (x *BattleOutputMessage) GetTrainerInfos() []*BattlePrepareOutputTrainerInfo {
	if x != nil {
		return x.TrainerInfos
	}
	return nil
}

func (x *BattleOutputMessage) GetBattleContent() string {
	if x != nil {
		return x.BattleContent
	}
	return ""
}

func (x *BattleOutputMessage) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *BattleOutputMessage) GetPsPlayerKey() string {
	if x != nil {
		return x.PsPlayerKey
	}
	return ""
}

type BattlePrepareOutputTrainerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trainer     *Trainer `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	PsPlayerKey string   `protobuf:"bytes,2,opt,name=psPlayerKey,proto3" json:"psPlayerKey,omitempty"` //p1,p2,p3,p4
	IsAi        bool     `protobuf:"varint,3,opt,name=isAi,proto3" json:"isAi,omitempty"`
}

func (x *BattlePrepareOutputTrainerInfo) Reset() {
	*x = BattlePrepareOutputTrainerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattlePrepareOutputTrainerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePrepareOutputTrainerInfo) ProtoMessage() {}

func (x *BattlePrepareOutputTrainerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePrepareOutputTrainerInfo.ProtoReflect.Descriptor instead.
func (*BattlePrepareOutputTrainerInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{3}
}

func (x *BattlePrepareOutputTrainerInfo) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

func (x *BattlePrepareOutputTrainerInfo) GetPsPlayerKey() string {
	if x != nil {
		return x.PsPlayerKey
	}
	return ""
}

func (x *BattlePrepareOutputTrainerInfo) GetIsAi() bool {
	if x != nil {
		return x.IsAi
	}
	return false
}

// client -> nakama
type BattleClientDecisionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DecisionType BattleDecisionType                `protobuf:"varint,1,opt,name=decisionType,proto3,enum=MainServer.BattleDecisionType" json:"decisionType,omitempty"`
	Choices      []*BattleClientDecisionChoiceInfo `protobuf:"bytes,2,rep,name=choices,proto3" json:"choices,omitempty"`
}

func (x *BattleClientDecisionMessage) Reset() {
	*x = BattleClientDecisionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleClientDecisionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleClientDecisionMessage) ProtoMessage() {}

func (x *BattleClientDecisionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleClientDecisionMessage.ProtoReflect.Descriptor instead.
func (*BattleClientDecisionMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{4}
}

func (x *BattleClientDecisionMessage) GetDecisionType() BattleDecisionType {
	if x != nil {
		return x.DecisionType
	}
	return BattleDecisionType_Choice
}

func (x *BattleClientDecisionMessage) GetChoices() []*BattleClientDecisionChoiceInfo {
	if x != nil {
		return x.Choices
	}
	return nil
}

type BattleClientDecisionChoiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Slot       int32            `protobuf:"varint,1,opt,name=slot,proto3" json:"slot,omitempty"` //精灵位置
	TargetSlot int32            `protobuf:"varint,2,opt,name=targetSlot,proto3" json:"targetSlot,omitempty"`
	ChoiceType BattleChoiceType `protobuf:"varint,3,opt,name=choiceType,proto3,enum=MainServer.BattleChoiceType" json:"choiceType,omitempty"`
	Choice     string           `protobuf:"bytes,4,opt,name=choice,proto3" json:"choice,omitempty"`
	// string move = 2; //使用技能
	// string switch = 3; //切换精灵
	// bool titem = 4; //使用道具
	// bool exit = 5; //逃跑
	Special BattleSpecialMove `protobuf:"varint,5,opt,name=special,proto3,enum=MainServer.BattleSpecialMove" json:"special,omitempty"` //比如mega
}

func (x *BattleClientDecisionChoiceInfo) Reset() {
	*x = BattleClientDecisionChoiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleClientDecisionChoiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleClientDecisionChoiceInfo) ProtoMessage() {}

func (x *BattleClientDecisionChoiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleClientDecisionChoiceInfo.ProtoReflect.Descriptor instead.
func (*BattleClientDecisionChoiceInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{5}
}

func (x *BattleClientDecisionChoiceInfo) GetSlot() int32 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *BattleClientDecisionChoiceInfo) GetTargetSlot() int32 {
	if x != nil {
		return x.TargetSlot
	}
	return 0
}

func (x *BattleClientDecisionChoiceInfo) GetChoiceType() BattleChoiceType {
	if x != nil {
		return x.ChoiceType
	}
	return BattleChoiceType_Choice_GiveUp
}

func (x *BattleClientDecisionChoiceInfo) GetChoice() string {
	if x != nil {
		return x.Choice
	}
	return ""
}

func (x *BattleClientDecisionChoiceInfo) GetSpecial() BattleSpecialMove {
	if x != nil {
		return x.Special
	}
	return BattleSpecialMove_SpecialMove_None
}

// nakama -> ps
type BattleDecisionMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleId     string                              `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	BattleType   BattleType                          `protobuf:"varint,2,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
	DecisionType BattleDecisionType                  `protobuf:"varint,3,opt,name=decisionType,proto3,enum=MainServer.BattleDecisionType" json:"decisionType,omitempty"`
	Choices      map[int64]*BattleDecisionChoiceInfo `protobuf:"bytes,4,rep,name=choices,proto3" json:"choices,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //tid -> choice
	// repeated BattleDecisionChoiceInfo choices = 4;
	// string content = 3;
	TeamInfos []*BattleInitTeamInfo `protobuf:"bytes,5,rep,name=teamInfos,proto3" json:"teamInfos,omitempty"`
}

func (x *BattleDecisionMessage) Reset() {
	*x = BattleDecisionMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleDecisionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleDecisionMessage) ProtoMessage() {}

func (x *BattleDecisionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleDecisionMessage.ProtoReflect.Descriptor instead.
func (*BattleDecisionMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{6}
}

func (x *BattleDecisionMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleDecisionMessage) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

func (x *BattleDecisionMessage) GetDecisionType() BattleDecisionType {
	if x != nil {
		return x.DecisionType
	}
	return BattleDecisionType_Choice
}

func (x *BattleDecisionMessage) GetChoices() map[int64]*BattleDecisionChoiceInfo {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *BattleDecisionMessage) GetTeamInfos() []*BattleInitTeamInfo {
	if x != nil {
		return x.TeamInfos
	}
	return nil
}

// nakama -> ps
type BattleDecisionChoiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid      int64  `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`
	Choice   string `protobuf:"bytes,2,opt,name=choice,proto3" json:"choice,omitempty"`
	Done     bool   `protobuf:"varint,3,opt,name=done,proto3" json:"done,omitempty"`         //指令出现一个错误，则都用ai进行命令
	IsGiveup bool   `protobuf:"varint,4,opt,name=isGiveup,proto3" json:"isGiveup,omitempty"` //
}

func (x *BattleDecisionChoiceInfo) Reset() {
	*x = BattleDecisionChoiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleDecisionChoiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleDecisionChoiceInfo) ProtoMessage() {}

func (x *BattleDecisionChoiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleDecisionChoiceInfo.ProtoReflect.Descriptor instead.
func (*BattleDecisionChoiceInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{7}
}

func (x *BattleDecisionChoiceInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleDecisionChoiceInfo) GetChoice() string {
	if x != nil {
		return x.Choice
	}
	return ""
}

func (x *BattleDecisionChoiceInfo) GetDone() bool {
	if x != nil {
		return x.Done
	}
	return false
}

func (x *BattleDecisionChoiceInfo) GetIsGiveup() bool {
	if x != nil {
		return x.IsGiveup
	}
	return false
}

type BattleInitTeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// int64 tid = 1;
	Team        string                          `protobuf:"bytes,1,opt,name=team,proto3" json:"team,omitempty"`
	TrainerInfo *BattlePrepareOutputTrainerInfo `protobuf:"bytes,2,opt,name=trainerInfo,proto3" json:"trainerInfo,omitempty"`
}

func (x *BattleInitTeamInfo) Reset() {
	*x = BattleInitTeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleInitTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleInitTeamInfo) ProtoMessage() {}

func (x *BattleInitTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleInitTeamInfo.ProtoReflect.Descriptor instead.
func (*BattleInitTeamInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{8}
}

func (x *BattleInitTeamInfo) GetTeam() string {
	if x != nil {
		return x.Team
	}
	return ""
}

func (x *BattleInitTeamInfo) GetTrainerInfo() *BattlePrepareOutputTrainerInfo {
	if x != nil {
		return x.TrainerInfo
	}
	return nil
}

// ps -> nakama
type BattleServerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattleId            string                               `protobuf:"bytes,1,opt,name=battleId,proto3" json:"battleId,omitempty"`
	Tid                 int64                                `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Permission          BattleServerMessagePermission        `protobuf:"varint,3,opt,name=permission,proto3,enum=MainServer.BattleServerMessagePermission" json:"permission,omitempty"`
	Content             string                               `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	NeedUpdatePokeInfos []*BattleServerMessageUpdatePokeInfo `protobuf:"bytes,5,rep,name=needUpdatePokeInfos,proto3" json:"needUpdatePokeInfos,omitempty"`
	WinInfos            []*BattleServerMessageWinInfo        `protobuf:"bytes,6,rep,name=winInfos,proto3" json:"winInfos,omitempty"`
	TitemInfos          []*BattleServerMessageTitemInfo      `protobuf:"bytes,7,rep,name=titemInfos,proto3" json:"titemInfos,omitempty"`
}

func (x *BattleServerMessage) Reset() {
	*x = BattleServerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleServerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessage) ProtoMessage() {}

func (x *BattleServerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessage.ProtoReflect.Descriptor instead.
func (*BattleServerMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{9}
}

func (x *BattleServerMessage) GetBattleId() string {
	if x != nil {
		return x.BattleId
	}
	return ""
}

func (x *BattleServerMessage) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleServerMessage) GetPermission() BattleServerMessagePermission {
	if x != nil {
		return x.Permission
	}
	return BattleServerMessagePermission_Player
}

func (x *BattleServerMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BattleServerMessage) GetNeedUpdatePokeInfos() []*BattleServerMessageUpdatePokeInfo {
	if x != nil {
		return x.NeedUpdatePokeInfos
	}
	return nil
}

func (x *BattleServerMessage) GetWinInfos() []*BattleServerMessageWinInfo {
	if x != nil {
		return x.WinInfos
	}
	return nil
}

func (x *BattleServerMessage) GetTitemInfos() []*BattleServerMessageTitemInfo {
	if x != nil {
		return x.TitemInfos
	}
	return nil
}

type BattleServerMessageUpdatePokeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid   int64 `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	HpSub int32 `protobuf:"varint,3,opt,name=hpSub,proto3" json:"hpSub,omitempty"`
}

func (x *BattleServerMessageUpdatePokeInfo) Reset() {
	*x = BattleServerMessageUpdatePokeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleServerMessageUpdatePokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageUpdatePokeInfo) ProtoMessage() {}

func (x *BattleServerMessageUpdatePokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageUpdatePokeInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageUpdatePokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{10}
}

func (x *BattleServerMessageUpdatePokeInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BattleServerMessageUpdatePokeInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BattleServerMessageUpdatePokeInfo) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

type BattleServerMessageWinInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WinPartId string  `protobuf:"bytes,1,opt,name=winPartId,proto3" json:"winPartId,omitempty"`
	WinTids   []int64 `protobuf:"varint,2,rep,packed,name=winTids,proto3" json:"winTids,omitempty"`
}

func (x *BattleServerMessageWinInfo) Reset() {
	*x = BattleServerMessageWinInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleServerMessageWinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageWinInfo) ProtoMessage() {}

func (x *BattleServerMessageWinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageWinInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageWinInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{11}
}

func (x *BattleServerMessageWinInfo) GetWinPartId() string {
	if x != nil {
		return x.WinPartId
	}
	return ""
}

func (x *BattleServerMessageWinInfo) GetWinTids() []int64 {
	if x != nil {
		return x.WinTids
	}
	return nil
}

type BattleServerMessageTitemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Titem    string `protobuf:"bytes,1,opt,name=titem,proto3" json:"titem,omitempty"`
	TargetId int64  `protobuf:"varint,2,opt,name=targetId,proto3" json:"targetId,omitempty"`
	Success  bool   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *BattleServerMessageTitemInfo) Reset() {
	*x = BattleServerMessageTitemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleInfo_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleServerMessageTitemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleServerMessageTitemInfo) ProtoMessage() {}

func (x *BattleServerMessageTitemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleInfo_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleServerMessageTitemInfo.ProtoReflect.Descriptor instead.
func (*BattleServerMessageTitemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleInfo_proto_rawDescGZIP(), []int{12}
}

func (x *BattleServerMessageTitemInfo) GetTitem() string {
	if x != nil {
		return x.Titem
	}
	return ""
}

func (x *BattleServerMessageTitemInfo) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *BattleServerMessageTitemInfo) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_MainServer_BattleInfo_proto protoreflect.FileDescriptor

var file_MainServer_BattleInfo_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xe1, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x72, 0x65, 0x70,
	0x61, 0x72, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x12, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x69, 0x4d, 0x61, 0x6b, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x41, 0x69, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x12, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x41, 0x69, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x10, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61,
	0x6b, 0x65, 0x72, 0x52, 0x10, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x4d, 0x61, 0x6b, 0x65, 0x72, 0x22, 0xaa, 0x01, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a,
	0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x22, 0xd8, 0x02, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45,
	0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x22, 0x85, 0x01,
	0x0a, 0x1e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2d, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4b, 0x65,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73, 0x41, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x04, 0x69, 0x73, 0x41, 0x69, 0x22, 0xa7, 0x01, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x07, 0x63, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22,
	0xe3, 0x01, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x07,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x52, 0x07, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x22, 0x99, 0x03, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0a, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x3c, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a,
	0x60, 0x0a, 0x0c, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x74, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64, 0x6f, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x47, 0x69, 0x76, 0x65, 0x75, 0x70, 0x22, 0x76, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x61,
	0x6d, 0x12, 0x4c, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x97, 0x03, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x13, 0x6e, 0x65,
	0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f,
	0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x6e, 0x65, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x42, 0x0a, 0x08, 0x77,
	0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x57, 0x69,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x77, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x48, 0x0a, 0x0a, 0x74, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x74,
	0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x5b, 0x0a, 0x21, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x68, 0x70, 0x53, 0x75, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x68, 0x70, 0x53, 0x75, 0x62, 0x22, 0x54, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x57, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e, 0x50, 0x61, 0x72, 0x74, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69, 0x6e, 0x50, 0x61, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x69, 0x6e, 0x54, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x07, 0x77, 0x69, 0x6e, 0x54, 0x69, 0x64, 0x73, 0x22, 0x6a, 0x0a, 0x1c,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2a, 0x3f, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x54, 0x65, 0x61, 0x6d, 0x41, 0x63, 0x6b, 0x10, 0x02, 0x2a, 0x29, 0x0a, 0x11, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x5f, 0x4e, 0x6f,
	0x6e, 0x65, 0x10, 0x00, 0x2a, 0x97, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x68, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x47, 0x69, 0x76, 0x65, 0x55, 0x70, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x4d, 0x6f, 0x76, 0x65, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x54, 0x69, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x46, 0x6f, 0x72,
	0x63, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x63, 0x6b, 0x10, 0x05, 0x12, 0x0e,
	0x0a, 0x0a, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x52, 0x75, 0x6e, 0x10, 0x06, 0x2a, 0x34,
	0x0a, 0x12, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x49, 0x6e, 0x69, 0x74, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x45, 0x78,
	0x69, 0x74, 0x10, 0x02, 0x2a, 0x4a, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x10, 0x01, 0x12,
	0x0f, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x02,
	0x2a, 0x83, 0x02, 0x0a, 0x0a, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x15, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x49, 0x5f, 0x34, 0x10, 0x63,
	0x12, 0x08, 0x0a, 0x04, 0x41, 0x49, 0x5f, 0x32, 0x10, 0x62, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x10, 0x64, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x57, 0x69, 0x6c,
	0x64, 0x10, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x57, 0x69, 0x6c,
	0x64, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f,
	0x6e, 0x10, 0x66, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x57, 0x69, 0x6c,
	0x64, 0x10, 0x67, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4e, 0x50, 0x43,
	0x10, 0x68, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4e, 0x50, 0x43, 0x41,
	0x6e, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x10,
	0x69, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4e, 0x50, 0x43, 0x10, 0x6a,
	0x12, 0x10, 0x0a, 0x0c, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x10, 0x6b, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x41, 0x6e, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x6d,
	0x6f, 0x6e, 0x10, 0x6c, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x10, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_BattleInfo_proto_rawDescOnce sync.Once
	file_MainServer_BattleInfo_proto_rawDescData = file_MainServer_BattleInfo_proto_rawDesc
)

func file_MainServer_BattleInfo_proto_rawDescGZIP() []byte {
	file_MainServer_BattleInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_BattleInfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_BattleInfo_proto_rawDescData)
	})
	return file_MainServer_BattleInfo_proto_rawDescData
}

var file_MainServer_BattleInfo_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_MainServer_BattleInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_MainServer_BattleInfo_proto_goTypes = []any{
	(BattleOutputMessageType)(0),              // 0: MainServer.BattleOutputMessageType
	(BattleSpecialMove)(0),                    // 1: MainServer.BattleSpecialMove
	(BattleChoiceType)(0),                     // 2: MainServer.BattleChoiceType
	(BattleDecisionType)(0),                   // 3: MainServer.BattleDecisionType
	(BattleServerMessagePermission)(0),        // 4: MainServer.BattleServerMessagePermission
	(BattleType)(0),                           // 5: MainServer.BattleType
	(*BattlePrepare)(nil),                     // 6: MainServer.BattlePrepare
	(*BattleOutputMessageInfo)(nil),           // 7: MainServer.BattleOutputMessageInfo
	(*BattleOutputMessage)(nil),               // 8: MainServer.BattleOutputMessage
	(*BattlePrepareOutputTrainerInfo)(nil),    // 9: MainServer.BattlePrepareOutputTrainerInfo
	(*BattleClientDecisionMessage)(nil),       // 10: MainServer.BattleClientDecisionMessage
	(*BattleClientDecisionChoiceInfo)(nil),    // 11: MainServer.BattleClientDecisionChoiceInfo
	(*BattleDecisionMessage)(nil),             // 12: MainServer.BattleDecisionMessage
	(*BattleDecisionChoiceInfo)(nil),          // 13: MainServer.BattleDecisionChoiceInfo
	(*BattleInitTeamInfo)(nil),                // 14: MainServer.BattleInitTeamInfo
	(*BattleServerMessage)(nil),               // 15: MainServer.BattleServerMessage
	(*BattleServerMessageUpdatePokeInfo)(nil), // 16: MainServer.BattleServerMessageUpdatePokeInfo
	(*BattleServerMessageWinInfo)(nil),        // 17: MainServer.BattleServerMessageWinInfo
	(*BattleServerMessageTitemInfo)(nil),      // 18: MainServer.BattleServerMessageTitemInfo
	nil,                                       // 19: MainServer.BattleDecisionMessage.ChoicesEntry
	(*BattleMatchAiMaker)(nil),                // 20: MainServer.BattleMatchAiMaker
	(*BattleMatchMaker)(nil),                  // 21: MainServer.BattleMatchMaker
	(*Trainer)(nil),                           // 22: MainServer.Trainer
}
var file_MainServer_BattleInfo_proto_depIdxs = []int32{
	5,  // 0: MainServer.BattlePrepare.battleType:type_name -> MainServer.BattleType
	20, // 1: MainServer.BattlePrepare.battleMatchAiMaker:type_name -> MainServer.BattleMatchAiMaker
	21, // 2: MainServer.BattlePrepare.battleMatchMaker:type_name -> MainServer.BattleMatchMaker
	5,  // 3: MainServer.BattleOutputMessageInfo.battleType:type_name -> MainServer.BattleType
	8,  // 4: MainServer.BattleOutputMessageInfo.messages:type_name -> MainServer.BattleOutputMessage
	5,  // 5: MainServer.BattleOutputMessage.battleType:type_name -> MainServer.BattleType
	0,  // 6: MainServer.BattleOutputMessage.messageType:type_name -> MainServer.BattleOutputMessageType
	9,  // 7: MainServer.BattleOutputMessage.trainerInfos:type_name -> MainServer.BattlePrepareOutputTrainerInfo
	22, // 8: MainServer.BattlePrepareOutputTrainerInfo.trainer:type_name -> MainServer.Trainer
	3,  // 9: MainServer.BattleClientDecisionMessage.decisionType:type_name -> MainServer.BattleDecisionType
	11, // 10: MainServer.BattleClientDecisionMessage.choices:type_name -> MainServer.BattleClientDecisionChoiceInfo
	2,  // 11: MainServer.BattleClientDecisionChoiceInfo.choiceType:type_name -> MainServer.BattleChoiceType
	1,  // 12: MainServer.BattleClientDecisionChoiceInfo.special:type_name -> MainServer.BattleSpecialMove
	5,  // 13: MainServer.BattleDecisionMessage.battleType:type_name -> MainServer.BattleType
	3,  // 14: MainServer.BattleDecisionMessage.decisionType:type_name -> MainServer.BattleDecisionType
	19, // 15: MainServer.BattleDecisionMessage.choices:type_name -> MainServer.BattleDecisionMessage.ChoicesEntry
	14, // 16: MainServer.BattleDecisionMessage.teamInfos:type_name -> MainServer.BattleInitTeamInfo
	9,  // 17: MainServer.BattleInitTeamInfo.trainerInfo:type_name -> MainServer.BattlePrepareOutputTrainerInfo
	4,  // 18: MainServer.BattleServerMessage.permission:type_name -> MainServer.BattleServerMessagePermission
	16, // 19: MainServer.BattleServerMessage.needUpdatePokeInfos:type_name -> MainServer.BattleServerMessageUpdatePokeInfo
	17, // 20: MainServer.BattleServerMessage.winInfos:type_name -> MainServer.BattleServerMessageWinInfo
	18, // 21: MainServer.BattleServerMessage.titemInfos:type_name -> MainServer.BattleServerMessageTitemInfo
	13, // 22: MainServer.BattleDecisionMessage.ChoicesEntry.value:type_name -> MainServer.BattleDecisionChoiceInfo
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_MainServer_BattleInfo_proto_init() }
func file_MainServer_BattleInfo_proto_init() {
	if File_MainServer_BattleInfo_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_BattleMatch_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_BattleInfo_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BattlePrepare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*BattleOutputMessageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*BattleOutputMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*BattlePrepareOutputTrainerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*BattleClientDecisionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*BattleClientDecisionChoiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*BattleDecisionMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*BattleDecisionChoiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*BattleInitTeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*BattleServerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*BattleServerMessageUpdatePokeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*BattleServerMessageWinInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleInfo_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*BattleServerMessageTitemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_BattleInfo_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattleInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_BattleInfo_proto_depIdxs,
		EnumInfos:         file_MainServer_BattleInfo_proto_enumTypes,
		MessageInfos:      file_MainServer_BattleInfo_proto_msgTypes,
	}.Build()
	File_MainServer_BattleInfo_proto = out.File
	file_MainServer_BattleInfo_proto_rawDesc = nil
	file_MainServer_BattleInfo_proto_goTypes = nil
	file_MainServer_BattleInfo_proto_depIdxs = nil
}
