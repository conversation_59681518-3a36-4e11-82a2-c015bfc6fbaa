// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/BattleMatch.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BattleMatchMatchStyle int32

const (
	BattleMatchMatchStyle_free BattleMatchMatchStyle = 0
	BattleMatchMatchStyle_rank BattleMatchMatchStyle = 1
	BattleMatchMatchStyle_net  BattleMatchMatchStyle = 2
)

// Enum value maps for BattleMatchMatchStyle.
var (
	BattleMatchMatchStyle_name = map[int32]string{
		0: "free",
		1: "rank",
		2: "net",
	}
	BattleMatchMatchStyle_value = map[string]int32{
		"free": 0,
		"rank": 1,
		"net":  2,
	}
)

func (x BattleMatchMatchStyle) Enum() *BattleMatchMatchStyle {
	p := new(BattleMatchMatchStyle)
	*p = x
	return p
}

func (x BattleMatchMatchStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleMatchMatchStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleMatch_proto_enumTypes[0].Descriptor()
}

func (BattleMatchMatchStyle) Type() protoreflect.EnumType {
	return &file_MainServer_BattleMatch_proto_enumTypes[0]
}

func (x BattleMatchMatchStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleMatchMatchStyle.Descriptor instead.
func (BattleMatchMatchStyle) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{0}
}

type BattleMatchThreeTrainerType int32

const (
	BattleMatchThreeTrainerType_none   BattleMatchThreeTrainerType = 0
	BattleMatchThreeTrainerType_accept BattleMatchThreeTrainerType = 1
	BattleMatchThreeTrainerType_only   BattleMatchThreeTrainerType = 2
)

// Enum value maps for BattleMatchThreeTrainerType.
var (
	BattleMatchThreeTrainerType_name = map[int32]string{
		0: "none",
		1: "accept",
		2: "only",
	}
	BattleMatchThreeTrainerType_value = map[string]int32{
		"none":   0,
		"accept": 1,
		"only":   2,
	}
)

func (x BattleMatchThreeTrainerType) Enum() *BattleMatchThreeTrainerType {
	p := new(BattleMatchThreeTrainerType)
	*p = x
	return p
}

func (x BattleMatchThreeTrainerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleMatchThreeTrainerType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleMatch_proto_enumTypes[1].Descriptor()
}

func (BattleMatchThreeTrainerType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleMatch_proto_enumTypes[1]
}

func (x BattleMatchThreeTrainerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleMatchThreeTrainerType.Descriptor instead.
func (BattleMatchThreeTrainerType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{1}
}

type BattleMatchMaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchStyle       BattleMatchMatchStyle       `protobuf:"varint,1,opt,name=matchStyle,proto3,enum=MainServer.BattleMatchMatchStyle" json:"matchStyle,omitempty"`
	ThreeTrainerType BattleMatchThreeTrainerType `protobuf:"varint,2,opt,name=threeTrainerType,proto3,enum=MainServer.BattleMatchThreeTrainerType" json:"threeTrainerType,omitempty"`
	Label            *BattleMatchLabel           `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	Name             string                      `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *BattleMatchMaker) Reset() {
	*x = BattleMatchMaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleMatch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleMatchMaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchMaker) ProtoMessage() {}

func (x *BattleMatchMaker) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchMaker.ProtoReflect.Descriptor instead.
func (*BattleMatchMaker) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{0}
}

func (x *BattleMatchMaker) GetMatchStyle() BattleMatchMatchStyle {
	if x != nil {
		return x.MatchStyle
	}
	return BattleMatchMatchStyle_free
}

func (x *BattleMatchMaker) GetThreeTrainerType() BattleMatchThreeTrainerType {
	if x != nil {
		return x.ThreeTrainerType
	}
	return BattleMatchThreeTrainerType_none
}

func (x *BattleMatchMaker) GetLabel() *BattleMatchLabel {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *BattleMatchMaker) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BattleMatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchId      string            `protobuf:"bytes,1,opt,name=matchId,proto3" json:"matchId,omitempty"`
	Label        *BattleMatchLabel `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	TrainerCount int32             `protobuf:"varint,3,opt,name=trainerCount,proto3" json:"trainerCount,omitempty"`
}

func (x *BattleMatchInfo) Reset() {
	*x = BattleMatchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleMatch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleMatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchInfo) ProtoMessage() {}

func (x *BattleMatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchInfo.ProtoReflect.Descriptor instead.
func (*BattleMatchInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{1}
}

func (x *BattleMatchInfo) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *BattleMatchInfo) GetLabel() *BattleMatchLabel {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *BattleMatchInfo) GetTrainerCount() int32 {
	if x != nil {
		return x.TrainerCount
	}
	return 0
}

type BattleMatchLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Open       bool `protobuf:"varint,1,opt,name=open,proto3" json:"open,omitempty"`
	Started    bool `protobuf:"varint,2,opt,name=started,proto3" json:"started,omitempty"`
	PartyUpTwo bool `protobuf:"varint,3,opt,name=party_up_two,json=partyUpTwo,proto3" json:"party_up_two,omitempty"` //2人以上
	// bool three = 4; //接受3人
	Min              int32                       `protobuf:"varint,4,opt,name=min,proto3" json:"min,omitempty"`
	Max              int32                       `protobuf:"varint,5,opt,name=max,proto3" json:"max,omitempty"`
	MatchStyle       BattleMatchMatchStyle       `protobuf:"varint,6,opt,name=matchStyle,proto3,enum=MainServer.BattleMatchMatchStyle" json:"matchStyle,omitempty"`
	ThreeTrainerType BattleMatchThreeTrainerType `protobuf:"varint,7,opt,name=threeTrainerType,proto3,enum=MainServer.BattleMatchThreeTrainerType" json:"threeTrainerType,omitempty"`
	Name             string                      `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *BattleMatchLabel) Reset() {
	*x = BattleMatchLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleMatch_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleMatchLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchLabel) ProtoMessage() {}

func (x *BattleMatchLabel) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchLabel.ProtoReflect.Descriptor instead.
func (*BattleMatchLabel) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{2}
}

func (x *BattleMatchLabel) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

func (x *BattleMatchLabel) GetStarted() bool {
	if x != nil {
		return x.Started
	}
	return false
}

func (x *BattleMatchLabel) GetPartyUpTwo() bool {
	if x != nil {
		return x.PartyUpTwo
	}
	return false
}

func (x *BattleMatchLabel) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *BattleMatchLabel) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *BattleMatchLabel) GetMatchStyle() BattleMatchMatchStyle {
	if x != nil {
		return x.MatchStyle
	}
	return BattleMatchMatchStyle_free
}

func (x *BattleMatchLabel) GetThreeTrainerType() BattleMatchThreeTrainerType {
	if x != nil {
		return x.ThreeTrainerType
	}
	return BattleMatchThreeTrainerType_none
}

func (x *BattleMatchLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BattleMatchAiMaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReginId string   `protobuf:"bytes,2,opt,name=reginId,proto3" json:"reginId,omitempty"`
	AreaId  string   `protobuf:"bytes,3,opt,name=areaId,proto3" json:"areaId,omitempty"`
	NpcIds  []string `protobuf:"bytes,4,rep,name=npcIds,proto3" json:"npcIds,omitempty"`
}

func (x *BattleMatchAiMaker) Reset() {
	*x = BattleMatchAiMaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BattleMatch_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattleMatchAiMaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchAiMaker) ProtoMessage() {}

func (x *BattleMatchAiMaker) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchAiMaker.ProtoReflect.Descriptor instead.
func (*BattleMatchAiMaker) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{3}
}

func (x *BattleMatchAiMaker) GetReginId() string {
	if x != nil {
		return x.ReginId
	}
	return ""
}

func (x *BattleMatchAiMaker) GetAreaId() string {
	if x != nil {
		return x.AreaId
	}
	return ""
}

func (x *BattleMatchAiMaker) GetNpcIds() []string {
	if x != nil {
		return x.NpcIds
	}
	return nil
}

var File_MainServer_BattleMatch_proto protoreflect.FileDescriptor

var file_MainServer_BattleMatch_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xf2, 0x01, 0x0a, 0x10, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x12,
	0x41, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x12, 0x53, 0x0a, 0x10, 0x74, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x83, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb2, 0x02, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74,
	0x79, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x77, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x70, 0x61, 0x72, 0x74, 0x79, 0x55, 0x70, 0x54, 0x77, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x41,
	0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x53, 0x0a, 0x10, 0x74, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x69, 0x4d, 0x61, 0x6b, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x72,
	0x65, 0x61, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x72, 0x65, 0x61,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x70, 0x63, 0x49, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x70, 0x63, 0x49, 0x64, 0x73, 0x2a, 0x34, 0x0a, 0x15, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x79, 0x6c, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x66, 0x72, 0x65, 0x65, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x6e, 0x65, 0x74, 0x10, 0x02,
	0x2a, 0x3d, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54,
	0x68, 0x72, 0x65, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x08, 0x0a, 0x04, 0x6e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x6f, 0x6e, 0x6c, 0x79, 0x10, 0x02, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_BattleMatch_proto_rawDescOnce sync.Once
	file_MainServer_BattleMatch_proto_rawDescData = file_MainServer_BattleMatch_proto_rawDesc
)

func file_MainServer_BattleMatch_proto_rawDescGZIP() []byte {
	file_MainServer_BattleMatch_proto_rawDescOnce.Do(func() {
		file_MainServer_BattleMatch_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_BattleMatch_proto_rawDescData)
	})
	return file_MainServer_BattleMatch_proto_rawDescData
}

var file_MainServer_BattleMatch_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_BattleMatch_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_BattleMatch_proto_goTypes = []any{
	(BattleMatchMatchStyle)(0),       // 0: MainServer.BattleMatchMatchStyle
	(BattleMatchThreeTrainerType)(0), // 1: MainServer.BattleMatchThreeTrainerType
	(*BattleMatchMaker)(nil),         // 2: MainServer.BattleMatchMaker
	(*BattleMatchInfo)(nil),          // 3: MainServer.BattleMatchInfo
	(*BattleMatchLabel)(nil),         // 4: MainServer.BattleMatchLabel
	(*BattleMatchAiMaker)(nil),       // 5: MainServer.BattleMatchAiMaker
}
var file_MainServer_BattleMatch_proto_depIdxs = []int32{
	0, // 0: MainServer.BattleMatchMaker.matchStyle:type_name -> MainServer.BattleMatchMatchStyle
	1, // 1: MainServer.BattleMatchMaker.threeTrainerType:type_name -> MainServer.BattleMatchThreeTrainerType
	4, // 2: MainServer.BattleMatchMaker.label:type_name -> MainServer.BattleMatchLabel
	4, // 3: MainServer.BattleMatchInfo.label:type_name -> MainServer.BattleMatchLabel
	0, // 4: MainServer.BattleMatchLabel.matchStyle:type_name -> MainServer.BattleMatchMatchStyle
	1, // 5: MainServer.BattleMatchLabel.threeTrainerType:type_name -> MainServer.BattleMatchThreeTrainerType
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_MainServer_BattleMatch_proto_init() }
func file_MainServer_BattleMatch_proto_init() {
	if File_MainServer_BattleMatch_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_BattleMatch_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BattleMatchMaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleMatch_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*BattleMatchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleMatch_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*BattleMatchLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BattleMatch_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*BattleMatchAiMaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_BattleMatch_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattleMatch_proto_goTypes,
		DependencyIndexes: file_MainServer_BattleMatch_proto_depIdxs,
		EnumInfos:         file_MainServer_BattleMatch_proto_enumTypes,
		MessageInfos:      file_MainServer_BattleMatch_proto_msgTypes,
	}.Build()
	File_MainServer_BattleMatch_proto = out.File
	file_MainServer_BattleMatch_proto_rawDesc = nil
	file_MainServer_BattleMatch_proto_goTypes = nil
	file_MainServer_BattleMatch_proto_depIdxs = nil
}
