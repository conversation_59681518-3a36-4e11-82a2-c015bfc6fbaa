// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/BreedPoke.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BreedPokemonRequest - 繁殖宝可梦请求
type BreedPokemonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FatherPokeId int64          `protobuf:"varint,1,opt,name=father_poke_id,json=fatherPokeId,proto3" json:"father_poke_id,omitempty"` // 父方宝可梦ID
	MotherPokeId int64          `protobuf:"varint,2,opt,name=mother_poke_id,json=motherPokeId,proto3" json:"mother_poke_id,omitempty"` // 母方宝可梦ID
	FatherItems  *BreedingItems `protobuf:"bytes,3,opt,name=father_items,json=fatherItems,proto3" json:"father_items,omitempty"`       // 父方携带的繁殖道具
	MotherItems  *BreedingItems `protobuf:"bytes,4,opt,name=mother_items,json=motherItems,proto3" json:"mother_items,omitempty"`       // 母方携带的繁殖道具
	BreedCount   int32          `protobuf:"varint,5,opt,name=breed_count,json=breedCount,proto3" json:"breed_count,omitempty"`         // 繁殖次数，影响死亡概率
}

func (x *BreedPokemonRequest) Reset() {
	*x = BreedPokemonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedPokemonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedPokemonRequest) ProtoMessage() {}

func (x *BreedPokemonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedPokemonRequest.ProtoReflect.Descriptor instead.
func (*BreedPokemonRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{0}
}

func (x *BreedPokemonRequest) GetFatherPokeId() int64 {
	if x != nil {
		return x.FatherPokeId
	}
	return 0
}

func (x *BreedPokemonRequest) GetMotherPokeId() int64 {
	if x != nil {
		return x.MotherPokeId
	}
	return 0
}

func (x *BreedPokemonRequest) GetFatherItems() *BreedingItems {
	if x != nil {
		return x.FatherItems
	}
	return nil
}

func (x *BreedPokemonRequest) GetMotherItems() *BreedingItems {
	if x != nil {
		return x.MotherItems
	}
	return nil
}

func (x *BreedPokemonRequest) GetBreedCount() int32 {
	if x != nil {
		return x.BreedCount
	}
	return 0
}

// BreedingItems - 繁殖道具
type BreedingItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedString     bool      `protobuf:"varint,1,opt,name=red_string,json=redString,proto3" json:"red_string,omitempty"`             // 红线，用于传递特性
	ShinyCharm    bool      `protobuf:"varint,2,opt,name=shiny_charm,json=shinyCharm,proto3" json:"shiny_charm,omitempty"`          // 闪耀护符，提高生产闪光宝可梦的概率
	ShinyDrug     bool      `protobuf:"varint,3,opt,name=shiny_drug,json=shinyDrug,proto3" json:"shiny_drug,omitempty"`             // 闪光药，提高生产闪光宝可梦的概率
	ImmortalCharm bool      `protobuf:"varint,4,opt,name=immortal_charm,json=immortalCharm,proto3" json:"immortal_charm,omitempty"` // 不死护符，防止宝可梦死亡
	StatItems     *PokeStat `protobuf:"bytes,5,opt,name=stat_items,json=statItems,proto3" json:"stat_items,omitempty"`              // 能力值遗传道具
}

func (x *BreedingItems) Reset() {
	*x = BreedingItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedingItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedingItems) ProtoMessage() {}

func (x *BreedingItems) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedingItems.ProtoReflect.Descriptor instead.
func (*BreedingItems) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{1}
}

func (x *BreedingItems) GetRedString() bool {
	if x != nil {
		return x.RedString
	}
	return false
}

func (x *BreedingItems) GetShinyCharm() bool {
	if x != nil {
		return x.ShinyCharm
	}
	return false
}

func (x *BreedingItems) GetShinyDrug() bool {
	if x != nil {
		return x.ShinyDrug
	}
	return false
}

func (x *BreedingItems) GetImmortalCharm() bool {
	if x != nil {
		return x.ImmortalCharm
	}
	return false
}

func (x *BreedingItems) GetStatItems() *PokeStat {
	if x != nil {
		return x.StatItems
	}
	return nil
}

// BreedPokemonResponse - 繁殖宝可梦响应
type BreedPokemonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Baby       *Poke `protobuf:"bytes,1,opt,name=baby,proto3" json:"baby,omitempty"`                                // 新生宝可梦
	FatherDied bool  `protobuf:"varint,2,opt,name=father_died,json=fatherDied,proto3" json:"father_died,omitempty"` // 父方是否死亡
	MotherDied bool  `protobuf:"varint,3,opt,name=mother_died,json=motherDied,proto3" json:"mother_died,omitempty"` // 母方是否死亡
}

func (x *BreedPokemonResponse) Reset() {
	*x = BreedPokemonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedPokemonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedPokemonResponse) ProtoMessage() {}

func (x *BreedPokemonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedPokemonResponse.ProtoReflect.Descriptor instead.
func (*BreedPokemonResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{2}
}

func (x *BreedPokemonResponse) GetBaby() *Poke {
	if x != nil {
		return x.Baby
	}
	return nil
}

func (x *BreedPokemonResponse) GetFatherDied() bool {
	if x != nil {
		return x.FatherDied
	}
	return false
}

func (x *BreedPokemonResponse) GetMotherDied() bool {
	if x != nil {
		return x.MotherDied
	}
	return false
}

// EvolveByLevelRequest - 等级进化请求
type EvolveByLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId          int64  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`                           // 宝可梦ID
	TargetEvolution string `protobuf:"bytes,2,opt,name=target_evolution,json=targetEvolution,proto3" json:"target_evolution,omitempty"` // 目标进化形态
}

func (x *EvolveByLevelRequest) Reset() {
	*x = EvolveByLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvolveByLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvolveByLevelRequest) ProtoMessage() {}

func (x *EvolveByLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvolveByLevelRequest.ProtoReflect.Descriptor instead.
func (*EvolveByLevelRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{3}
}

func (x *EvolveByLevelRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *EvolveByLevelRequest) GetTargetEvolution() string {
	if x != nil {
		return x.TargetEvolution
	}
	return ""
}

var File_MainServer_BreedPoke_proto protoreflect.FileDescriptor

var file_MainServer_BreedPoke_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xfe, 0x01, 0x0a, 0x13, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x6f, 0x6b,
	0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x72, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x52, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x72, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x52, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x72, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xca, 0x01, 0x0a, 0x0d, 0x42, 0x72, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x43, 0x68, 0x61,
	0x72, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x5f, 0x64, 0x72, 0x75, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x44, 0x72, 0x75,
	0x67, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6d, 0x6d, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6d, 0x6d, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x72, 0x6d, 0x12, 0x33, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x7e, 0x0a,
	0x14, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x62, 0x61, 0x62, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x04, 0x62, 0x61, 0x62, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x69, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x44, 0x69, 0x65, 0x64, 0x22, 0x5a, 0x0a,
	0x14, 0x45, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x45, 0x76, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_MainServer_BreedPoke_proto_rawDescOnce sync.Once
	file_MainServer_BreedPoke_proto_rawDescData = file_MainServer_BreedPoke_proto_rawDesc
)

func file_MainServer_BreedPoke_proto_rawDescGZIP() []byte {
	file_MainServer_BreedPoke_proto_rawDescOnce.Do(func() {
		file_MainServer_BreedPoke_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_BreedPoke_proto_rawDescData)
	})
	return file_MainServer_BreedPoke_proto_rawDescData
}

var file_MainServer_BreedPoke_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_BreedPoke_proto_goTypes = []any{
	(*BreedPokemonRequest)(nil),  // 0: MainServer.BreedPokemonRequest
	(*BreedingItems)(nil),        // 1: MainServer.BreedingItems
	(*BreedPokemonResponse)(nil), // 2: MainServer.BreedPokemonResponse
	(*EvolveByLevelRequest)(nil), // 3: MainServer.EvolveByLevelRequest
	(*PokeStat)(nil),             // 4: MainServer.PokeStat
	(*Poke)(nil),                 // 5: MainServer.Poke
}
var file_MainServer_BreedPoke_proto_depIdxs = []int32{
	1, // 0: MainServer.BreedPokemonRequest.father_items:type_name -> MainServer.BreedingItems
	1, // 1: MainServer.BreedPokemonRequest.mother_items:type_name -> MainServer.BreedingItems
	4, // 2: MainServer.BreedingItems.stat_items:type_name -> MainServer.PokeStat
	5, // 3: MainServer.BreedPokemonResponse.baby:type_name -> MainServer.Poke
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_BreedPoke_proto_init() }
func file_MainServer_BreedPoke_proto_init() {
	if File_MainServer_BreedPoke_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_BreedPoke_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BreedPokemonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*BreedingItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*BreedPokemonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*EvolveByLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_BreedPoke_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BreedPoke_proto_goTypes,
		DependencyIndexes: file_MainServer_BreedPoke_proto_depIdxs,
		MessageInfos:      file_MainServer_BreedPoke_proto_msgTypes,
	}.Build()
	File_MainServer_BreedPoke_proto = out.File
	file_MainServer_BreedPoke_proto_rawDesc = nil
	file_MainServer_BreedPoke_proto_goTypes = nil
	file_MainServer_BreedPoke_proto_depIdxs = nil
}
