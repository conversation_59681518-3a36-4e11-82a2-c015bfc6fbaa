// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/BreedPoke.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BreedPokemonRequest - 繁殖宝可梦请求
type BreedPokemonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeInfo_1 *SimpleBoxPokeInfo `protobuf:"bytes,1,opt,name=poke_info_1,json=pokeInfo1,proto3" json:"poke_info_1,omitempty"`   // 父方宝可梦ID
	PokeInfo_2 *SimpleBoxPokeInfo `protobuf:"bytes,2,opt,name=poke_info_2,json=pokeInfo2,proto3" json:"poke_info_2,omitempty"`   // 母方宝可梦ID
	Items_1    *BreedingItems     `protobuf:"bytes,3,opt,name=items_1,json=items1,proto3" json:"items_1,omitempty"`              // 父方携带的繁殖道具
	Items_2    *BreedingItems     `protobuf:"bytes,4,opt,name=items_2,json=items2,proto3" json:"items_2,omitempty"`              // 母方携带的繁殖道具
	BreedCount int32              `protobuf:"varint,5,opt,name=breed_count,json=breedCount,proto3" json:"breed_count,omitempty"` // 繁殖次数，影响死亡概率
}

func (x *BreedPokemonRequest) Reset() {
	*x = BreedPokemonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedPokemonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedPokemonRequest) ProtoMessage() {}

func (x *BreedPokemonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedPokemonRequest.ProtoReflect.Descriptor instead.
func (*BreedPokemonRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{0}
}

func (x *BreedPokemonRequest) GetPokeInfo_1() *SimpleBoxPokeInfo {
	if x != nil {
		return x.PokeInfo_1
	}
	return nil
}

func (x *BreedPokemonRequest) GetPokeInfo_2() *SimpleBoxPokeInfo {
	if x != nil {
		return x.PokeInfo_2
	}
	return nil
}

func (x *BreedPokemonRequest) GetItems_1() *BreedingItems {
	if x != nil {
		return x.Items_1
	}
	return nil
}

func (x *BreedPokemonRequest) GetItems_2() *BreedingItems {
	if x != nil {
		return x.Items_2
	}
	return nil
}

func (x *BreedPokemonRequest) GetBreedCount() int32 {
	if x != nil {
		return x.BreedCount
	}
	return 0
}

type SimpleBoxPokeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId      int64       `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	PokeBox     int32       `protobuf:"varint,2,opt,name=poke_box,json=pokeBox,proto3" json:"poke_box,omitempty"` //PokeBox.index
	PokeBoxType PokeBoxType `protobuf:"varint,3,opt,name=poke_box_type,json=pokeBoxType,proto3,enum=MainServer.PokeBoxType" json:"poke_box_type,omitempty"`
	PokeBoxLoc  int32       `protobuf:"varint,4,opt,name=poke_box_loc,json=pokeBoxLoc,proto3" json:"poke_box_loc,omitempty"`
}

func (x *SimpleBoxPokeInfo) Reset() {
	*x = SimpleBoxPokeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimpleBoxPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleBoxPokeInfo) ProtoMessage() {}

func (x *SimpleBoxPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleBoxPokeInfo.ProtoReflect.Descriptor instead.
func (*SimpleBoxPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{1}
}

func (x *SimpleBoxPokeInfo) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *SimpleBoxPokeInfo) GetPokeBox() int32 {
	if x != nil {
		return x.PokeBox
	}
	return 0
}

func (x *SimpleBoxPokeInfo) GetPokeBoxType() PokeBoxType {
	if x != nil {
		return x.PokeBoxType
	}
	return PokeBoxType_normal
}

func (x *SimpleBoxPokeInfo) GetPokeBoxLoc() int32 {
	if x != nil {
		return x.PokeBoxLoc
	}
	return 0
}

// BreedingItems - 繁殖道具
type BreedingItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedString     bool      `protobuf:"varint,1,opt,name=red_string,json=redString,proto3" json:"red_string,omitempty"`             // 红线，用于传递特性
	ShinyCharm    bool      `protobuf:"varint,2,opt,name=shiny_charm,json=shinyCharm,proto3" json:"shiny_charm,omitempty"`          // 闪耀护符，提高生产闪光宝可梦的概率
	ShinyDrug     bool      `protobuf:"varint,3,opt,name=shiny_drug,json=shinyDrug,proto3" json:"shiny_drug,omitempty"`             // 闪光药，提高生产闪光宝可梦的概率
	ImmortalCharm bool      `protobuf:"varint,4,opt,name=immortal_charm,json=immortalCharm,proto3" json:"immortal_charm,omitempty"` // 不死护符，防止宝可梦死亡
	StatItems     *PokeStat `protobuf:"bytes,5,opt,name=stat_items,json=statItems,proto3" json:"stat_items,omitempty"`              // 能力值遗传道具
}

func (x *BreedingItems) Reset() {
	*x = BreedingItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedingItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedingItems) ProtoMessage() {}

func (x *BreedingItems) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedingItems.ProtoReflect.Descriptor instead.
func (*BreedingItems) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{2}
}

func (x *BreedingItems) GetRedString() bool {
	if x != nil {
		return x.RedString
	}
	return false
}

func (x *BreedingItems) GetShinyCharm() bool {
	if x != nil {
		return x.ShinyCharm
	}
	return false
}

func (x *BreedingItems) GetShinyDrug() bool {
	if x != nil {
		return x.ShinyDrug
	}
	return false
}

func (x *BreedingItems) GetImmortalCharm() bool {
	if x != nil {
		return x.ImmortalCharm
	}
	return false
}

func (x *BreedingItems) GetStatItems() *PokeStat {
	if x != nil {
		return x.StatItems
	}
	return nil
}

// BreedPokemonResponse - 繁殖宝可梦响应
type BreedPokemonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Baby       *Poke `protobuf:"bytes,1,opt,name=baby,proto3" json:"baby,omitempty"`                                // 新生宝可梦
	FatherDied bool  `protobuf:"varint,2,opt,name=father_died,json=fatherDied,proto3" json:"father_died,omitempty"` // 父方是否死亡
	MotherDied bool  `protobuf:"varint,3,opt,name=mother_died,json=motherDied,proto3" json:"mother_died,omitempty"` // 母方是否死亡
	FatherId   int64 `protobuf:"varint,4,opt,name=father_id,json=fatherId,proto3" json:"father_id,omitempty"`       // 父方id
	MotherId   int64 `protobuf:"varint,5,opt,name=mother_id,json=motherId,proto3" json:"mother_id,omitempty"`       // 母方id
}

func (x *BreedPokemonResponse) Reset() {
	*x = BreedPokemonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BreedPokemonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BreedPokemonResponse) ProtoMessage() {}

func (x *BreedPokemonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BreedPokemonResponse.ProtoReflect.Descriptor instead.
func (*BreedPokemonResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{3}
}

func (x *BreedPokemonResponse) GetBaby() *Poke {
	if x != nil {
		return x.Baby
	}
	return nil
}

func (x *BreedPokemonResponse) GetFatherDied() bool {
	if x != nil {
		return x.FatherDied
	}
	return false
}

func (x *BreedPokemonResponse) GetMotherDied() bool {
	if x != nil {
		return x.MotherDied
	}
	return false
}

func (x *BreedPokemonResponse) GetFatherId() int64 {
	if x != nil {
		return x.FatherId
	}
	return 0
}

func (x *BreedPokemonResponse) GetMotherId() int64 {
	if x != nil {
		return x.MotherId
	}
	return 0
}

// EvolveByLevelRequest - 等级进化请求
type EvolveByLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId          int64  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`                           // 宝可梦ID
	TargetEvolution string `protobuf:"bytes,2,opt,name=target_evolution,json=targetEvolution,proto3" json:"target_evolution,omitempty"` // 目标进化形态
}

func (x *EvolveByLevelRequest) Reset() {
	*x = EvolveByLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvolveByLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvolveByLevelRequest) ProtoMessage() {}

func (x *EvolveByLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvolveByLevelRequest.ProtoReflect.Descriptor instead.
func (*EvolveByLevelRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{4}
}

func (x *EvolveByLevelRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *EvolveByLevelRequest) GetTargetEvolution() string {
	if x != nil {
		return x.TargetEvolution
	}
	return ""
}

type HatchEggRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId    int64              `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`   // 宝可梦ID
	EggInfo   *SimpleBoxPokeInfo `protobuf:"bytes,2,opt,name=egg_info,json=eggInfo,proto3" json:"egg_info,omitempty"` // 卵信息
	TakeOut   bool               `protobuf:"varint,3,opt,name=take_out,json=takeOut,proto3" json:"take_out,omitempty"`
	IsSpecial bool               `protobuf:"varint,4,opt,name=is_special,json=isSpecial,proto3" json:"is_special,omitempty"`
}

func (x *HatchEggRequest) Reset() {
	*x = HatchEggRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_BreedPoke_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HatchEggRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HatchEggRequest) ProtoMessage() {}

func (x *HatchEggRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BreedPoke_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HatchEggRequest.ProtoReflect.Descriptor instead.
func (*HatchEggRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_BreedPoke_proto_rawDescGZIP(), []int{5}
}

func (x *HatchEggRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *HatchEggRequest) GetEggInfo() *SimpleBoxPokeInfo {
	if x != nil {
		return x.EggInfo
	}
	return nil
}

func (x *HatchEggRequest) GetTakeOut() bool {
	if x != nil {
		return x.TakeOut
	}
	return false
}

func (x *HatchEggRequest) GetIsSpecial() bool {
	if x != nil {
		return x.IsSpecial
	}
	return false
}

var File_MainServer_BreedPoke_proto protoreflect.FileDescriptor

var file_MainServer_BreedPoke_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65,
	0x42, 0x6f, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x02, 0x0a, 0x13, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x31,
	0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x32, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x32, 0x12,
	0x32, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x5f, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x06, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x31, 0x12, 0x32, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x5f, 0x32, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x42, 0x72, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52,
	0x06, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa6, 0x01, 0x0a, 0x11, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x6b, 0x65, 0x5f,
	0x62, 0x6f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x6f, 0x6b, 0x65, 0x42,
	0x6f, 0x78, 0x12, 0x3b, 0x0a, 0x0d, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x70, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0c, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x6c, 0x6f, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x4c, 0x6f,
	0x63, 0x22, 0xca, 0x01, 0x0a, 0x0d, 0x42, 0x72, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x43, 0x68,
	0x61, 0x72, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x5f, 0x64, 0x72, 0x75,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x44, 0x72,
	0x75, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6d, 0x6d, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6d, 0x6d, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x72, 0x6d, 0x12, 0x33, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xb8,
	0x01, 0x0a, 0x14, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x62, 0x61, 0x62, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x04, 0x62, 0x61, 0x62, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x69, 0x65, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x44, 0x69, 0x65, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x14, 0x45, 0x76, 0x6f,
	0x6c, 0x76, 0x65, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x76, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9e, 0x01, 0x0a, 0x0f, 0x48, 0x61, 0x74, 0x63, 0x68, 0x45,
	0x67, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6b,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6b, 0x65,
	0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x65, 0x67, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x65, 0x67, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x61, 0x6b, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x74, 0x61, 0x6b, 0x65, 0x4f, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_BreedPoke_proto_rawDescOnce sync.Once
	file_MainServer_BreedPoke_proto_rawDescData = file_MainServer_BreedPoke_proto_rawDesc
)

func file_MainServer_BreedPoke_proto_rawDescGZIP() []byte {
	file_MainServer_BreedPoke_proto_rawDescOnce.Do(func() {
		file_MainServer_BreedPoke_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_BreedPoke_proto_rawDescData)
	})
	return file_MainServer_BreedPoke_proto_rawDescData
}

var file_MainServer_BreedPoke_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_BreedPoke_proto_goTypes = []any{
	(*BreedPokemonRequest)(nil),  // 0: MainServer.BreedPokemonRequest
	(*SimpleBoxPokeInfo)(nil),    // 1: MainServer.SimpleBoxPokeInfo
	(*BreedingItems)(nil),        // 2: MainServer.BreedingItems
	(*BreedPokemonResponse)(nil), // 3: MainServer.BreedPokemonResponse
	(*EvolveByLevelRequest)(nil), // 4: MainServer.EvolveByLevelRequest
	(*HatchEggRequest)(nil),      // 5: MainServer.HatchEggRequest
	(PokeBoxType)(0),             // 6: MainServer.PokeBoxType
	(*PokeStat)(nil),             // 7: MainServer.PokeStat
	(*Poke)(nil),                 // 8: MainServer.Poke
}
var file_MainServer_BreedPoke_proto_depIdxs = []int32{
	1, // 0: MainServer.BreedPokemonRequest.poke_info_1:type_name -> MainServer.SimpleBoxPokeInfo
	1, // 1: MainServer.BreedPokemonRequest.poke_info_2:type_name -> MainServer.SimpleBoxPokeInfo
	2, // 2: MainServer.BreedPokemonRequest.items_1:type_name -> MainServer.BreedingItems
	2, // 3: MainServer.BreedPokemonRequest.items_2:type_name -> MainServer.BreedingItems
	6, // 4: MainServer.SimpleBoxPokeInfo.poke_box_type:type_name -> MainServer.PokeBoxType
	7, // 5: MainServer.BreedingItems.stat_items:type_name -> MainServer.PokeStat
	8, // 6: MainServer.BreedPokemonResponse.baby:type_name -> MainServer.Poke
	1, // 7: MainServer.HatchEggRequest.egg_info:type_name -> MainServer.SimpleBoxPokeInfo
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_MainServer_BreedPoke_proto_init() }
func file_MainServer_BreedPoke_proto_init() {
	if File_MainServer_BreedPoke_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_PokeBox_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_BreedPoke_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BreedPokemonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SimpleBoxPokeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*BreedingItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*BreedPokemonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*EvolveByLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_BreedPoke_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*HatchEggRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_BreedPoke_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BreedPoke_proto_goTypes,
		DependencyIndexes: file_MainServer_BreedPoke_proto_depIdxs,
		MessageInfos:      file_MainServer_BreedPoke_proto_msgTypes,
	}.Build()
	File_MainServer_BreedPoke_proto = out.File
	file_MainServer_BreedPoke_proto_rawDesc = nil
	file_MainServer_BreedPoke_proto_goTypes = nil
	file_MainServer_BreedPoke_proto_depIdxs = nil
}
