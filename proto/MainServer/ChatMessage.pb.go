// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/ChatMessage.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 枚举：消息类型
type ChatMessageType int32

const (
	ChatMessageType_TEXT  ChatMessageType = 0
	ChatMessageType_IMAGE ChatMessageType = 1
	ChatMessageType_VIDEO ChatMessageType = 2
)

// Enum value maps for ChatMessageType.
var (
	ChatMessageType_name = map[int32]string{
		0: "TEXT",
		1: "IMAGE",
		2: "VIDEO",
	}
	ChatMessageType_value = map[string]int32{
		"TEXT":  0,
		"IMAGE": 1,
		"VIDEO": 2,
	}
)

func (x ChatMessageType) Enum() *ChatMessageType {
	p := new(ChatMessageType)
	*p = x
	return p
}

func (x ChatMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ChatMessage_proto_enumTypes[0].Descriptor()
}

func (ChatMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_ChatMessage_proto_enumTypes[0]
}

func (x ChatMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatMessageType.Descriptor instead.
func (ChatMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

// 消息作者定义
type ChatMessageAuthor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Tid  int64  `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"` // 注意：isSelf 是派生字段，不应在 protobuf 中定义，因为它是运行时计算得出的。
}

func (x *ChatMessageAuthor) Reset() {
	*x = ChatMessageAuthor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessageAuthor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageAuthor) ProtoMessage() {}

func (x *ChatMessageAuthor) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageAuthor.ProtoReflect.Descriptor instead.
func (*ChatMessageAuthor) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

func (x *ChatMessageAuthor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatMessageAuthor) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ChatMessageAuthor) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

// 聊天消息定义
type ChatMessageModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text   string             `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Time   string             `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	Type   ChatMessageType    `protobuf:"varint,3,opt,name=type,proto3,enum=ChatMessage.ChatMessageType" json:"type,omitempty"` // 默认值为 TEXT，可在服务端/客户端处理
	Author *ChatMessageAuthor `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
}

func (x *ChatMessageModel) Reset() {
	*x = ChatMessageModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageModel) ProtoMessage() {}

func (x *ChatMessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageModel.ProtoReflect.Descriptor instead.
func (*ChatMessageModel) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{1}
}

func (x *ChatMessageModel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ChatMessageModel) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *ChatMessageModel) GetType() ChatMessageType {
	if x != nil {
		return x.Type
	}
	return ChatMessageType_TEXT
}

func (x *ChatMessageModel) GetAuthor() *ChatMessageAuthor {
	if x != nil {
		return x.Author
	}
	return nil
}

var File_MainServer_ChatMessage_proto protoreflect.FileDescriptor

var file_MainServer_ChatMessage_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x43, 0x68, 0x61,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x4d, 0x0a, 0x11, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x22, 0xa4, 0x01, 0x0a, 0x10, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x2a, 0x31, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_ChatMessage_proto_rawDescOnce sync.Once
	file_MainServer_ChatMessage_proto_rawDescData = file_MainServer_ChatMessage_proto_rawDesc
)

func file_MainServer_ChatMessage_proto_rawDescGZIP() []byte {
	file_MainServer_ChatMessage_proto_rawDescOnce.Do(func() {
		file_MainServer_ChatMessage_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_ChatMessage_proto_rawDescData)
	})
	return file_MainServer_ChatMessage_proto_rawDescData
}

var file_MainServer_ChatMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ChatMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_ChatMessage_proto_goTypes = []any{
	(ChatMessageType)(0),      // 0: ChatMessage.ChatMessageType
	(*ChatMessageAuthor)(nil), // 1: ChatMessage.ChatMessageAuthor
	(*ChatMessageModel)(nil),  // 2: ChatMessage.ChatMessageModel
}
var file_MainServer_ChatMessage_proto_depIdxs = []int32{
	0, // 0: ChatMessage.ChatMessageModel.type:type_name -> ChatMessage.ChatMessageType
	1, // 1: ChatMessage.ChatMessageModel.author:type_name -> ChatMessage.ChatMessageAuthor
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_ChatMessage_proto_init() }
func file_MainServer_ChatMessage_proto_init() {
	if File_MainServer_ChatMessage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_ChatMessage_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ChatMessageAuthor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ChatMessage_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ChatMessageModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_ChatMessage_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ChatMessage_proto_goTypes,
		DependencyIndexes: file_MainServer_ChatMessage_proto_depIdxs,
		EnumInfos:         file_MainServer_ChatMessage_proto_enumTypes,
		MessageInfos:      file_MainServer_ChatMessage_proto_msgTypes,
	}.Build()
	File_MainServer_ChatMessage_proto = out.File
	file_MainServer_ChatMessage_proto_rawDesc = nil
	file_MainServer_ChatMessage_proto_goTypes = nil
	file_MainServer_ChatMessage_proto_depIdxs = nil
}
