// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Email.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmailStatus int32

const (
	EmailStatus_EMAIL_STATUS_UNREAD              EmailStatus = 0
	EmailStatus_EMAIL_STATUS_READ                EmailStatus = 1
	EmailStatus_EMAIL_STATUS_DELETED             EmailStatus = 2
	EmailStatus_EMAIL_STATUS_RECEIVED_ATTACHMENT EmailStatus = 3
)

// Enum value maps for EmailStatus.
var (
	EmailStatus_name = map[int32]string{
		0: "EMAIL_STATUS_UNREAD",
		1: "EMAIL_STATUS_READ",
		2: "EMAIL_STATUS_DELETED",
		3: "EMAIL_STATUS_RECEIVED_ATTACHMENT",
	}
	EmailStatus_value = map[string]int32{
		"EMAIL_STATUS_UNREAD":              0,
		"EMAIL_STATUS_READ":                1,
		"EMAIL_STATUS_DELETED":             2,
		"EMAIL_STATUS_RECEIVED_ATTACHMENT": 3,
	}
)

func (x EmailStatus) Enum() *EmailStatus {
	p := new(EmailStatus)
	*p = x
	return p
}

func (x EmailStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[0].Descriptor()
}

func (EmailStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[0]
}

func (x EmailStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailStatus.Descriptor instead.
func (EmailStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{0}
}

type EmailOp int32

const (
	EmailOp_EMAIL_OP_READ               EmailOp = 0
	EmailOp_EMAIL_OP_DELETE             EmailOp = 1
	EmailOp_EMAIL_OP_RECEIVE_ATTACHMENT EmailOp = 2
)

// Enum value maps for EmailOp.
var (
	EmailOp_name = map[int32]string{
		0: "EMAIL_OP_READ",
		1: "EMAIL_OP_DELETE",
		2: "EMAIL_OP_RECEIVE_ATTACHMENT",
	}
	EmailOp_value = map[string]int32{
		"EMAIL_OP_READ":               0,
		"EMAIL_OP_DELETE":             1,
		"EMAIL_OP_RECEIVE_ATTACHMENT": 2,
	}
)

func (x EmailOp) Enum() *EmailOp {
	p := new(EmailOp)
	*p = x
	return p
}

func (x EmailOp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailOp) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Email_proto_enumTypes[1].Descriptor()
}

func (EmailOp) Type() protoreflect.EnumType {
	return &file_MainServer_Email_proto_enumTypes[1]
}

func (x EmailOp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailOp.Descriptor instead.
func (EmailOp) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{1}
}

type EmailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SenderId    int64            `protobuf:"varint,2,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	ReceiverId  int64            `protobuf:"varint,3,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	Contents    []*EmailLanguage `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty"`
	SendTime    int64            `protobuf:"varint,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ReadTime    int64            `protobuf:"varint,6,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
	Status      EmailStatus      `protobuf:"varint,7,opt,name=status,proto3,enum=MainServer.EmailStatus" json:"status,omitempty"`
	Attachments *EmailAttachment `protobuf:"bytes,8,opt,name=attachments,proto3" json:"attachments,omitempty"`
}

func (x *EmailInfo) Reset() {
	*x = EmailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailInfo) ProtoMessage() {}

func (x *EmailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailInfo.ProtoReflect.Descriptor instead.
func (*EmailInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{0}
}

func (x *EmailInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EmailInfo) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *EmailInfo) GetReceiverId() int64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *EmailInfo) GetContents() []*EmailLanguage {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *EmailInfo) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *EmailInfo) GetReadTime() int64 {
	if x != nil {
		return x.ReadTime
	}
	return 0
}

func (x *EmailInfo) GetStatus() EmailStatus {
	if x != nil {
		return x.Status
	}
	return EmailStatus_EMAIL_STATUS_UNREAD
}

func (x *EmailInfo) GetAttachments() *EmailAttachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

type EmailLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content  string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *EmailLanguage) Reset() {
	*x = EmailLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailLanguage) ProtoMessage() {}

func (x *EmailLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailLanguage.ProtoReflect.Descriptor instead.
func (*EmailLanguage) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{1}
}

func (x *EmailLanguage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EmailLanguage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EmailLanguage) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type EmailAttachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pokemons []*Poke         `protobuf:"bytes,1,rep,name=pokemons,proto3" json:"pokemons,omitempty"`
	Items    []*AddItemParam `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`     // 道具 参数定义在Inventory
	Clothes  []*TrainerCloth `protobuf:"bytes,3,rep,name=clothes,proto3" json:"clothes,omitempty"` //服饰
}

func (x *EmailAttachment) Reset() {
	*x = EmailAttachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailAttachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailAttachment) ProtoMessage() {}

func (x *EmailAttachment) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailAttachment.ProtoReflect.Descriptor instead.
func (*EmailAttachment) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{2}
}

func (x *EmailAttachment) GetPokemons() []*Poke {
	if x != nil {
		return x.Pokemons
	}
	return nil
}

func (x *EmailAttachment) GetItems() []*AddItemParam {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *EmailAttachment) GetClothes() []*TrainerCloth {
	if x != nil {
		return x.Clothes
	}
	return nil
}

type EmailOpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmailIds []int64 `protobuf:"varint,1,rep,packed,name=email_ids,json=emailIds,proto3" json:"email_ids,omitempty"`
	Op       EmailOp `protobuf:"varint,2,opt,name=op,proto3,enum=MainServer.EmailOp" json:"op,omitempty"`
}

func (x *EmailOpRequest) Reset() {
	*x = EmailOpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailOpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailOpRequest) ProtoMessage() {}

func (x *EmailOpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailOpRequest.ProtoReflect.Descriptor instead.
func (*EmailOpRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{3}
}

func (x *EmailOpRequest) GetEmailIds() []int64 {
	if x != nil {
		return x.EmailIds
	}
	return nil
}

func (x *EmailOpRequest) GetOp() EmailOp {
	if x != nil {
		return x.Op
	}
	return EmailOp_EMAIL_OP_READ
}

type EmailListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Emails []*EmailInfo `protobuf:"bytes,1,rep,name=emails,proto3" json:"emails,omitempty"`
}

func (x *EmailListResponse) Reset() {
	*x = EmailListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailListResponse) ProtoMessage() {}

func (x *EmailListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailListResponse.ProtoReflect.Descriptor instead.
func (*EmailListResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{4}
}

func (x *EmailListResponse) GetEmails() []*EmailInfo {
	if x != nil {
		return x.Emails
	}
	return nil
}

// 邮件列表请求
type EmailListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                                    // 页码
	PageSize    int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`            // 每页数量
	IsFilterAll bool  `protobuf:"varint,3,opt,name=is_filter_all,json=isFilterAll,proto3" json:"is_filter_all,omitempty"` // 是否过滤全部
}

func (x *EmailListRequest) Reset() {
	*x = EmailListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailListRequest) ProtoMessage() {}

func (x *EmailListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailListRequest.ProtoReflect.Descriptor instead.
func (*EmailListRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{5}
}

func (x *EmailListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *EmailListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *EmailListRequest) GetIsFilterAll() bool {
	if x != nil {
		return x.IsFilterAll
	}
	return false
}

// 邮件通知
type EmailNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email *EmailInfo `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"` // 邮件信息
}

func (x *EmailNotification) Reset() {
	*x = EmailNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Email_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailNotification) ProtoMessage() {}

func (x *EmailNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Email_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailNotification.ProtoReflect.Descriptor instead.
func (*EmailNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_Email_proto_rawDescGZIP(), []int{6}
}

func (x *EmailNotification) GetEmail() *EmailInfo {
	if x != nil {
		return x.Email
	}
	return nil
}

var File_MainServer_Email_proto protoreflect.FileDescriptor

var file_MainServer_Email_proto_rawDesc = []byte{
	0x0a, 0x16, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x02, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0x5b, 0x0a, 0x0d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x22, 0xa3, 0x01, 0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x08, 0x70, 0x6f, 0x6b, 0x65, 0x6d, 0x6f,
	0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x07, 0x63,
	0x6c, 0x6f, 0x74, 0x68, 0x65, 0x73, 0x22, 0x52, 0x0a, 0x0e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4f,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x52, 0x02, 0x6f, 0x70, 0x22, 0x42, 0x0a, 0x11, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x67,
	0x0a, 0x10, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x22, 0x40, 0x0a, 0x11, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2a, 0x7d, 0x0a, 0x0b, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x52, 0x45, 0x41, 0x44, 0x10,
	0x00, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x54, 0x41,
	0x43, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x2a, 0x52, 0x0a, 0x07, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4f, 0x70, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x50, 0x5f,
	0x52, 0x45, 0x41, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x4f, 0x50, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4f, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x5f,
	0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Email_proto_rawDescOnce sync.Once
	file_MainServer_Email_proto_rawDescData = file_MainServer_Email_proto_rawDesc
)

func file_MainServer_Email_proto_rawDescGZIP() []byte {
	file_MainServer_Email_proto_rawDescOnce.Do(func() {
		file_MainServer_Email_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Email_proto_rawDescData)
	})
	return file_MainServer_Email_proto_rawDescData
}

var file_MainServer_Email_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_Email_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_MainServer_Email_proto_goTypes = []any{
	(EmailStatus)(0),          // 0: MainServer.EmailStatus
	(EmailOp)(0),              // 1: MainServer.EmailOp
	(*EmailInfo)(nil),         // 2: MainServer.EmailInfo
	(*EmailLanguage)(nil),     // 3: MainServer.EmailLanguage
	(*EmailAttachment)(nil),   // 4: MainServer.EmailAttachment
	(*EmailOpRequest)(nil),    // 5: MainServer.EmailOpRequest
	(*EmailListResponse)(nil), // 6: MainServer.EmailListResponse
	(*EmailListRequest)(nil),  // 7: MainServer.EmailListRequest
	(*EmailNotification)(nil), // 8: MainServer.EmailNotification
	(*Poke)(nil),              // 9: MainServer.Poke
	(*AddItemParam)(nil),      // 10: MainServer.AddItemParam
	(*TrainerCloth)(nil),      // 11: MainServer.TrainerCloth
}
var file_MainServer_Email_proto_depIdxs = []int32{
	3,  // 0: MainServer.EmailInfo.contents:type_name -> MainServer.EmailLanguage
	0,  // 1: MainServer.EmailInfo.status:type_name -> MainServer.EmailStatus
	4,  // 2: MainServer.EmailInfo.attachments:type_name -> MainServer.EmailAttachment
	9,  // 3: MainServer.EmailAttachment.pokemons:type_name -> MainServer.Poke
	10, // 4: MainServer.EmailAttachment.items:type_name -> MainServer.AddItemParam
	11, // 5: MainServer.EmailAttachment.clothes:type_name -> MainServer.TrainerCloth
	1,  // 6: MainServer.EmailOpRequest.op:type_name -> MainServer.EmailOp
	2,  // 7: MainServer.EmailListResponse.emails:type_name -> MainServer.EmailInfo
	2,  // 8: MainServer.EmailNotification.email:type_name -> MainServer.EmailInfo
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_MainServer_Email_proto_init() }
func file_MainServer_Email_proto_init() {
	if File_MainServer_Email_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Email_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EmailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EmailLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*EmailAttachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*EmailOpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*EmailListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*EmailListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Email_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*EmailNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Email_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Email_proto_goTypes,
		DependencyIndexes: file_MainServer_Email_proto_depIdxs,
		EnumInfos:         file_MainServer_Email_proto_enumTypes,
		MessageInfos:      file_MainServer_Email_proto_msgTypes,
	}.Build()
	File_MainServer_Email_proto = out.File
	file_MainServer_Email_proto_rawDesc = nil
	file_MainServer_Email_proto_goTypes = nil
	file_MainServer_Email_proto_depIdxs = nil
}
