// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Inventory.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InventoryType int32

const (
	InventoryType_inventory_nor       InventoryType = 0 //喷雾剂之类的
	InventoryType_inventory_poke      InventoryType = 1
	InventoryType_inventory_ball      InventoryType = 2 //精灵球
	InventoryType_inventory_battlesys InventoryType = 3 //mege & z
	InventoryType_inventory_healing   InventoryType = 4 //回复
	InventoryType_inventory_berry     InventoryType = 5 //果实
	InventoryType_inventory_box       InventoryType = 6 //礼盒之类的
	InventoryType_inventory_carry     InventoryType = 7 //携带道具
	InventoryType_inventory_cultivate InventoryType = 8 //进化，努力值，特性，性格，技能 极巨化培育 太晶化碎片
)

// Enum value maps for InventoryType.
var (
	InventoryType_name = map[int32]string{
		0: "inventory_nor",
		1: "inventory_poke",
		2: "inventory_ball",
		3: "inventory_battlesys",
		4: "inventory_healing",
		5: "inventory_berry",
		6: "inventory_box",
		7: "inventory_carry",
		8: "inventory_cultivate",
	}
	InventoryType_value = map[string]int32{
		"inventory_nor":       0,
		"inventory_poke":      1,
		"inventory_ball":      2,
		"inventory_battlesys": 3,
		"inventory_healing":   4,
		"inventory_berry":     5,
		"inventory_box":       6,
		"inventory_carry":     7,
		"inventory_cultivate": 8,
	}
)

func (x InventoryType) Enum() *InventoryType {
	p := new(InventoryType)
	*p = x
	return p
}

func (x InventoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Inventory_proto_enumTypes[0].Descriptor()
}

func (InventoryType) Type() protoreflect.EnumType {
	return &file_MainServer_Inventory_proto_enumTypes[0]
}

func (x InventoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryType.Descriptor instead.
func (InventoryType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{0}
}

// 库存状态枚举
type InventoryStatus int32

const (
	InventoryStatus_InventoryStatus_Unknown InventoryStatus = 0
	InventoryStatus_InventoryStatus_Normal  InventoryStatus = 1 // 普通状态
	InventoryStatus_InventoryStatus_Sale    InventoryStatus = 2 // 上架状态
)

// Enum value maps for InventoryStatus.
var (
	InventoryStatus_name = map[int32]string{
		0: "InventoryStatus_Unknown",
		1: "InventoryStatus_Normal",
		2: "InventoryStatus_Sale",
	}
	InventoryStatus_value = map[string]int32{
		"InventoryStatus_Unknown": 0,
		"InventoryStatus_Normal":  1,
		"InventoryStatus_Sale":    2,
	}
)

func (x InventoryStatus) Enum() *InventoryStatus {
	p := new(InventoryStatus)
	*p = x
	return p
}

func (x InventoryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Inventory_proto_enumTypes[1].Descriptor()
}

func (InventoryStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Inventory_proto_enumTypes[1]
}

func (x InventoryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryStatus.Descriptor instead.
func (InventoryStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{1}
}

type InventorySlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId            string `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName          string `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity          int32  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Index             int32  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	MaximumStack      int32  `protobuf:"varint,5,opt,name=maximum_stack,json=maximumStack,proto3" json:"maximum_stack,omitempty"`
	TargetInventoryId int64  `protobuf:"varint,6,opt,name=target_inventory_id,json=targetInventoryId,proto3" json:"target_inventory_id,omitempty"`
	Price             int64  `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`                                //给商店用的
	Contribution      int64  `protobuf:"varint,8,opt,name=contribution,proto3" json:"contribution,omitempty"`                  //给商店用的
	SpecialCoin       int64  `protobuf:"varint,9,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"` //给商店用的
}

func (x *InventorySlot) Reset() {
	*x = InventorySlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InventorySlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorySlot) ProtoMessage() {}

func (x *InventorySlot) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorySlot.ProtoReflect.Descriptor instead.
func (*InventorySlot) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{0}
}

func (x *InventorySlot) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

func (x *InventorySlot) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *InventorySlot) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *InventorySlot) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *InventorySlot) GetMaximumStack() int32 {
	if x != nil {
		return x.MaximumStack
	}
	return 0
}

func (x *InventorySlot) GetTargetInventoryId() int64 {
	if x != nil {
		return x.TargetInventoryId
	}
	return 0
}

func (x *InventorySlot) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *InventorySlot) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *InventorySlot) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

// 库存项目
type Inventory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                         // 库存ID
	Tid         int64           `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                       // 训练师ID
	ItemName    string          `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`              // 道具ID（道具名称）
	Quantity    int32           `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`                             // 数量
	Price       int32           `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`                                   // 价格
	SpecialCoin int32           `protobuf:"varint,6,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`    // 特殊货币
	Status      InventoryStatus `protobuf:"varint,7,opt,name=status,proto3,enum=MainServer.InventoryStatus" json:"status,omitempty"` // 状态
	CreateTs    int64           `protobuf:"varint,8,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`             // 创建时间戳
	UpdateTs    int64           `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`             // 更新时间戳
}

func (x *Inventory) Reset() {
	*x = Inventory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Inventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inventory) ProtoMessage() {}

func (x *Inventory) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inventory.ProtoReflect.Descriptor instead.
func (*Inventory) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{1}
}

func (x *Inventory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Inventory) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Inventory) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *Inventory) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *Inventory) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Inventory) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *Inventory) GetStatus() InventoryStatus {
	if x != nil {
		return x.Status
	}
	return InventoryStatus_InventoryStatus_Unknown
}

func (x *Inventory) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Inventory) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 使用道具请求参数
type UseItemParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"` // 道具ID（道具名称）
	Count    int32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                      // 使用数量
}

func (x *UseItemParam) Reset() {
	*x = UseItemParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemParam) ProtoMessage() {}

func (x *UseItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemParam.ProtoReflect.Descriptor instead.
func (*UseItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{2}
}

func (x *UseItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 添加道具请求参数
type AddItemParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"` // 道具ID（道具名称）
	Count    int32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                      // 添加数量
}

func (x *AddItemParam) Reset() {
	*x = AddItemParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddItemParam) ProtoMessage() {}

func (x *AddItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddItemParam.ProtoReflect.Descriptor instead.
func (*AddItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{3}
}

func (x *AddItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *AddItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 上架道具请求参数
type SaleItemParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName    string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`           // 道具ID（道具名称）
	Count       int32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                // 上架数量
	Price       int32  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                // 价格
	SpecialCoin int32  `protobuf:"varint,4,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"` // 特殊货币
}

func (x *SaleItemParam) Reset() {
	*x = SaleItemParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaleItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleItemParam) ProtoMessage() {}

func (x *SaleItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleItemParam.ProtoReflect.Descriptor instead.
func (*SaleItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{4}
}

func (x *SaleItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *SaleItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SaleItemParam) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleItemParam) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

// 下架道具请求参数
type UnsaleItemParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"` // 道具ID（道具名称）
	Count    int32  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                      // 下架数量
}

func (x *UnsaleItemParam) Reset() {
	*x = UnsaleItemParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsaleItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleItemParam) ProtoMessage() {}

func (x *UnsaleItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleItemParam.ProtoReflect.Descriptor instead.
func (*UnsaleItemParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{5}
}

func (x *UnsaleItemParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UnsaleItemParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 查询道具请求参数
type QueryItemsParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName string          `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`              // 道具ID（道具名称），空字符串表示查询所有道具
	Status   InventoryStatus `protobuf:"varint,2,opt,name=status,proto3,enum=MainServer.InventoryStatus" json:"status,omitempty"` // 状态，0表示查询所有状态
	UpdateTs int64           `protobuf:"varint,3,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`             // 更新时间戳，大于0时只查询更新时间大于此值的记录
}

func (x *QueryItemsParam) Reset() {
	*x = QueryItemsParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryItemsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemsParam) ProtoMessage() {}

func (x *QueryItemsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemsParam.ProtoReflect.Descriptor instead.
func (*QueryItemsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{6}
}

func (x *QueryItemsParam) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *QueryItemsParam) GetStatus() InventoryStatus {
	if x != nil {
		return x.Status
	}
	return InventoryStatus_InventoryStatus_Unknown
}

func (x *QueryItemsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 使用道具响应
type UseItemResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *UseItemResponse) Reset() {
	*x = UseItemResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemResponse) ProtoMessage() {}

func (x *UseItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemResponse.ProtoReflect.Descriptor instead.
func (*UseItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{7}
}

func (x *UseItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UseItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 添加道具响应
type AddItemResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *AddItemResponse) Reset() {
	*x = AddItemResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddItemResponse) ProtoMessage() {}

func (x *AddItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddItemResponse.ProtoReflect.Descriptor instead.
func (*AddItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{8}
}

func (x *AddItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 上架道具响应
type SaleItemResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *SaleItemResponse) Reset() {
	*x = SaleItemResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaleItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleItemResponse) ProtoMessage() {}

func (x *SaleItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleItemResponse.ProtoReflect.Descriptor instead.
func (*SaleItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{9}
}

func (x *SaleItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SaleItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 下架道具响应
type UnsaleItemResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *UnsaleItemResponse) Reset() {
	*x = UnsaleItemResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsaleItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleItemResponse) ProtoMessage() {}

func (x *UnsaleItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleItemResponse.ProtoReflect.Descriptor instead.
func (*UnsaleItemResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{10}
}

func (x *UnsaleItemResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UnsaleItemResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取所有道具请求参数
type GetAllItemsParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateTs int64 `protobuf:"varint,1,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳，大于0时只查询更新时间大于此值的记录
}

func (x *GetAllItemsParam) Reset() {
	*x = GetAllItemsParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllItemsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllItemsParam) ProtoMessage() {}

func (x *GetAllItemsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllItemsParam.ProtoReflect.Descriptor instead.
func (*GetAllItemsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{11}
}

func (x *GetAllItemsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 获取所有道具响应
type GetAllItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool         `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Items   []*Inventory `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`      // 道具列表
}

func (x *GetAllItemsResponse) Reset() {
	*x = GetAllItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllItemsResponse) ProtoMessage() {}

func (x *GetAllItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllItemsResponse.ProtoReflect.Descriptor instead.
func (*GetAllItemsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{12}
}

func (x *GetAllItemsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetAllItemsResponse) GetItems() []*Inventory {
	if x != nil {
		return x.Items
	}
	return nil
}

// 查询道具响应
type QueryItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool         `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Items   []*Inventory `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`      // 道具列表
}

func (x *QueryItemsResponse) Reset() {
	*x = QueryItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Inventory_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryItemsResponse) ProtoMessage() {}

func (x *QueryItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Inventory_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryItemsResponse.ProtoReflect.Descriptor instead.
func (*QueryItemsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Inventory_proto_rawDescGZIP(), []int{13}
}

func (x *QueryItemsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryItemsResponse) GetItems() []*Inventory {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_MainServer_Inventory_proto protoreflect.FileDescriptor

var file_MainServer_Inventory_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xa9, 0x02, 0x0a, 0x0d, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x74, 0x65,
	0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x69, 0x6d,
	0x75, 0x6d, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x69,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x43, 0x6f, 0x69, 0x6e, 0x22, 0x8e, 0x02, 0x0a, 0x09, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x74, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x61, 0x6c, 0x43, 0x6f, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0x41, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x41, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x7b, 0x0a, 0x0d, 0x53,
	0x61, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x69, 0x6e, 0x22, 0x44, 0x0a, 0x0f, 0x55, 0x6e, 0x73, 0x61,
	0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x80,
	0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x73, 0x22, 0x45, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x45, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x46, 0x0a, 0x10, 0x53, 0x61, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x48, 0x0a, 0x12, 0x55, 0x6e, 0x73, 0x61, 0x6c,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x2f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x22, 0x5c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x5b, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x2a, 0xd0, 0x01,
	0x0a, 0x0d, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x11, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x72,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x70, 0x6f, 0x6b, 0x65, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x6c, 0x6c, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x73, 0x79,
	0x73, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x68, 0x65, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x62, 0x65, 0x72, 0x72, 0x79, 0x10, 0x05, 0x12,
	0x11, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x62, 0x6f, 0x78,
	0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x63, 0x61, 0x72, 0x72, 0x79, 0x10, 0x07, 0x12, 0x17, 0x0a, 0x13, 0x69, 0x6e, 0x76, 0x65, 0x6e,
	0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x75, 0x6c, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x10, 0x08,
	0x2a, 0x64, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x1a, 0x0a, 0x16, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x53, 0x61, 0x6c, 0x65, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Inventory_proto_rawDescOnce sync.Once
	file_MainServer_Inventory_proto_rawDescData = file_MainServer_Inventory_proto_rawDesc
)

func file_MainServer_Inventory_proto_rawDescGZIP() []byte {
	file_MainServer_Inventory_proto_rawDescOnce.Do(func() {
		file_MainServer_Inventory_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Inventory_proto_rawDescData)
	})
	return file_MainServer_Inventory_proto_rawDescData
}

var file_MainServer_Inventory_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_Inventory_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_MainServer_Inventory_proto_goTypes = []any{
	(InventoryType)(0),          // 0: MainServer.InventoryType
	(InventoryStatus)(0),        // 1: MainServer.InventoryStatus
	(*InventorySlot)(nil),       // 2: MainServer.InventorySlot
	(*Inventory)(nil),           // 3: MainServer.Inventory
	(*UseItemParam)(nil),        // 4: MainServer.UseItemParam
	(*AddItemParam)(nil),        // 5: MainServer.AddItemParam
	(*SaleItemParam)(nil),       // 6: MainServer.SaleItemParam
	(*UnsaleItemParam)(nil),     // 7: MainServer.UnsaleItemParam
	(*QueryItemsParam)(nil),     // 8: MainServer.QueryItemsParam
	(*UseItemResponse)(nil),     // 9: MainServer.UseItemResponse
	(*AddItemResponse)(nil),     // 10: MainServer.AddItemResponse
	(*SaleItemResponse)(nil),    // 11: MainServer.SaleItemResponse
	(*UnsaleItemResponse)(nil),  // 12: MainServer.UnsaleItemResponse
	(*GetAllItemsParam)(nil),    // 13: MainServer.GetAllItemsParam
	(*GetAllItemsResponse)(nil), // 14: MainServer.GetAllItemsResponse
	(*QueryItemsResponse)(nil),  // 15: MainServer.QueryItemsResponse
}
var file_MainServer_Inventory_proto_depIdxs = []int32{
	1, // 0: MainServer.Inventory.status:type_name -> MainServer.InventoryStatus
	1, // 1: MainServer.QueryItemsParam.status:type_name -> MainServer.InventoryStatus
	3, // 2: MainServer.GetAllItemsResponse.items:type_name -> MainServer.Inventory
	3, // 3: MainServer.QueryItemsResponse.items:type_name -> MainServer.Inventory
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_Inventory_proto_init() }
func file_MainServer_Inventory_proto_init() {
	if File_MainServer_Inventory_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Inventory_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*InventorySlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Inventory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*UseItemParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AddItemParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SaleItemParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*UnsaleItemParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QueryItemsParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UseItemResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*AddItemResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SaleItemResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*UnsaleItemResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetAllItemsParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetAllItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Inventory_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*QueryItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Inventory_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Inventory_proto_goTypes,
		DependencyIndexes: file_MainServer_Inventory_proto_depIdxs,
		EnumInfos:         file_MainServer_Inventory_proto_enumTypes,
		MessageInfos:      file_MainServer_Inventory_proto_msgTypes,
	}.Build()
	File_MainServer_Inventory_proto = out.File
	file_MainServer_Inventory_proto_rawDesc = nil
	file_MainServer_Inventory_proto_goTypes = nil
	file_MainServer_Inventory_proto_depIdxs = nil
}
