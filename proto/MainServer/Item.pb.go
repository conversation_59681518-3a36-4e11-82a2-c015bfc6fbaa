// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Item.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ItemFilterSort int32

const (
	ItemFilterSort_price_i ItemFilterSort = 0 //默认用价格排序
)

// Enum value maps for ItemFilterSort.
var (
	ItemFilterSort_name = map[int32]string{
		0: "price_i",
	}
	ItemFilterSort_value = map[string]int32{
		"price_i": 0,
	}
)

func (x ItemFilterSort) Enum() *ItemFilterSort {
	p := new(ItemFilterSort)
	*p = x
	return p
}

func (x ItemFilterSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemFilterSort) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Item_proto_enumTypes[0].Descriptor()
}

func (ItemFilterSort) Type() protoreflect.EnumType {
	return &file_MainServer_Item_proto_enumTypes[0]
}

func (x ItemFilterSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemFilterSort.Descriptor instead.
func (ItemFilterSort) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid int64 `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	// int32 item_type = 3;
	Name      string     `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Count     int32      `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	Price     float64    `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	SaleCount int32      `protobuf:"varint,7,opt,name=sale_count,json=saleCount,proto3" json:"sale_count,omitempty"`
	Extra     *ItemExtra `protobuf:"bytes,8,opt,name=extra,proto3" json:"extra,omitempty"`                         // 额外信息
	CreateTs  int64      `protobuf:"varint,9,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`  // 创建时间戳
	UpdateTs  int64      `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Item_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{0}
}

func (x *Item) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Item) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Item) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Item) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Item) GetSaleCount() int32 {
	if x != nil {
		return x.SaleCount
	}
	return 0
}

func (x *Item) GetExtra() *ItemExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Item) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Item) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type ItemExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ItemExtra) Reset() {
	*x = ItemExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Item_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemExtra) ProtoMessage() {}

func (x *ItemExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemExtra.ProtoReflect.Descriptor instead.
func (*ItemExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{1}
}

// 在前端进行名称筛选 再传到后面
type ItemFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names    []string       `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	Sort     ItemFilterSort `protobuf:"varint,2,opt,name=sort,proto3,enum=MainServer.ItemFilterSort" json:"sort,omitempty"`
	Page     int32          `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32          `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MinPrice float64        `protobuf:"fixed64,5,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice float64        `protobuf:"fixed64,6,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	UpdateTs int64          `protobuf:"varint,7,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Sale     bool           `protobuf:"varint,8,opt,name=sale,proto3" json:"sale,omitempty"` //sale_count > 0
	Owner    bool           `protobuf:"varint,13,opt,name=owner,proto3" json:"owner,omitempty"`
}

func (x *ItemFilter) Reset() {
	*x = ItemFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Item_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemFilter) ProtoMessage() {}

func (x *ItemFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemFilter.ProtoReflect.Descriptor instead.
func (*ItemFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{2}
}

func (x *ItemFilter) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *ItemFilter) GetSort() ItemFilterSort {
	if x != nil {
		return x.Sort
	}
	return ItemFilterSort_price_i
}

func (x *ItemFilter) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ItemFilter) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ItemFilter) GetMinPrice() float64 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *ItemFilter) GetMaxPrice() float64 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *ItemFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *ItemFilter) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *ItemFilter) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

type UseItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	Quantity int32  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Target   string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
}

func (x *UseItemInfo) Reset() {
	*x = UseItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Item_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemInfo) ProtoMessage() {}

func (x *UseItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Item_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemInfo.ProtoReflect.Descriptor instead.
func (*UseItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Item_proto_rawDescGZIP(), []int{3}
}

func (x *UseItemInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *UseItemInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *UseItemInfo) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

var File_MainServer_Item_proto protoreflect.FileDescriptor

var file_MainServer_Item_proto_rawDesc = []byte{
	0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x49, 0x74, 0x65,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x22, 0xee, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x73, 0x22, 0x0b, 0x0a, 0x09, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x22, 0x84, 0x02, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x72, 0x74,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x61, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x61,
	0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x5e, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x2a, 0x1d, 0x0a, 0x0e, 0x49, 0x74, 0x65, 0x6d,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x0b, 0x0a, 0x07, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x10, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Item_proto_rawDescOnce sync.Once
	file_MainServer_Item_proto_rawDescData = file_MainServer_Item_proto_rawDesc
)

func file_MainServer_Item_proto_rawDescGZIP() []byte {
	file_MainServer_Item_proto_rawDescOnce.Do(func() {
		file_MainServer_Item_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Item_proto_rawDescData)
	})
	return file_MainServer_Item_proto_rawDescData
}

var file_MainServer_Item_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Item_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_Item_proto_goTypes = []any{
	(ItemFilterSort)(0), // 0: MainServer.ItemFilterSort
	(*Item)(nil),        // 1: MainServer.Item
	(*ItemExtra)(nil),   // 2: MainServer.ItemExtra
	(*ItemFilter)(nil),  // 3: MainServer.ItemFilter
	(*UseItemInfo)(nil), // 4: MainServer.UseItemInfo
}
var file_MainServer_Item_proto_depIdxs = []int32{
	2, // 0: MainServer.Item.extra:type_name -> MainServer.ItemExtra
	0, // 1: MainServer.ItemFilter.sort:type_name -> MainServer.ItemFilterSort
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_Item_proto_init() }
func file_MainServer_Item_proto_init() {
	if File_MainServer_Item_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Item_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Item_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ItemExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Item_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ItemFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Item_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UseItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Item_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Item_proto_goTypes,
		DependencyIndexes: file_MainServer_Item_proto_depIdxs,
		EnumInfos:         file_MainServer_Item_proto_enumTypes,
		MessageInfos:      file_MainServer_Item_proto_msgTypes,
	}.Build()
	File_MainServer_Item_proto = out.File
	file_MainServer_Item_proto_rawDesc = nil
	file_MainServer_Item_proto_goTypes = nil
	file_MainServer_Item_proto_depIdxs = nil
}
