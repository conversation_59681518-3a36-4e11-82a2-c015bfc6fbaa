// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/LocalPokeData.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LocalPokeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameId       string                          `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	PsData       *PSPokemonData                  `protobuf:"bytes,2,opt,name=psData,proto3" json:"psData,omitempty"`
	Localization *PokeDataLocalizationStoreValue `protobuf:"bytes,3,opt,name=localization,proto3" json:"localization,omitempty"`
}

func (x *LocalPokeData) Reset() {
	*x = LocalPokeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_LocalPokeData_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalPokeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalPokeData) ProtoMessage() {}

func (x *LocalPokeData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalPokeData.ProtoReflect.Descriptor instead.
func (*LocalPokeData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocalPokeData_proto_rawDescGZIP(), []int{0}
}

func (x *LocalPokeData) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *LocalPokeData) GetPsData() *PSPokemonData {
	if x != nil {
		return x.PsData
	}
	return nil
}

func (x *LocalPokeData) GetLocalization() *PokeDataLocalizationStoreValue {
	if x != nil {
		return x.Localization
	}
	return nil
}

type LocalMoveData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameId       string                          `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	PsData       *PSMMoveData                    `protobuf:"bytes,2,opt,name=psData,proto3" json:"psData,omitempty"`
	Localization *PokeDataLocalizationStoreValue `protobuf:"bytes,3,opt,name=localization,proto3" json:"localization,omitempty"`
}

func (x *LocalMoveData) Reset() {
	*x = LocalMoveData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_LocalPokeData_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalMoveData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalMoveData) ProtoMessage() {}

func (x *LocalMoveData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalMoveData.ProtoReflect.Descriptor instead.
func (*LocalMoveData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocalPokeData_proto_rawDescGZIP(), []int{1}
}

func (x *LocalMoveData) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *LocalMoveData) GetPsData() *PSMMoveData {
	if x != nil {
		return x.PsData
	}
	return nil
}

func (x *LocalMoveData) GetLocalization() *PokeDataLocalizationStoreValue {
	if x != nil {
		return x.Localization
	}
	return nil
}

var File_MainServer_LocalPokeData_proto protoreflect.FileDescriptor

var file_MainServer_LocalPokeData_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x18, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x64, 0x65, 0x78,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2f, 0x50, 0x53, 0x4d, 0x6f, 0x76, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x01, 0x0a, 0x0d, 0x4c,
	0x6f, 0x63, 0x61, 0x6c, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x53, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x06, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa9, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x70, 0x73, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x53, 0x4d, 0x4d, 0x6f, 0x76, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_LocalPokeData_proto_rawDescOnce sync.Once
	file_MainServer_LocalPokeData_proto_rawDescData = file_MainServer_LocalPokeData_proto_rawDesc
)

func file_MainServer_LocalPokeData_proto_rawDescGZIP() []byte {
	file_MainServer_LocalPokeData_proto_rawDescOnce.Do(func() {
		file_MainServer_LocalPokeData_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_LocalPokeData_proto_rawDescData)
	})
	return file_MainServer_LocalPokeData_proto_rawDescData
}

var file_MainServer_LocalPokeData_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_LocalPokeData_proto_goTypes = []any{
	(*LocalPokeData)(nil),                  // 0: MainServer.LocalPokeData
	(*LocalMoveData)(nil),                  // 1: MainServer.LocalMoveData
	(*PSPokemonData)(nil),                  // 2: MainServer.PSPokemonData
	(*PokeDataLocalizationStoreValue)(nil), // 3: MainServer.PokeDataLocalizationStoreValue
	(*PSMMoveData)(nil),                    // 4: MainServer.PSMMoveData
}
var file_MainServer_LocalPokeData_proto_depIdxs = []int32{
	2, // 0: MainServer.LocalPokeData.psData:type_name -> MainServer.PSPokemonData
	3, // 1: MainServer.LocalPokeData.localization:type_name -> MainServer.PokeDataLocalizationStoreValue
	4, // 2: MainServer.LocalMoveData.psData:type_name -> MainServer.PSMMoveData
	3, // 3: MainServer.LocalMoveData.localization:type_name -> MainServer.PokeDataLocalizationStoreValue
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_LocalPokeData_proto_init() }
func file_MainServer_LocalPokeData_proto_init() {
	if File_MainServer_LocalPokeData_proto != nil {
		return
	}
	file_MainServer_Pokedex_proto_init()
	file_MainServer_PSMove_proto_init()
	file_MainServer_PokeName_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_LocalPokeData_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*LocalPokeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_LocalPokeData_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*LocalMoveData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_LocalPokeData_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_LocalPokeData_proto_goTypes,
		DependencyIndexes: file_MainServer_LocalPokeData_proto_depIdxs,
		MessageInfos:      file_MainServer_LocalPokeData_proto_msgTypes,
	}.Build()
	File_MainServer_LocalPokeData_proto = out.File
	file_MainServer_LocalPokeData_proto_rawDesc = nil
	file_MainServer_LocalPokeData_proto_goTypes = nil
	file_MainServer_LocalPokeData_proto_depIdxs = nil
}
