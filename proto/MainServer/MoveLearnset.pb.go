// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/MoveLearnset.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义学习方式的枚举
type MoveLearnMethod int32

const (
	MoveLearnMethod_MACHINE  MoveLearnMethod = 0 // M: 通过技术机器 (TM/HM)
	MoveLearnMethod_EGG_MOVE MoveLearnMethod = 1 // E: 通过遗传 (Egg Move)
	MoveLearnMethod_TUTOR    MoveLearnMethod = 2 // T: 通过教学 (Tutor)
	MoveLearnMethod_EVENT_S  MoveLearnMethod = 3 // S: 通过事件 (Event)
	MoveLearnMethod_LEVEL_UP MoveLearnMethod = 4 // L: 通过升级 (Level Up)
	MoveLearnMethod_VIRTUAL  MoveLearnMethod = 5 // V: 虚拟控制台 (Virtual Console)
	MoveLearnMethod_OTHER    MoveLearnMethod = 6 // O: 其他或未识别的方式
)

// Enum value maps for MoveLearnMethod.
var (
	MoveLearnMethod_name = map[int32]string{
		0: "MACHINE",
		1: "EGG_MOVE",
		2: "TUTOR",
		3: "EVENT_S",
		4: "LEVEL_UP",
		5: "VIRTUAL",
		6: "OTHER",
	}
	MoveLearnMethod_value = map[string]int32{
		"MACHINE":  0,
		"EGG_MOVE": 1,
		"TUTOR":    2,
		"EVENT_S":  3,
		"LEVEL_UP": 4,
		"VIRTUAL":  5,
		"OTHER":    6,
	}
)

func (x MoveLearnMethod) Enum() *MoveLearnMethod {
	p := new(MoveLearnMethod)
	*p = x
	return p
}

func (x MoveLearnMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MoveLearnMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_MoveLearnset_proto_enumTypes[0].Descriptor()
}

func (MoveLearnMethod) Type() protoreflect.EnumType {
	return &file_MainServer_MoveLearnset_proto_enumTypes[0]
}

func (x MoveLearnMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MoveLearnMethod.Descriptor instead.
func (MoveLearnMethod) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{0}
}

// 定义 GenerationMove 消息
type GenerationMove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Generation int32           `protobuf:"varint,1,opt,name=generation,proto3" json:"generation,omitempty"`                         // 世代编号
	Method     MoveLearnMethod `protobuf:"varint,2,opt,name=method,proto3,enum=MainServer.MoveLearnMethod" json:"method,omitempty"` // 学习方式
	Level      *int32          `protobuf:"varint,3,opt,name=level,proto3,oneof" json:"level,omitempty"`                             // 学习等级，如果有的话
}

func (x *GenerationMove) Reset() {
	*x = GenerationMove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_MoveLearnset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerationMove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerationMove) ProtoMessage() {}

func (x *GenerationMove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_MoveLearnset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerationMove.ProtoReflect.Descriptor instead.
func (*GenerationMove) Descriptor() ([]byte, []int) {
	return file_MainServer_MoveLearnset_proto_rawDescGZIP(), []int{0}
}

func (x *GenerationMove) GetGeneration() int32 {
	if x != nil {
		return x.Generation
	}
	return 0
}

func (x *GenerationMove) GetMethod() MoveLearnMethod {
	if x != nil {
		return x.Method
	}
	return MoveLearnMethod_MACHINE
}

func (x *GenerationMove) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

var File_MainServer_MoveLearnset_proto protoreflect.FileDescriptor

var file_MainServer_MoveLearnset_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x4d, 0x6f, 0x76,
	0x65, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x8a, 0x01, 0x0a, 0x0e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33,
	0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x76, 0x65,
	0x4c, 0x65, 0x61, 0x72, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x2a, 0x6a, 0x0a, 0x0f, 0x4d, 0x6f, 0x76, 0x65,
	0x4c, 0x65, 0x61, 0x72, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0b, 0x0a, 0x07, 0x4d,
	0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x47, 0x47, 0x5f,
	0x4d, 0x4f, 0x56, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x55, 0x54, 0x4f, 0x52, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x56, 0x49, 0x52, 0x54, 0x55, 0x41, 0x4c, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x10, 0x06, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_MoveLearnset_proto_rawDescOnce sync.Once
	file_MainServer_MoveLearnset_proto_rawDescData = file_MainServer_MoveLearnset_proto_rawDesc
)

func file_MainServer_MoveLearnset_proto_rawDescGZIP() []byte {
	file_MainServer_MoveLearnset_proto_rawDescOnce.Do(func() {
		file_MainServer_MoveLearnset_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_MoveLearnset_proto_rawDescData)
	})
	return file_MainServer_MoveLearnset_proto_rawDescData
}

var file_MainServer_MoveLearnset_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_MoveLearnset_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_MoveLearnset_proto_goTypes = []any{
	(MoveLearnMethod)(0),   // 0: MainServer.MoveLearnMethod
	(*GenerationMove)(nil), // 1: MainServer.GenerationMove
}
var file_MainServer_MoveLearnset_proto_depIdxs = []int32{
	0, // 0: MainServer.GenerationMove.method:type_name -> MainServer.MoveLearnMethod
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_MoveLearnset_proto_init() }
func file_MainServer_MoveLearnset_proto_init() {
	if File_MainServer_MoveLearnset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_MoveLearnset_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GenerationMove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_MainServer_MoveLearnset_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_MoveLearnset_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_MoveLearnset_proto_goTypes,
		DependencyIndexes: file_MainServer_MoveLearnset_proto_depIdxs,
		EnumInfos:         file_MainServer_MoveLearnset_proto_enumTypes,
		MessageInfos:      file_MainServer_MoveLearnset_proto_msgTypes,
	}.Build()
	File_MainServer_MoveLearnset_proto = out.File
	file_MainServer_MoveLearnset_proto_rawDesc = nil
	file_MainServer_MoveLearnset_proto_goTypes = nil
	file_MainServer_MoveLearnset_proto_depIdxs = nil
}
