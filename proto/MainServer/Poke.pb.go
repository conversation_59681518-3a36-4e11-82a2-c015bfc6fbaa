// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Poke.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Nature int32

const (
	Nature_NATURE_UNSPECIFIED Nature = 0  // 默认值
	Nature_ADAMANT            Nature = 1  // +atk, -spa
	Nature_BASHFUL            Nature = 2  // 无增减
	Nature_BOLD               Nature = 3  // +def, -atk
	Nature_BRAVE              Nature = 4  // +atk, -spe
	Nature_CALM               Nature = 5  // +spd, -atk
	Nature_CAREFUL            Nature = 6  // +spd, -spa
	Nature_DOCILE             Nature = 7  // 无增减
	Nature_GENTLE             Nature = 8  // +spd, -def
	Nature_HARDY              Nature = 9  // 无增减
	Nature_HASTY              Nature = 10 // +spe, -def
	Nature_IMPISH             Nature = 11 // +def, -spa
	Nature_JOLLY              Nature = 12 // +spe, -spa
	Nature_LAX                Nature = 13 // +def, -spd
	Nature_LONELY             Nature = 14 // +atk, -def
	Nature_MILD               Nature = 15 // +spa, -def
	Nature_MODEST             Nature = 16 // +spa, -atk
	Nature_NAIVE              Nature = 17 // +spe, -spd
	Nature_NAUGHTY            Nature = 18 // +atk, -spd
	Nature_QUIET              Nature = 19 // +spa, -spe
	Nature_QUIRKY             Nature = 20 // 无增减
	Nature_RASH               Nature = 21 // +spa, -spd
	Nature_RELAXED            Nature = 22 // +def, -spe
	Nature_SASSY              Nature = 23 // +spd, -spe
	Nature_SERIOUS            Nature = 24 // 无增减
	Nature_TIMID              Nature = 25 // +spe, -atk
)

// Enum value maps for Nature.
var (
	Nature_name = map[int32]string{
		0:  "NATURE_UNSPECIFIED",
		1:  "ADAMANT",
		2:  "BASHFUL",
		3:  "BOLD",
		4:  "BRAVE",
		5:  "CALM",
		6:  "CAREFUL",
		7:  "DOCILE",
		8:  "GENTLE",
		9:  "HARDY",
		10: "HASTY",
		11: "IMPISH",
		12: "JOLLY",
		13: "LAX",
		14: "LONELY",
		15: "MILD",
		16: "MODEST",
		17: "NAIVE",
		18: "NAUGHTY",
		19: "QUIET",
		20: "QUIRKY",
		21: "RASH",
		22: "RELAXED",
		23: "SASSY",
		24: "SERIOUS",
		25: "TIMID",
	}
	Nature_value = map[string]int32{
		"NATURE_UNSPECIFIED": 0,
		"ADAMANT":            1,
		"BASHFUL":            2,
		"BOLD":               3,
		"BRAVE":              4,
		"CALM":               5,
		"CAREFUL":            6,
		"DOCILE":             7,
		"GENTLE":             8,
		"HARDY":              9,
		"HASTY":              10,
		"IMPISH":             11,
		"JOLLY":              12,
		"LAX":                13,
		"LONELY":             14,
		"MILD":               15,
		"MODEST":             16,
		"NAIVE":              17,
		"NAUGHTY":            18,
		"QUIET":              19,
		"QUIRKY":             20,
		"RASH":               21,
		"RELAXED":            22,
		"SASSY":              23,
		"SERIOUS":            24,
		"TIMID":              25,
	}
)

func (x Nature) Enum() *Nature {
	p := new(Nature)
	*p = x
	return p
}

func (x Nature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Nature) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[0].Descriptor()
}

func (Nature) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[0]
}

func (x Nature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Nature.Descriptor instead.
func (Nature) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

type Poke struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid        int64         `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                               // user 信息
	Name       string        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                              // 宝可梦名称
	NickName   string        `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`      // 宝可梦昵称
	BallName   string        `protobuf:"bytes,5,opt,name=ball_name,json=ballName,proto3" json:"ball_name,omitempty"`      // 捕获使用的球
	ItemName   string        `protobuf:"bytes,6,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`      // 携带的道具名称
	Ability    string        `protobuf:"bytes,7,opt,name=ability,proto3" json:"ability,omitempty"`                        // 特性
	Evs        *PokeStat     `protobuf:"bytes,8,opt,name=evs,proto3" json:"evs,omitempty"`                                // 宝可梦的努力值
	Ivs        *PokeStat     `protobuf:"bytes,9,opt,name=ivs,proto3" json:"ivs,omitempty"`                                // 宝可梦的个体值
	Sale       bool          `protobuf:"varint,10,opt,name=sale,proto3" json:"sale,omitempty"`                            // 是否在售
	SaleInfo   *SaleInfo     `protobuf:"bytes,11,opt,name=sale_info,json=saleInfo,proto3" json:"sale_info,omitempty"`     // 售价
	Stats      string        `protobuf:"bytes,12,opt,name=stats,proto3" json:"stats,omitempty"`                           // 宝可梦当前状态
	Moves      []*Moves      `protobuf:"bytes,13,rep,name=moves,proto3" json:"moves,omitempty"`                           // 宝可梦的招式
	Level      int32         `protobuf:"varint,14,opt,name=level,proto3" json:"level,omitempty"`                          // 等级
	Nature     Nature        `protobuf:"varint,15,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"` // 宝可梦性格信息
	Status     string        `protobuf:"bytes,16,opt,name=status,proto3" json:"status,omitempty"`                         // 宝可梦状态信息
	Experience int64         `protobuf:"varint,17,opt,name=experience,proto3" json:"experience,omitempty"`                // 经验值
	Born       *BornInfo     `protobuf:"bytes,18,opt,name=born,proto3" json:"born,omitempty"`                             // 出生信息
	Egg        bool          `protobuf:"varint,19,opt,name=egg,proto3" json:"egg,omitempty"`                              // 是否为蛋
	Shiny      int32         `protobuf:"varint,20,opt,name=shiny,proto3" json:"shiny,omitempty"`                          // 是否为异色（0为普通）
	Gender     string        `protobuf:"bytes,21,opt,name=gender,proto3" json:"gender,omitempty"`                         // 性别
	HpSub      int32         `protobuf:"varint,22,opt,name=hp_sub,json=hpSub,proto3" json:"hp_sub,omitempty"`             // hp
	Happiness  int32         `protobuf:"varint,23,opt,name=happiness,proto3" json:"happiness,omitempty"`
	SysExtra   *PokeSysExtra `protobuf:"bytes,24,opt,name=sys_extra,json=sysExtra,proto3" json:"sys_extra,omitempty"`
	Extra      *PokeExtra    `protobuf:"bytes,25,opt,name=extra,proto3" json:"extra,omitempty"` // 额外信息
	Release    bool          `protobuf:"varint,26,opt,name=release,proto3" json:"release,omitempty"`
	HonorInfo  *HonorInfo    `protobuf:"bytes,27,opt,name=honorInfo,proto3" json:"honorInfo,omitempty"`
	BreedCount int32         `protobuf:"varint,28,opt,name=breedCount,proto3" json:"breedCount,omitempty"`
	CreateTs   int64         `protobuf:"varint,29,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 创建时间戳
	UpdateTs   int64         `protobuf:"varint,30,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
}

func (x *Poke) Reset() {
	*x = Poke{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Poke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Poke) ProtoMessage() {}

func (x *Poke) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Poke.ProtoReflect.Descriptor instead.
func (*Poke) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

func (x *Poke) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Poke) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Poke) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Poke) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *Poke) GetBallName() string {
	if x != nil {
		return x.BallName
	}
	return ""
}

func (x *Poke) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *Poke) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *Poke) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

func (x *Poke) GetIvs() *PokeStat {
	if x != nil {
		return x.Ivs
	}
	return nil
}

func (x *Poke) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *Poke) GetSaleInfo() *SaleInfo {
	if x != nil {
		return x.SaleInfo
	}
	return nil
}

func (x *Poke) GetStats() string {
	if x != nil {
		return x.Stats
	}
	return ""
}

func (x *Poke) GetMoves() []*Moves {
	if x != nil {
		return x.Moves
	}
	return nil
}

func (x *Poke) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Poke) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *Poke) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Poke) GetExperience() int64 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *Poke) GetBorn() *BornInfo {
	if x != nil {
		return x.Born
	}
	return nil
}

func (x *Poke) GetEgg() bool {
	if x != nil {
		return x.Egg
	}
	return false
}

func (x *Poke) GetShiny() int32 {
	if x != nil {
		return x.Shiny
	}
	return 0
}

func (x *Poke) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Poke) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

func (x *Poke) GetHappiness() int32 {
	if x != nil {
		return x.Happiness
	}
	return 0
}

func (x *Poke) GetSysExtra() *PokeSysExtra {
	if x != nil {
		return x.SysExtra
	}
	return nil
}

func (x *Poke) GetExtra() *PokeExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Poke) GetRelease() bool {
	if x != nil {
		return x.Release
	}
	return false
}

func (x *Poke) GetHonorInfo() *HonorInfo {
	if x != nil {
		return x.HonorInfo
	}
	return nil
}

func (x *Poke) GetBreedCount() int32 {
	if x != nil {
		return x.BreedCount
	}
	return 0
}

func (x *Poke) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Poke) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type HonorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Badges []string `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
}

func (x *HonorInfo) Reset() {
	*x = HonorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HonorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HonorInfo) ProtoMessage() {}

func (x *HonorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HonorInfo.ProtoReflect.Descriptor instead.
func (*HonorInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{1}
}

func (x *HonorInfo) GetBadges() []string {
	if x != nil {
		return x.Badges
	}
	return nil
}

type SaleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Price       int32 `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	SpecialCoin int32 `protobuf:"varint,2,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`
}

func (x *SaleInfo) Reset() {
	*x = SaleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleInfo) ProtoMessage() {}

func (x *SaleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleInfo.ProtoReflect.Descriptor instead.
func (*SaleInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{2}
}

func (x *SaleInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleInfo) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

type PokeStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hp  int32 `protobuf:"varint,1,opt,name=hp,proto3" json:"hp,omitempty"`
	Atk int32 `protobuf:"varint,2,opt,name=atk,proto3" json:"atk,omitempty"`
	Def int32 `protobuf:"varint,3,opt,name=def,proto3" json:"def,omitempty"`
	Spa int32 `protobuf:"varint,4,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd int32 `protobuf:"varint,5,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe int32 `protobuf:"varint,6,opt,name=spe,proto3" json:"spe,omitempty"`
}

func (x *PokeStat) Reset() {
	*x = PokeStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeStat) ProtoMessage() {}

func (x *PokeStat) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeStat.ProtoReflect.Descriptor instead.
func (*PokeStat) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{3}
}

func (x *PokeStat) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PokeStat) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PokeStat) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PokeStat) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PokeStat) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PokeStat) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type Moves struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PpPro int32  `protobuf:"varint,2,opt,name=pp_pro,json=ppPro,proto3" json:"pp_pro,omitempty"`
	PpSub int32  `protobuf:"varint,3,opt,name=pp_sub,json=ppSub,proto3" json:"pp_sub,omitempty"`
}

func (x *Moves) Reset() {
	*x = Moves{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Moves) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Moves) ProtoMessage() {}

func (x *Moves) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Moves.ProtoReflect.Descriptor instead.
func (*Moves) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{4}
}

func (x *Moves) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Moves) GetPpPro() int32 {
	if x != nil {
		return x.PpPro
	}
	return 0
}

func (x *Moves) GetPpSub() int32 {
	if x != nil {
		return x.PpSub
	}
	return 0
}

type BornInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionInfo string `protobuf:"bytes,1,opt,name=region_info,json=regionInfo,proto3" json:"region_info,omitempty"`
}

func (x *BornInfo) Reset() {
	*x = BornInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BornInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BornInfo) ProtoMessage() {}

func (x *BornInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BornInfo.ProtoReflect.Descriptor instead.
func (*BornInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{5}
}

func (x *BornInfo) GetRegionInfo() string {
	if x != nil {
		return x.RegionInfo
	}
	return ""
}

type PokeSysExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DynamaxLevel int32  `protobuf:"varint,1,opt,name=dynamaxLevel,proto3" json:"dynamaxLevel,omitempty"`
	Gigantamax   bool   `protobuf:"varint,2,opt,name=gigantamax,proto3" json:"gigantamax,omitempty"`
	Terastal     string `protobuf:"bytes,3,opt,name=terastal,proto3" json:"terastal,omitempty"` //太晶化类型
}

func (x *PokeSysExtra) Reset() {
	*x = PokeSysExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeSysExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeSysExtra) ProtoMessage() {}

func (x *PokeSysExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeSysExtra.ProtoReflect.Descriptor instead.
func (*PokeSysExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{6}
}

func (x *PokeSysExtra) GetDynamaxLevel() int32 {
	if x != nil {
		return x.DynamaxLevel
	}
	return 0
}

func (x *PokeSysExtra) GetGigantamax() bool {
	if x != nil {
		return x.Gigantamax
	}
	return false
}

func (x *PokeSysExtra) GetTerastal() string {
	if x != nil {
		return x.Terastal
	}
	return ""
}

type PokeExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TempItem string    `protobuf:"bytes,1,opt,name=tempItem,proto3" json:"tempItem,omitempty"` //临时道具 //用于租借方更换道具 //归还后退还道具
	TempEvs  *PokeStat `protobuf:"bytes,2,opt,name=tempEvs,proto3" json:"tempEvs,omitempty"`   // 临时宝可梦的努力值 //用于租借修改努力值 //待定使用
}

func (x *PokeExtra) Reset() {
	*x = PokeExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Poke_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeExtra) ProtoMessage() {}

func (x *PokeExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeExtra.ProtoReflect.Descriptor instead.
func (*PokeExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{7}
}

func (x *PokeExtra) GetTempItem() string {
	if x != nil {
		return x.TempItem
	}
	return ""
}

func (x *PokeExtra) GetTempEvs() *PokeStat {
	if x != nil {
		return x.TempEvs
	}
	return nil
}

var File_MainServer_Poke_proto protoreflect.FileDescriptor

var file_MainServer_Poke_proto_rawDesc = []byte{
	0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x22, 0xa9, 0x07, 0x0a, 0x04, 0x50, 0x6f, 0x6b, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x03, 0x65, 0x76, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x03, 0x65, 0x76, 0x73, 0x12, 0x26, 0x0a, 0x03, 0x69,
	0x76, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x03,
	0x69, 0x76, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x61, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x73, 0x61, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x73, 0x61, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x73,
	0x12, 0x27, 0x0a, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x76,
	0x65, 0x73, 0x52, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x2a, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x12, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42,
	0x6f, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x67, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x67, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x73, 0x68, 0x69, 0x6e, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a,
	0x06, 0x68, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x68,
	0x70, 0x53, 0x75, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x79, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x53, 0x79, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x08, 0x73, 0x79, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x12, 0x33, 0x0a, 0x09, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x48, 0x6f, 0x6e, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x68, 0x6f, 0x6e, 0x6f,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x72, 0x65, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22,
	0x23, 0x0a, 0x09, 0x48, 0x6f, 0x6e, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x61, 0x64, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x62, 0x61,
	0x64, 0x67, 0x65, 0x73, 0x22, 0x43, 0x0a, 0x08, 0x53, 0x61, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x69, 0x6e, 0x22, 0x74, 0x0a, 0x08, 0x50, 0x6f, 0x6b,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x68, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x74, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x61, 0x74, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x70, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x70, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x70, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x70, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x73, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x70, 0x65, 0x22,
	0x49, 0x0a, 0x05, 0x4d, 0x6f, 0x76, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x70,
	0x50, 0x72, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x70, 0x53, 0x75, 0x62, 0x22, 0x2b, 0x0a, 0x08, 0x42, 0x6f,
	0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6e, 0x0a, 0x0c, 0x50, 0x6f, 0x6b, 0x65, 0x53,
	0x79, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x61, 0x78, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x67,
	0x69, 0x67, 0x61, 0x6e, 0x74, 0x61, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x67, 0x69, 0x67, 0x61, 0x6e, 0x74, 0x61, 0x6d, 0x61, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x65, 0x72, 0x61, 0x73, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x72, 0x61, 0x73, 0x74, 0x61, 0x6c, 0x22, 0x57, 0x0a, 0x09, 0x50, 0x6f, 0x6b, 0x65, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x49, 0x74, 0x65, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x2e, 0x0a, 0x07, 0x74, 0x65, 0x6d, 0x70, 0x45, 0x76, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x6f, 0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x07, 0x74, 0x65, 0x6d, 0x70, 0x45, 0x76, 0x73,
	0x2a, 0xbf, 0x02, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x4e,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x44, 0x41, 0x4d, 0x41, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x42, 0x41, 0x53, 0x48, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x08, 0x0a,
	0x04, 0x42, 0x4f, 0x4c, 0x44, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x52, 0x41, 0x56, 0x45,
	0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x4c, 0x4d, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x41, 0x52, 0x45, 0x46, 0x55, 0x4c, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x4f, 0x43,
	0x49, 0x4c, 0x45, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x45, 0x4e, 0x54, 0x4c, 0x45, 0x10,
	0x08, 0x12, 0x09, 0x0a, 0x05, 0x48, 0x41, 0x52, 0x44, 0x59, 0x10, 0x09, 0x12, 0x09, 0x0a, 0x05,
	0x48, 0x41, 0x53, 0x54, 0x59, 0x10, 0x0a, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4d, 0x50, 0x49, 0x53,
	0x48, 0x10, 0x0b, 0x12, 0x09, 0x0a, 0x05, 0x4a, 0x4f, 0x4c, 0x4c, 0x59, 0x10, 0x0c, 0x12, 0x07,
	0x0a, 0x03, 0x4c, 0x41, 0x58, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x4f, 0x4e, 0x45, 0x4c,
	0x59, 0x10, 0x0e, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x49, 0x4c, 0x44, 0x10, 0x0f, 0x12, 0x0a, 0x0a,
	0x06, 0x4d, 0x4f, 0x44, 0x45, 0x53, 0x54, 0x10, 0x10, 0x12, 0x09, 0x0a, 0x05, 0x4e, 0x41, 0x49,
	0x56, 0x45, 0x10, 0x11, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x41, 0x55, 0x47, 0x48, 0x54, 0x59, 0x10,
	0x12, 0x12, 0x09, 0x0a, 0x05, 0x51, 0x55, 0x49, 0x45, 0x54, 0x10, 0x13, 0x12, 0x0a, 0x0a, 0x06,
	0x51, 0x55, 0x49, 0x52, 0x4b, 0x59, 0x10, 0x14, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x41, 0x53, 0x48,
	0x10, 0x15, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x4c, 0x41, 0x58, 0x45, 0x44, 0x10, 0x16, 0x12,
	0x09, 0x0a, 0x05, 0x53, 0x41, 0x53, 0x53, 0x59, 0x10, 0x17, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45,
	0x52, 0x49, 0x4f, 0x55, 0x53, 0x10, 0x18, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x49, 0x4d, 0x49, 0x44,
	0x10, 0x19, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Poke_proto_rawDescOnce sync.Once
	file_MainServer_Poke_proto_rawDescData = file_MainServer_Poke_proto_rawDesc
)

func file_MainServer_Poke_proto_rawDescGZIP() []byte {
	file_MainServer_Poke_proto_rawDescOnce.Do(func() {
		file_MainServer_Poke_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Poke_proto_rawDescData)
	})
	return file_MainServer_Poke_proto_rawDescData
}

var file_MainServer_Poke_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Poke_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_MainServer_Poke_proto_goTypes = []any{
	(Nature)(0),          // 0: MainServer.Nature
	(*Poke)(nil),         // 1: MainServer.Poke
	(*HonorInfo)(nil),    // 2: MainServer.HonorInfo
	(*SaleInfo)(nil),     // 3: MainServer.SaleInfo
	(*PokeStat)(nil),     // 4: MainServer.PokeStat
	(*Moves)(nil),        // 5: MainServer.Moves
	(*BornInfo)(nil),     // 6: MainServer.BornInfo
	(*PokeSysExtra)(nil), // 7: MainServer.PokeSysExtra
	(*PokeExtra)(nil),    // 8: MainServer.PokeExtra
}
var file_MainServer_Poke_proto_depIdxs = []int32{
	4,  // 0: MainServer.Poke.evs:type_name -> MainServer.PokeStat
	4,  // 1: MainServer.Poke.ivs:type_name -> MainServer.PokeStat
	3,  // 2: MainServer.Poke.sale_info:type_name -> MainServer.SaleInfo
	5,  // 3: MainServer.Poke.moves:type_name -> MainServer.Moves
	0,  // 4: MainServer.Poke.nature:type_name -> MainServer.Nature
	6,  // 5: MainServer.Poke.born:type_name -> MainServer.BornInfo
	7,  // 6: MainServer.Poke.sys_extra:type_name -> MainServer.PokeSysExtra
	8,  // 7: MainServer.Poke.extra:type_name -> MainServer.PokeExtra
	2,  // 8: MainServer.Poke.honorInfo:type_name -> MainServer.HonorInfo
	4,  // 9: MainServer.PokeExtra.tempEvs:type_name -> MainServer.PokeStat
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_MainServer_Poke_proto_init() }
func file_MainServer_Poke_proto_init() {
	if File_MainServer_Poke_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Poke_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Poke); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*HonorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SaleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PokeStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Moves); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*BornInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PokeSysExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Poke_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PokeExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Poke_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Poke_proto_goTypes,
		DependencyIndexes: file_MainServer_Poke_proto_depIdxs,
		EnumInfos:         file_MainServer_Poke_proto_enumTypes,
		MessageInfos:      file_MainServer_Poke_proto_msgTypes,
	}.Build()
	File_MainServer_Poke_proto = out.File
	file_MainServer_Poke_proto_rawDesc = nil
	file_MainServer_Poke_proto_goTypes = nil
	file_MainServer_Poke_proto_depIdxs = nil
}
