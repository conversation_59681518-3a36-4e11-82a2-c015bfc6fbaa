// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/PokeBox.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeBoxType int32

const (
	PokeBoxType_normal        PokeBoxType = 0
	PokeBoxType_hatch         PokeBoxType = 1  //孵化
	PokeBoxType_sale          PokeBoxType = 2  //出售
	PokeBoxType_rent          PokeBoxType = 3  //出租
	PokeBoxType_around        PokeBoxType = 4  //携带 //还未使用
	PokeBoxType_borrow        PokeBoxType = 5  //借用
	PokeBoxType_specialNormal PokeBoxType = 10 //特殊
	PokeBoxType_specialHatch  PokeBoxType = 11 //特殊孵化
	PokeBoxType_specialSale   PokeBoxType = 12 //特殊出售
	PokeBoxType_specialRent   PokeBoxType = 13 //特殊出租
	PokeBoxType_specialAround PokeBoxType = 14 //特殊携带
	PokeBoxType_specialBorrow PokeBoxType = 15 //特殊借用
)

// Enum value maps for PokeBoxType.
var (
	PokeBoxType_name = map[int32]string{
		0:  "normal",
		1:  "hatch",
		2:  "sale",
		3:  "rent",
		4:  "around",
		5:  "borrow",
		10: "specialNormal",
		11: "specialHatch",
		12: "specialSale",
		13: "specialRent",
		14: "specialAround",
		15: "specialBorrow",
	}
	PokeBoxType_value = map[string]int32{
		"normal":        0,
		"hatch":         1,
		"sale":          2,
		"rent":          3,
		"around":        4,
		"borrow":        5,
		"specialNormal": 10,
		"specialHatch":  11,
		"specialSale":   12,
		"specialRent":   13,
		"specialAround": 14,
		"specialBorrow": 15,
	}
)

func (x PokeBoxType) Enum() *PokeBoxType {
	p := new(PokeBoxType)
	*p = x
	return p
}

func (x PokeBoxType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeBoxType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeBox_proto_enumTypes[0].Descriptor()
}

func (PokeBoxType) Type() protoreflect.EnumType {
	return &file_MainServer_PokeBox_proto_enumTypes[0]
}

func (x PokeBoxType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeBoxType.Descriptor instead.
func (PokeBoxType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{0}
}

type PokeBorrowType int32

const (
	PokeBorrowType_BorrowTypeNone        PokeBorrowType = 0
	PokeBorrowType_BorrowTypeTrainerTeam PokeBorrowType = 1 //向组织租借
	PokeBorrowType_BorrowTypeRent        PokeBorrowType = 2 //向个人租借
)

// Enum value maps for PokeBorrowType.
var (
	PokeBorrowType_name = map[int32]string{
		0: "BorrowTypeNone",
		1: "BorrowTypeTrainerTeam",
		2: "BorrowTypeRent",
	}
	PokeBorrowType_value = map[string]int32{
		"BorrowTypeNone":        0,
		"BorrowTypeTrainerTeam": 1,
		"BorrowTypeRent":        2,
	}
)

func (x PokeBorrowType) Enum() *PokeBorrowType {
	p := new(PokeBorrowType)
	*p = x
	return p
}

func (x PokeBorrowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeBorrowType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeBox_proto_enumTypes[1].Descriptor()
}

func (PokeBorrowType) Type() protoreflect.EnumType {
	return &file_MainServer_PokeBox_proto_enumTypes[1]
}

func (x PokeBorrowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeBorrowType.Descriptor instead.
func (PokeBorrowType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{1}
}

type PokeBox struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index    int32                   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Tid      int64                   `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"`
	Name     string                  `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type     PokeBoxType             `protobuf:"varint,5,opt,name=type,proto3,enum=MainServer.PokeBoxType" json:"type,omitempty"`
	Pokes    map[string]*BoxPokeInfo `protobuf:"bytes,6,rep,name=pokes,proto3" json:"pokes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra    *PokeBoxExtra           `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`
	CreateTs int64                   `protobuf:"varint,8,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	UpdateTs int64                   `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
}

func (x *PokeBox) Reset() {
	*x = PokeBox{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeBox_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeBox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBox) ProtoMessage() {}

func (x *PokeBox) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBox.ProtoReflect.Descriptor instead.
func (*PokeBox) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{0}
}

func (x *PokeBox) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeBox) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *PokeBox) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeBox) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeBox) GetType() PokeBoxType {
	if x != nil {
		return x.Type
	}
	return PokeBoxType_normal
}

func (x *PokeBox) GetPokes() map[string]*BoxPokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeBox) GetExtra() *PokeBoxExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *PokeBox) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeBox) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type BoxPokeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PokeBoxType type = 1;
	// int32 loc = 2;
	Id     int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Around bool  `protobuf:"varint,4,opt,name=around,proto3" json:"around,omitempty"`
	// string name = 4;
	// int32 lv = 5;
	// string item = 6;
	// int32 shiny = 7;
	// bool egg = 8;
	BorrowType PokeBorrowType `protobuf:"varint,7,opt,name=borrow_type,json=borrowType,proto3,enum=MainServer.PokeBorrowType" json:"borrow_type,omitempty"`
	BorrowTime int32          `protobuf:"varint,8,opt,name=borrow_time,json=borrowTime,proto3" json:"borrow_time,omitempty"` //租借的时间，
	ValueTs    int32          `protobuf:"varint,9,opt,name=value_ts,json=valueTs,proto3" json:"value_ts,omitempty"`          //孵化的时候可以用来统计 剩余时间
}

func (x *BoxPokeInfo) Reset() {
	*x = BoxPokeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeBox_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoxPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoxPokeInfo) ProtoMessage() {}

func (x *BoxPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoxPokeInfo.ProtoReflect.Descriptor instead.
func (*BoxPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{1}
}

func (x *BoxPokeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoxPokeInfo) GetAround() bool {
	if x != nil {
		return x.Around
	}
	return false
}

func (x *BoxPokeInfo) GetBorrowType() PokeBorrowType {
	if x != nil {
		return x.BorrowType
	}
	return PokeBorrowType_BorrowTypeNone
}

func (x *BoxPokeInfo) GetBorrowTime() int32 {
	if x != nil {
		return x.BorrowTime
	}
	return 0
}

func (x *BoxPokeInfo) GetValueTs() int32 {
	if x != nil {
		return x.ValueTs
	}
	return 0
}

type PokeBoxExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PokeBoxExtra) Reset() {
	*x = PokeBoxExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeBox_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeBoxExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxExtra) ProtoMessage() {}

func (x *PokeBoxExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxExtra.ProtoReflect.Descriptor instead.
func (*PokeBoxExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{2}
}

type PokeBatchBoxExchange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exchanges []*PokeBoxExchange `protobuf:"bytes,1,rep,name=exchanges,proto3" json:"exchanges,omitempty"`
}

func (x *PokeBatchBoxExchange) Reset() {
	*x = PokeBatchBoxExchange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeBox_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeBatchBoxExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBatchBoxExchange) ProtoMessage() {}

func (x *PokeBatchBoxExchange) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBatchBoxExchange.ProtoReflect.Descriptor instead.
func (*PokeBatchBoxExchange) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{3}
}

func (x *PokeBatchBoxExchange) GetExchanges() []*PokeBoxExchange {
	if x != nil {
		return x.Exchanges
	}
	return nil
}

type PokeBoxExchange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceBox     int32       `protobuf:"varint,1,opt,name=source_box,json=sourceBox,proto3" json:"source_box,omitempty"` //PokeBox.index
	SourceBoxType PokeBoxType `protobuf:"varint,2,opt,name=source_box_type,json=sourceBoxType,proto3,enum=MainServer.PokeBoxType" json:"source_box_type,omitempty"`
	SourceLoc     int32       `protobuf:"varint,3,opt,name=source_loc,json=sourceLoc,proto3" json:"source_loc,omitempty"`
	TargetBox     int32       `protobuf:"varint,4,opt,name=target_box,json=targetBox,proto3" json:"target_box,omitempty"` //PokeBox.index
	TargetBoxType PokeBoxType `protobuf:"varint,5,opt,name=target_box_type,json=targetBoxType,proto3,enum=MainServer.PokeBoxType" json:"target_box_type,omitempty"`
	TargetLoc     int32       `protobuf:"varint,6,opt,name=target_loc,json=targetLoc,proto3" json:"target_loc,omitempty"`
	IsDelete      bool        `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
}

func (x *PokeBoxExchange) Reset() {
	*x = PokeBoxExchange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeBox_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeBoxExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxExchange) ProtoMessage() {}

func (x *PokeBoxExchange) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxExchange.ProtoReflect.Descriptor instead.
func (*PokeBoxExchange) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{4}
}

func (x *PokeBoxExchange) GetSourceBox() int32 {
	if x != nil {
		return x.SourceBox
	}
	return 0
}

func (x *PokeBoxExchange) GetSourceBoxType() PokeBoxType {
	if x != nil {
		return x.SourceBoxType
	}
	return PokeBoxType_normal
}

func (x *PokeBoxExchange) GetSourceLoc() int32 {
	if x != nil {
		return x.SourceLoc
	}
	return 0
}

func (x *PokeBoxExchange) GetTargetBox() int32 {
	if x != nil {
		return x.TargetBox
	}
	return 0
}

func (x *PokeBoxExchange) GetTargetBoxType() PokeBoxType {
	if x != nil {
		return x.TargetBoxType
	}
	return PokeBoxType_normal
}

func (x *PokeBoxExchange) GetTargetLoc() int32 {
	if x != nil {
		return x.TargetLoc
	}
	return 0
}

func (x *PokeBoxExchange) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

var File_MainServer_PokeBox_proto protoreflect.FileDescriptor

var file_MainServer_PokeBox_proto_rawDesc = []byte{
	0x0a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x42, 0x6f, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xf5, 0x02, 0x0a, 0x07, 0x50, 0x6f, 0x6b, 0x65, 0x42,
	0x6f, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x70,
	0x6f, 0x6b, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x2e,
	0x50, 0x6f, 0x6b, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65,
	0x73, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x6b, 0x65, 0x42, 0x6f, 0x78, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x1a, 0x51, 0x0a, 0x0a, 0x50,
	0x6f, 0x6b, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xae,
	0x01, 0x0a, 0x0b, 0x42, 0x6f, 0x78, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x73, 0x22,
	0x0e, 0x0a, 0x0c, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x45, 0x78, 0x74, 0x72, 0x61, 0x22,
	0x51, 0x0a, 0x14, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x78, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x22, 0xac, 0x02, 0x0a, 0x0f, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x6f, 0x78, 0x12, 0x3f, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x78, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65,
	0x42, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42,
	0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6c, 0x6f, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4c, 0x6f, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x62, 0x6f, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x42, 0x6f, 0x78, 0x12, 0x3f, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x62,
	0x6f, 0x78, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42,
	0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x42, 0x6f,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6c, 0x6f, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x4c, 0x6f, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x2a, 0xbd, 0x01, 0x0a, 0x0b, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x68, 0x61, 0x74, 0x63, 0x68, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x73, 0x61, 0x6c, 0x65,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x72, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06,
	0x61, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x62, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x4e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x0a, 0x12, 0x10, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x61, 0x6c, 0x48, 0x61, 0x74, 0x63, 0x68, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x65, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x6e, 0x74, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x0e, 0x12, 0x11,
	0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x10,
	0x0f, 0x2a, 0x53, 0x0a, 0x0e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x54, 0x79, 0x70,
	0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_PokeBox_proto_rawDescOnce sync.Once
	file_MainServer_PokeBox_proto_rawDescData = file_MainServer_PokeBox_proto_rawDesc
)

func file_MainServer_PokeBox_proto_rawDescGZIP() []byte {
	file_MainServer_PokeBox_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeBox_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_PokeBox_proto_rawDescData)
	})
	return file_MainServer_PokeBox_proto_rawDescData
}

var file_MainServer_PokeBox_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_PokeBox_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_PokeBox_proto_goTypes = []any{
	(PokeBoxType)(0),             // 0: MainServer.PokeBoxType
	(PokeBorrowType)(0),          // 1: MainServer.PokeBorrowType
	(*PokeBox)(nil),              // 2: MainServer.PokeBox
	(*BoxPokeInfo)(nil),          // 3: MainServer.BoxPokeInfo
	(*PokeBoxExtra)(nil),         // 4: MainServer.PokeBoxExtra
	(*PokeBatchBoxExchange)(nil), // 5: MainServer.PokeBatchBoxExchange
	(*PokeBoxExchange)(nil),      // 6: MainServer.PokeBoxExchange
	nil,                          // 7: MainServer.PokeBox.PokesEntry
}
var file_MainServer_PokeBox_proto_depIdxs = []int32{
	0, // 0: MainServer.PokeBox.type:type_name -> MainServer.PokeBoxType
	7, // 1: MainServer.PokeBox.pokes:type_name -> MainServer.PokeBox.PokesEntry
	4, // 2: MainServer.PokeBox.extra:type_name -> MainServer.PokeBoxExtra
	1, // 3: MainServer.BoxPokeInfo.borrow_type:type_name -> MainServer.PokeBorrowType
	6, // 4: MainServer.PokeBatchBoxExchange.exchanges:type_name -> MainServer.PokeBoxExchange
	0, // 5: MainServer.PokeBoxExchange.source_box_type:type_name -> MainServer.PokeBoxType
	0, // 6: MainServer.PokeBoxExchange.target_box_type:type_name -> MainServer.PokeBoxType
	3, // 7: MainServer.PokeBox.PokesEntry.value:type_name -> MainServer.BoxPokeInfo
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_MainServer_PokeBox_proto_init() }
func file_MainServer_PokeBox_proto_init() {
	if File_MainServer_PokeBox_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_PokeBox_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PokeBox); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeBox_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*BoxPokeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeBox_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PokeBoxExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeBox_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PokeBatchBoxExchange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeBox_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PokeBoxExchange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_PokeBox_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeBox_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeBox_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeBox_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeBox_proto_msgTypes,
	}.Build()
	File_MainServer_PokeBox_proto = out.File
	file_MainServer_PokeBox_proto_rawDesc = nil
	file_MainServer_PokeBox_proto_goTypes = nil
	file_MainServer_PokeBox_proto_depIdxs = nil
}
