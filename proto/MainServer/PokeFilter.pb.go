// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/PokeFilter.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeFilterSort int32

const (
	PokeFilterSort_NONE           PokeFilterSort = 0 // 无排序
	PokeFilterSort_PRICE_ASC      PokeFilterSort = 1 // 价格升序
	PokeFilterSort_PRICE_DESC     PokeFilterSort = 2 // 价格降序
	PokeFilterSort_LEVEL_ASC      PokeFilterSort = 3 // 等级升序
	PokeFilterSort_LEVEL_DESC     PokeFilterSort = 4 // 等级降序
	PokeFilterSort_UPDATE_TS_ASC  PokeFilterSort = 5 // 更新时间升序
	PokeFilterSort_UPDATE_TS_DESC PokeFilterSort = 6 // 更新时间降序
)

// Enum value maps for PokeFilterSort.
var (
	PokeFilterSort_name = map[int32]string{
		0: "NONE",
		1: "PRICE_ASC",
		2: "PRICE_DESC",
		3: "LEVEL_ASC",
		4: "LEVEL_DESC",
		5: "UPDATE_TS_ASC",
		6: "UPDATE_TS_DESC",
	}
	PokeFilterSort_value = map[string]int32{
		"NONE":           0,
		"PRICE_ASC":      1,
		"PRICE_DESC":     2,
		"LEVEL_ASC":      3,
		"LEVEL_DESC":     4,
		"UPDATE_TS_ASC":  5,
		"UPDATE_TS_DESC": 6,
	}
)

func (x PokeFilterSort) Enum() *PokeFilterSort {
	p := new(PokeFilterSort)
	*p = x
	return p
}

func (x PokeFilterSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeFilterSort) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeFilter_proto_enumTypes[0].Descriptor()
}

func (PokeFilterSort) Type() protoreflect.EnumType {
	return &file_MainServer_PokeFilter_proto_enumTypes[0]
}

func (x PokeFilterSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeFilterSort.Descriptor instead.
func (PokeFilterSort) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{0}
}

//	enum PokeFilterSort {
//	    ivs = 0; //默认用成长值总和排序
//	    evs = 1;
//	    price = 2;
//	}
//
// 在前端进行名称筛选 再传到后面
type PokeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinEvs         *PokeStat      `protobuf:"bytes,1,opt,name=min_evs,json=minEvs,proto3" json:"min_evs,omitempty"`
	MinIvs         *PokeStat      `protobuf:"bytes,2,opt,name=min_ivs,json=minIvs,proto3" json:"min_ivs,omitempty"`
	Names          []string       `protobuf:"bytes,3,rep,name=names,proto3" json:"names,omitempty"`
	Sort           PokeFilterSort `protobuf:"varint,4,opt,name=sort,proto3,enum=MainServer.PokeFilterSort" json:"sort,omitempty"`
	Page           int32          `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32          `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MinPrice       int32          `protobuf:"varint,7,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice       int32          `protobuf:"varint,8,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	MinSpecialCoin int32          `protobuf:"varint,9,opt,name=min_special_coin,json=minSpecialCoin,proto3" json:"min_special_coin,omitempty"`
	MaxSpecialCoin int32          `protobuf:"varint,10,opt,name=max_special_coin,json=maxSpecialCoin,proto3" json:"max_special_coin,omitempty"`
	UpdateTs       int64          `protobuf:"varint,11,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Sale           bool           `protobuf:"varint,12,opt,name=sale,proto3" json:"sale,omitempty"`
	Owner          bool           `protobuf:"varint,13,opt,name=owner,proto3" json:"owner,omitempty"`
	Gender         string         `protobuf:"bytes,14,opt,name=gender,proto3" json:"gender,omitempty"`
	Ability        string         `protobuf:"bytes,15,opt,name=ability,proto3" json:"ability,omitempty"`
}

func (x *PokeFilter) Reset() {
	*x = PokeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeFilter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeFilter) ProtoMessage() {}

func (x *PokeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeFilter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeFilter.ProtoReflect.Descriptor instead.
func (*PokeFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{0}
}

func (x *PokeFilter) GetMinEvs() *PokeStat {
	if x != nil {
		return x.MinEvs
	}
	return nil
}

func (x *PokeFilter) GetMinIvs() *PokeStat {
	if x != nil {
		return x.MinIvs
	}
	return nil
}

func (x *PokeFilter) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *PokeFilter) GetSort() PokeFilterSort {
	if x != nil {
		return x.Sort
	}
	return PokeFilterSort_NONE
}

func (x *PokeFilter) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokeFilter) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PokeFilter) GetMinPrice() int32 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *PokeFilter) GetMaxPrice() int32 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *PokeFilter) GetMinSpecialCoin() int32 {
	if x != nil {
		return x.MinSpecialCoin
	}
	return 0
}

func (x *PokeFilter) GetMaxSpecialCoin() int32 {
	if x != nil {
		return x.MaxSpecialCoin
	}
	return 0
}

func (x *PokeFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeFilter) GetSale() bool {
	if x != nil {
		return x.Sale
	}
	return false
}

func (x *PokeFilter) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

func (x *PokeFilter) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PokeFilter) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

type RpcPokeInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId int64 `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
}

func (x *RpcPokeInfoRequest) Reset() {
	*x = RpcPokeInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeFilter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcPokeInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeInfoRequest) ProtoMessage() {}

func (x *RpcPokeInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeFilter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeInfoRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeInfoRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeFilter_proto_rawDescGZIP(), []int{1}
}

func (x *RpcPokeInfoRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

var File_MainServer_PokeFilter_proto protoreflect.FileDescriptor

var file_MainServer_PokeFilter_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe8, 0x03, 0x0a, 0x0a, 0x50, 0x6f, 0x6b, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x2d, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x65, 0x76, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x6b, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x45, 0x76, 0x73, 0x12, 0x2d,
	0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x76, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x49, 0x76, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x6f, 0x6b, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f,
	0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x61, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73,
	0x61, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x2d, 0x0a, 0x12, 0x52,
	0x70, 0x63, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6b, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x2a, 0x7f, 0x0a, 0x0e, 0x50, 0x6f,
	0x6b, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x08, 0x0a, 0x04,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f,
	0x41, 0x53, 0x43, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x44,
	0x45, 0x53, 0x43, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41,
	0x53, 0x43, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x44, 0x45,
	0x53, 0x43, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x53, 0x5f, 0x41, 0x53, 0x43, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x54, 0x53, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x10, 0x06, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_MainServer_PokeFilter_proto_rawDescOnce sync.Once
	file_MainServer_PokeFilter_proto_rawDescData = file_MainServer_PokeFilter_proto_rawDesc
)

func file_MainServer_PokeFilter_proto_rawDescGZIP() []byte {
	file_MainServer_PokeFilter_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeFilter_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_PokeFilter_proto_rawDescData)
	})
	return file_MainServer_PokeFilter_proto_rawDescData
}

var file_MainServer_PokeFilter_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_PokeFilter_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_PokeFilter_proto_goTypes = []any{
	(PokeFilterSort)(0),        // 0: MainServer.PokeFilterSort
	(*PokeFilter)(nil),         // 1: MainServer.PokeFilter
	(*RpcPokeInfoRequest)(nil), // 2: MainServer.RpcPokeInfoRequest
	(*PokeStat)(nil),           // 3: MainServer.PokeStat
}
var file_MainServer_PokeFilter_proto_depIdxs = []int32{
	3, // 0: MainServer.PokeFilter.min_evs:type_name -> MainServer.PokeStat
	3, // 1: MainServer.PokeFilter.min_ivs:type_name -> MainServer.PokeStat
	0, // 2: MainServer.PokeFilter.sort:type_name -> MainServer.PokeFilterSort
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_PokeFilter_proto_init() }
func file_MainServer_PokeFilter_proto_init() {
	if File_MainServer_PokeFilter_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_PokeFilter_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PokeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeFilter_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RpcPokeInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_PokeFilter_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeFilter_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeFilter_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeFilter_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeFilter_proto_msgTypes,
	}.Build()
	File_MainServer_PokeFilter_proto = out.File
	file_MainServer_PokeFilter_proto_rawDesc = nil
	file_MainServer_PokeFilter_proto_goTypes = nil
	file_MainServer_PokeFilter_proto_depIdxs = nil
}
