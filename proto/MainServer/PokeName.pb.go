// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/PokeName.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeDataType int32

const (
	PokeDataType_POKE_DATA_TYPE_POKEMON PokeDataType = 0 //宝可梦
	PokeDataType_POKE_DATA_TYPE_MOVE    PokeDataType = 1 //招式
	PokeDataType_POKE_DATA_TYPE_ITEM    PokeDataType = 2 //道具
	PokeDataType_POKE_DATA_TYPE_ABILITY PokeDataType = 3 //特性
	PokeDataType_POKE_DATA_TYPE_TYPE    PokeDataType = 4 //属性
	PokeDataType_POKE_DATA_TYPE_NATURE  PokeDataType = 5 //性格
	// POKE_DATA_TYPE_GENDER = 6; //性别
	PokeDataType_POKE_DATA_TYPE_STAT                PokeDataType = 7  //属性
	PokeDataType_POKE_DATA_TYPE_REGION              PokeDataType = 8  //地区
	PokeDataType_POKE_DATA_TYPE_LOCATION            PokeDataType = 9  //地点
	PokeDataType_POKE_DATA_TYPE_LOCATIONAREA        PokeDataType = 10 //地点区域
	PokeDataType_POKE_DATA_TYPE_POKEMON_FLAVOR_TEXT PokeDataType = 20 //宝可梦描述
	PokeDataType_POKE_DATA_TYPE_MOVE_FLAVOR_TEXT    PokeDataType = 21 //招式描述
	PokeDataType_POKE_DATA_TYPE_ITEM_FLAVOR_TEXT    PokeDataType = 22 //道具描述
	PokeDataType_POKE_DATA_TYPE_ABILITY_FLAVOR_TEXT PokeDataType = 23 //特性描述
)

// Enum value maps for PokeDataType.
var (
	PokeDataType_name = map[int32]string{
		0:  "POKE_DATA_TYPE_POKEMON",
		1:  "POKE_DATA_TYPE_MOVE",
		2:  "POKE_DATA_TYPE_ITEM",
		3:  "POKE_DATA_TYPE_ABILITY",
		4:  "POKE_DATA_TYPE_TYPE",
		5:  "POKE_DATA_TYPE_NATURE",
		7:  "POKE_DATA_TYPE_STAT",
		8:  "POKE_DATA_TYPE_REGION",
		9:  "POKE_DATA_TYPE_LOCATION",
		10: "POKE_DATA_TYPE_LOCATIONAREA",
		20: "POKE_DATA_TYPE_POKEMON_FLAVOR_TEXT",
		21: "POKE_DATA_TYPE_MOVE_FLAVOR_TEXT",
		22: "POKE_DATA_TYPE_ITEM_FLAVOR_TEXT",
		23: "POKE_DATA_TYPE_ABILITY_FLAVOR_TEXT",
	}
	PokeDataType_value = map[string]int32{
		"POKE_DATA_TYPE_POKEMON":             0,
		"POKE_DATA_TYPE_MOVE":                1,
		"POKE_DATA_TYPE_ITEM":                2,
		"POKE_DATA_TYPE_ABILITY":             3,
		"POKE_DATA_TYPE_TYPE":                4,
		"POKE_DATA_TYPE_NATURE":              5,
		"POKE_DATA_TYPE_STAT":                7,
		"POKE_DATA_TYPE_REGION":              8,
		"POKE_DATA_TYPE_LOCATION":            9,
		"POKE_DATA_TYPE_LOCATIONAREA":        10,
		"POKE_DATA_TYPE_POKEMON_FLAVOR_TEXT": 20,
		"POKE_DATA_TYPE_MOVE_FLAVOR_TEXT":    21,
		"POKE_DATA_TYPE_ITEM_FLAVOR_TEXT":    22,
		"POKE_DATA_TYPE_ABILITY_FLAVOR_TEXT": 23,
	}
)

func (x PokeDataType) Enum() *PokeDataType {
	p := new(PokeDataType)
	*p = x
	return p
}

func (x PokeDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeName_proto_enumTypes[0].Descriptor()
}

func (PokeDataType) Type() protoreflect.EnumType {
	return &file_MainServer_PokeName_proto_enumTypes[0]
}

func (x PokeDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeDataType.Descriptor instead.
func (PokeDataType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeName_proto_rawDescGZIP(), []int{0}
}

type PokemonNameProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number             int32  `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	NameEn             string `protobuf:"bytes,2,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
	NameZhHans         string `protobuf:"bytes,3,opt,name=name_zh_hans,json=nameZhHans,proto3" json:"name_zh_hans,omitempty"`
	NameZhHant         string `protobuf:"bytes,4,opt,name=name_zh_hant,json=nameZhHant,proto3" json:"name_zh_hant,omitempty"`
	NameJa             string `protobuf:"bytes,5,opt,name=name_ja,json=nameJa,proto3" json:"name_ja,omitempty"`
	NameJaHrkt         string `protobuf:"bytes,6,opt,name=name_ja_hrkt,json=nameJaHrkt,proto3" json:"name_ja_hrkt,omitempty"`
	NameRoomaji        string `protobuf:"bytes,7,opt,name=name_roomaji,json=nameRoomaji,proto3" json:"name_roomaji,omitempty"`
	NameKo             string `protobuf:"bytes,8,opt,name=name_ko,json=nameKo,proto3" json:"name_ko,omitempty"`
	NameFr             string `protobuf:"bytes,9,opt,name=name_fr,json=nameFr,proto3" json:"name_fr,omitempty"`
	NameDe             string `protobuf:"bytes,10,opt,name=name_de,json=nameDe,proto3" json:"name_de,omitempty"`
	NameEs             string `protobuf:"bytes,11,opt,name=name_es,json=nameEs,proto3" json:"name_es,omitempty"`
	NameIt             string `protobuf:"bytes,12,opt,name=name_it,json=nameIt,proto3" json:"name_it,omitempty"`
	NameZhPinyinSimple string `protobuf:"bytes,13,opt,name=name_zh_pinyin_simple,json=nameZhPinyinSimple,proto3" json:"name_zh_pinyin_simple,omitempty"`
	NameZhPinyinAll    string `protobuf:"bytes,14,opt,name=name_zh_pinyin_all,json=nameZhPinyinAll,proto3" json:"name_zh_pinyin_all,omitempty"`
}

func (x *PokemonNameProto) Reset() {
	*x = PokemonNameProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeName_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokemonNameProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokemonNameProto) ProtoMessage() {}

func (x *PokemonNameProto) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeName_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokemonNameProto.ProtoReflect.Descriptor instead.
func (*PokemonNameProto) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeName_proto_rawDescGZIP(), []int{0}
}

func (x *PokemonNameProto) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *PokemonNameProto) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *PokemonNameProto) GetNameZhHans() string {
	if x != nil {
		return x.NameZhHans
	}
	return ""
}

func (x *PokemonNameProto) GetNameZhHant() string {
	if x != nil {
		return x.NameZhHant
	}
	return ""
}

func (x *PokemonNameProto) GetNameJa() string {
	if x != nil {
		return x.NameJa
	}
	return ""
}

func (x *PokemonNameProto) GetNameJaHrkt() string {
	if x != nil {
		return x.NameJaHrkt
	}
	return ""
}

func (x *PokemonNameProto) GetNameRoomaji() string {
	if x != nil {
		return x.NameRoomaji
	}
	return ""
}

func (x *PokemonNameProto) GetNameKo() string {
	if x != nil {
		return x.NameKo
	}
	return ""
}

func (x *PokemonNameProto) GetNameFr() string {
	if x != nil {
		return x.NameFr
	}
	return ""
}

func (x *PokemonNameProto) GetNameDe() string {
	if x != nil {
		return x.NameDe
	}
	return ""
}

func (x *PokemonNameProto) GetNameEs() string {
	if x != nil {
		return x.NameEs
	}
	return ""
}

func (x *PokemonNameProto) GetNameIt() string {
	if x != nil {
		return x.NameIt
	}
	return ""
}

func (x *PokemonNameProto) GetNameZhPinyinSimple() string {
	if x != nil {
		return x.NameZhPinyinSimple
	}
	return ""
}

func (x *PokemonNameProto) GetNameZhPinyinAll() string {
	if x != nil {
		return x.NameZhPinyinAll
	}
	return ""
}

type PokeDataLocalization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameId             string `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	Number             int32  `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
	NameEn             string `protobuf:"bytes,3,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
	NameZhHans         string `protobuf:"bytes,4,opt,name=name_zh_hans,json=nameZhHans,proto3" json:"name_zh_hans,omitempty"`
	NameZhHant         string `protobuf:"bytes,5,opt,name=name_zh_hant,json=nameZhHant,proto3" json:"name_zh_hant,omitempty"`
	NameJa             string `protobuf:"bytes,6,opt,name=name_ja,json=nameJa,proto3" json:"name_ja,omitempty"`
	NameJaHrkt         string `protobuf:"bytes,7,opt,name=name_ja_hrkt,json=nameJaHrkt,proto3" json:"name_ja_hrkt,omitempty"`
	NameRoomaji        string `protobuf:"bytes,8,opt,name=name_roomaji,json=nameRoomaji,proto3" json:"name_roomaji,omitempty"`
	NameKo             string `protobuf:"bytes,9,opt,name=name_ko,json=nameKo,proto3" json:"name_ko,omitempty"`
	NameFr             string `protobuf:"bytes,10,opt,name=name_fr,json=nameFr,proto3" json:"name_fr,omitempty"`
	NameDe             string `protobuf:"bytes,11,opt,name=name_de,json=nameDe,proto3" json:"name_de,omitempty"`
	NameEs             string `protobuf:"bytes,12,opt,name=name_es,json=nameEs,proto3" json:"name_es,omitempty"`
	NameIt             string `protobuf:"bytes,13,opt,name=name_it,json=nameIt,proto3" json:"name_it,omitempty"`
	NameZhPinyinSimple string `protobuf:"bytes,14,opt,name=name_zh_pinyin_simple,json=nameZhPinyinSimple,proto3" json:"name_zh_pinyin_simple,omitempty"`
	NameZhPinyinAll    string `protobuf:"bytes,15,opt,name=name_zh_pinyin_all,json=nameZhPinyinAll,proto3" json:"name_zh_pinyin_all,omitempty"`
	IsSpecial          bool   `protobuf:"varint,16,opt,name=is_special,json=isSpecial,proto3" json:"is_special,omitempty"`
}

func (x *PokeDataLocalization) Reset() {
	*x = PokeDataLocalization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeName_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeDataLocalization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeDataLocalization) ProtoMessage() {}

func (x *PokeDataLocalization) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeName_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeDataLocalization.ProtoReflect.Descriptor instead.
func (*PokeDataLocalization) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeName_proto_rawDescGZIP(), []int{1}
}

func (x *PokeDataLocalization) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *PokeDataLocalization) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *PokeDataLocalization) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

func (x *PokeDataLocalization) GetNameZhHans() string {
	if x != nil {
		return x.NameZhHans
	}
	return ""
}

func (x *PokeDataLocalization) GetNameZhHant() string {
	if x != nil {
		return x.NameZhHant
	}
	return ""
}

func (x *PokeDataLocalization) GetNameJa() string {
	if x != nil {
		return x.NameJa
	}
	return ""
}

func (x *PokeDataLocalization) GetNameJaHrkt() string {
	if x != nil {
		return x.NameJaHrkt
	}
	return ""
}

func (x *PokeDataLocalization) GetNameRoomaji() string {
	if x != nil {
		return x.NameRoomaji
	}
	return ""
}

func (x *PokeDataLocalization) GetNameKo() string {
	if x != nil {
		return x.NameKo
	}
	return ""
}

func (x *PokeDataLocalization) GetNameFr() string {
	if x != nil {
		return x.NameFr
	}
	return ""
}

func (x *PokeDataLocalization) GetNameDe() string {
	if x != nil {
		return x.NameDe
	}
	return ""
}

func (x *PokeDataLocalization) GetNameEs() string {
	if x != nil {
		return x.NameEs
	}
	return ""
}

func (x *PokeDataLocalization) GetNameIt() string {
	if x != nil {
		return x.NameIt
	}
	return ""
}

func (x *PokeDataLocalization) GetNameZhPinyinSimple() string {
	if x != nil {
		return x.NameZhPinyinSimple
	}
	return ""
}

func (x *PokeDataLocalization) GetNameZhPinyinAll() string {
	if x != nil {
		return x.NameZhPinyinAll
	}
	return ""
}

func (x *PokeDataLocalization) GetIsSpecial() bool {
	if x != nil {
		return x.IsSpecial
	}
	return false
}

type PokeDataLocalizationStoreValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameId      string                `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	Names       *PokeDataLocalization `protobuf:"bytes,2,opt,name=names,proto3" json:"names,omitempty"`
	FlavorTexts *PokeDataLocalization `protobuf:"bytes,3,opt,name=flavor_texts,json=flavorTexts,proto3" json:"flavor_texts,omitempty"`
}

func (x *PokeDataLocalizationStoreValue) Reset() {
	*x = PokeDataLocalizationStoreValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeName_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeDataLocalizationStoreValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeDataLocalizationStoreValue) ProtoMessage() {}

func (x *PokeDataLocalizationStoreValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeName_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeDataLocalizationStoreValue.ProtoReflect.Descriptor instead.
func (*PokeDataLocalizationStoreValue) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeName_proto_rawDescGZIP(), []int{2}
}

func (x *PokeDataLocalizationStoreValue) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *PokeDataLocalizationStoreValue) GetNames() *PokeDataLocalization {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *PokeDataLocalizationStoreValue) GetFlavorTexts() *PokeDataLocalization {
	if x != nil {
		return x.FlavorTexts
	}
	return nil
}

var File_MainServer_PokeName_proto protoreflect.FileDescriptor

var file_MainServer_PokeName_proto_rawDesc = []byte{
	0x0a, 0x19, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xc2, 0x03, 0x0a, 0x10, 0x50, 0x6f, 0x6b, 0x65,
	0x6d, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12, 0x20, 0x0a,
	0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x68, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5a, 0x68, 0x48, 0x61, 0x6e, 0x73, 0x12,
	0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x68, 0x61, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5a, 0x68, 0x48, 0x61, 0x6e,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6a, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x61, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x6a, 0x61, 0x5f, 0x68, 0x72, 0x6b, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x61, 0x48, 0x72, 0x6b, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x61, 0x6a, 0x69, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x61, 0x6a, 0x69, 0x12,
	0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x4b, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x66, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x46,
	0x72, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d,
	0x65, 0x45, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x49, 0x74, 0x12, 0x31, 0x0a, 0x15,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x70, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x5f, 0x73,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6e, 0x61, 0x6d,
	0x65, 0x5a, 0x68, 0x50, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x12,
	0x2b, 0x0a, 0x12, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x70, 0x69, 0x6e, 0x79, 0x69,
	0x6e, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x61, 0x6d,
	0x65, 0x5a, 0x68, 0x50, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x41, 0x6c, 0x6c, 0x22, 0xfe, 0x03, 0x0a,
	0x14, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12,
	0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x68, 0x61, 0x6e, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5a, 0x68, 0x48, 0x61, 0x6e,
	0x73, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x68, 0x61, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5a, 0x68, 0x48,
	0x61, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6a, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x61, 0x12, 0x20, 0x0a, 0x0c,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6a, 0x61, 0x5f, 0x68, 0x72, 0x6b, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x4a, 0x61, 0x48, 0x72, 0x6b, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x61, 0x6a, 0x69, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x61, 0x6a,
	0x69, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x6f, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x4b, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x66, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d,
	0x65, 0x46, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x61, 0x6d, 0x65, 0x45, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x49, 0x74, 0x12, 0x31,
	0x0a, 0x15, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x70, 0x69, 0x6e, 0x79, 0x69, 0x6e,
	0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6e,
	0x61, 0x6d, 0x65, 0x5a, 0x68, 0x50, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x53, 0x69, 0x6d, 0x70, 0x6c,
	0x65, 0x12, 0x2b, 0x0a, 0x12, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x68, 0x5f, 0x70, 0x69, 0x6e,
	0x79, 0x69, 0x6e, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e,
	0x61, 0x6d, 0x65, 0x5a, 0x68, 0x50, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x41, 0x6c, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x22, 0xb6, 0x01,
	0x0a, 0x1e, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x05, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x0c, 0x66, 0x6c, 0x61, 0x76, 0x6f, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x66, 0x6c, 0x61, 0x76, 0x6f,
	0x72, 0x54, 0x65, 0x78, 0x74, 0x73, 0x2a, 0xb8, 0x03, 0x0a, 0x0c, 0x50, 0x6f, 0x6b, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x4f, 0x4b, 0x45, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4b, 0x45, 0x4d, 0x4f,
	0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x03, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f,
	0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x10, 0x07, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x4b,
	0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x41, 0x52, 0x45, 0x41, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x4f, 0x4b, 0x45, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4b, 0x45, 0x4d, 0x4f,
	0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x56, 0x4f, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x14, 0x12,
	0x23, 0x0a, 0x1f, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x56, 0x4f, 0x52, 0x5f, 0x54, 0x45,
	0x58, 0x54, 0x10, 0x15, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x4f, 0x4b, 0x45, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x46, 0x4c, 0x41, 0x56,
	0x4f, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x16, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x4f, 0x4b,
	0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x46, 0x4c, 0x41, 0x56, 0x4f, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x17, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_PokeName_proto_rawDescOnce sync.Once
	file_MainServer_PokeName_proto_rawDescData = file_MainServer_PokeName_proto_rawDesc
)

func file_MainServer_PokeName_proto_rawDescGZIP() []byte {
	file_MainServer_PokeName_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeName_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_PokeName_proto_rawDescData)
	})
	return file_MainServer_PokeName_proto_rawDescData
}

var file_MainServer_PokeName_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_PokeName_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_MainServer_PokeName_proto_goTypes = []any{
	(PokeDataType)(0),                      // 0: MainServer.PokeDataType
	(*PokemonNameProto)(nil),               // 1: MainServer.PokemonNameProto
	(*PokeDataLocalization)(nil),           // 2: MainServer.PokeDataLocalization
	(*PokeDataLocalizationStoreValue)(nil), // 3: MainServer.PokeDataLocalizationStoreValue
}
var file_MainServer_PokeName_proto_depIdxs = []int32{
	2, // 0: MainServer.PokeDataLocalizationStoreValue.names:type_name -> MainServer.PokeDataLocalization
	2, // 1: MainServer.PokeDataLocalizationStoreValue.flavor_texts:type_name -> MainServer.PokeDataLocalization
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_PokeName_proto_init() }
func file_MainServer_PokeName_proto_init() {
	if File_MainServer_PokeName_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_PokeName_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PokemonNameProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeName_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PokeDataLocalization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_PokeName_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PokeDataLocalizationStoreValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_PokeName_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeName_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeName_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeName_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeName_proto_msgTypes,
	}.Build()
	File_MainServer_PokeName_proto = out.File
	file_MainServer_PokeName_proto_rawDesc = nil
	file_MainServer_PokeName_proto_goTypes = nil
	file_MainServer_PokeName_proto_depIdxs = nil
}
