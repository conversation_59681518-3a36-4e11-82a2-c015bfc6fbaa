// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/PokeTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid      int64   `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Name     string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Pokes    []*Poke `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
	CreateTs int64   `protobuf:"varint,5,opt,name=createTs,proto3" json:"createTs,omitempty"`
	UpdateTs int64   `protobuf:"varint,6,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
	HireTids []int64 `protobuf:"varint,7,rep,packed,name=hireTids,proto3" json:"hireTids,omitempty"` //租赁人
}

func (x *PokeTeam) Reset() {
	*x = PokeTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeam) ProtoMessage() {}

func (x *PokeTeam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeam.ProtoReflect.Descriptor instead.
func (*PokeTeam) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{0}
}

func (x *PokeTeam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeTeam) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeTeam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeTeam) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeTeam) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeTeam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeTeam) GetHireTids() []int64 {
	if x != nil {
		return x.HireTids
	}
	return nil
}

var File_MainServer_PokeTeam_proto protoreflect.FileDescriptor

var file_MainServer_PokeTeam_proto_rawDesc = []byte{
	0x0a, 0x19, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc,
	0x01, 0x0a, 0x08, 0x50, 0x6f, 0x6b, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x6b, 0x65, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x69, 0x72, 0x65, 0x54, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x68, 0x69, 0x72, 0x65, 0x54, 0x69, 0x64, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_PokeTeam_proto_rawDescOnce sync.Once
	file_MainServer_PokeTeam_proto_rawDescData = file_MainServer_PokeTeam_proto_rawDesc
)

func file_MainServer_PokeTeam_proto_rawDescGZIP() []byte {
	file_MainServer_PokeTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeTeam_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_PokeTeam_proto_rawDescData)
	})
	return file_MainServer_PokeTeam_proto_rawDescData
}

var file_MainServer_PokeTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_PokeTeam_proto_goTypes = []any{
	(*PokeTeam)(nil), // 0: MainServer.PokeTeam
	(*Poke)(nil),     // 1: MainServer.Poke
}
var file_MainServer_PokeTeam_proto_depIdxs = []int32{
	1, // 0: MainServer.PokeTeam.pokes:type_name -> MainServer.Poke
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_PokeTeam_proto_init() }
func file_MainServer_PokeTeam_proto_init() {
	if File_MainServer_PokeTeam_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_PokeTeam_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PokeTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_PokeTeam_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeTeam_proto_depIdxs,
		MessageInfos:      file_MainServer_PokeTeam_proto_msgTypes,
	}.Build()
	File_MainServer_PokeTeam_proto = out.File
	file_MainServer_PokeTeam_proto_rawDesc = nil
	file_MainServer_PokeTeam_proto_goTypes = nil
	file_MainServer_PokeTeam_proto_depIdxs = nil
}
