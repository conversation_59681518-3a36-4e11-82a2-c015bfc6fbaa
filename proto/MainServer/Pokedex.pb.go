// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Pokedex.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PSPokemonDataMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[string]*PSPokemonData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PSPokemonDataMap) Reset() {
	*x = PSPokemonDataMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Pokedex_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PSPokemonDataMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSPokemonDataMap) ProtoMessage() {}

func (x *PSPokemonDataMap) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSPokemonDataMap.ProtoReflect.Descriptor instead.
func (*PSPokemonDataMap) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{0}
}

func (x *PSPokemonDataMap) GetData() map[string]*PSPokemonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PSPokemonData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseForme       string         `protobuf:"bytes,1,opt,name=baseForme,proto3" json:"baseForme,omitempty"`
	BaseSpecies     string         `protobuf:"bytes,2,opt,name=baseSpecies,proto3" json:"baseSpecies,omitempty"`
	BattleOnly      []string       `protobuf:"bytes,3,rep,name=battleOnly,proto3" json:"battleOnly,omitempty"`
	CanGigantamax   string         `protobuf:"bytes,4,opt,name=canGigantamax,proto3" json:"canGigantamax,omitempty"`
	CanHatch        bool           `protobuf:"varint,5,opt,name=canHatch,proto3" json:"canHatch,omitempty"`
	CannotDynamax   bool           `protobuf:"varint,6,opt,name=cannotDynamax,proto3" json:"cannotDynamax,omitempty"`
	ChangesFrom     string         `protobuf:"bytes,7,opt,name=changesFrom,proto3" json:"changesFrom,omitempty"`
	Color           string         `protobuf:"bytes,8,opt,name=color,proto3" json:"color,omitempty"`
	CosmeticFormes  []string       `protobuf:"bytes,9,rep,name=cosmeticFormes,proto3" json:"cosmeticFormes,omitempty"`
	EggGroups       []string       `protobuf:"bytes,10,rep,name=eggGroups,proto3" json:"eggGroups,omitempty"`
	EvoCondition    string         `protobuf:"bytes,11,opt,name=evoCondition,proto3" json:"evoCondition,omitempty"`
	EvoItem         string         `protobuf:"bytes,12,opt,name=evoItem,proto3" json:"evoItem,omitempty"`
	EvoLevel        int32          `protobuf:"varint,13,opt,name=evoLevel,proto3" json:"evoLevel,omitempty"`
	EvoMove         string         `protobuf:"bytes,14,opt,name=evoMove,proto3" json:"evoMove,omitempty"`
	EvoRegion       string         `protobuf:"bytes,15,opt,name=evoRegion,proto3" json:"evoRegion,omitempty"`
	EvoType         string         `protobuf:"bytes,16,opt,name=evoType,proto3" json:"evoType,omitempty"`
	Evos            []string       `protobuf:"bytes,17,rep,name=evos,proto3" json:"evos,omitempty"`
	ForceTeraType   string         `protobuf:"bytes,18,opt,name=forceTeraType,proto3" json:"forceTeraType,omitempty"`
	Forme           string         `protobuf:"bytes,19,opt,name=forme,proto3" json:"forme,omitempty"`
	FormeOrder      []string       `protobuf:"bytes,20,rep,name=formeOrder,proto3" json:"formeOrder,omitempty"`
	Gen             int32          `protobuf:"varint,21,opt,name=gen,proto3" json:"gen,omitempty"`
	Gender          string         `protobuf:"bytes,22,opt,name=gender,proto3" json:"gender,omitempty"`
	Heightm         float32        `protobuf:"fixed32,23,opt,name=heightm,proto3" json:"heightm,omitempty"`
	IsNonstandard   string         `protobuf:"bytes,24,opt,name=isNonstandard,proto3" json:"isNonstandard,omitempty"`
	MaxHP           int32          `protobuf:"varint,25,opt,name=maxHP,proto3" json:"maxHP,omitempty"`
	Mother          string         `protobuf:"bytes,26,opt,name=mother,proto3" json:"mother,omitempty"`
	Name            string         `protobuf:"bytes,27,opt,name=name,proto3" json:"name,omitempty"`
	Num             int32          `protobuf:"varint,28,opt,name=num,proto3" json:"num,omitempty"`
	OtherFormes     []string       `protobuf:"bytes,29,rep,name=otherFormes,proto3" json:"otherFormes,omitempty"`
	Prevo           string         `protobuf:"bytes,30,opt,name=prevo,proto3" json:"prevo,omitempty"`
	RequiredAbility string         `protobuf:"bytes,31,opt,name=requiredAbility,proto3" json:"requiredAbility,omitempty"`
	RequiredItem    string         `protobuf:"bytes,32,opt,name=requiredItem,proto3" json:"requiredItem,omitempty"`
	RequiredItems   []string       `protobuf:"bytes,33,rep,name=requiredItems,proto3" json:"requiredItems,omitempty"`
	RequiredMove    string         `protobuf:"bytes,34,opt,name=requiredMove,proto3" json:"requiredMove,omitempty"`
	Tags            []string       `protobuf:"bytes,35,rep,name=tags,proto3" json:"tags,omitempty"`
	Tier            string         `protobuf:"bytes,36,opt,name=tier,proto3" json:"tier,omitempty"`
	Types           []string       `protobuf:"bytes,37,rep,name=types,proto3" json:"types,omitempty"`
	Weightkg        float32        `protobuf:"fixed32,38,opt,name=weightkg,proto3" json:"weightkg,omitempty"`
	CaptureRate     int32          `protobuf:"varint,39,opt,name=captureRate,proto3" json:"captureRate,omitempty"`
	GrowthRate      string         `protobuf:"bytes,40,opt,name=growthRate,proto3" json:"growthRate,omitempty"`
	FormsSwitchable bool           `protobuf:"varint,41,opt,name=formsSwitchable,proto3" json:"formsSwitchable,omitempty"`
	HatchCounter    int32          `protobuf:"varint,42,opt,name=hatchCounter,proto3" json:"hatchCounter,omitempty"`
	Abilities       *PSAbilities   `protobuf:"bytes,43,opt,name=abilities,proto3" json:"abilities,omitempty"`
	BaseStats       *PSBasestats   `protobuf:"bytes,44,opt,name=baseStats,proto3" json:"baseStats,omitempty"`
	GenderRatio     *PSGenderratio `protobuf:"bytes,45,opt,name=genderRatio,proto3" json:"genderRatio,omitempty"`
	Efforts         *PSBasestats   `protobuf:"bytes,46,opt,name=efforts,proto3" json:"efforts,omitempty"`
	BaseExperience  int32          `protobuf:"varint,47,opt,name=baseExperience,proto3" json:"baseExperience,omitempty"`
}

func (x *PSPokemonData) Reset() {
	*x = PSPokemonData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Pokedex_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PSPokemonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSPokemonData) ProtoMessage() {}

func (x *PSPokemonData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSPokemonData.ProtoReflect.Descriptor instead.
func (*PSPokemonData) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{1}
}

func (x *PSPokemonData) GetBaseForme() string {
	if x != nil {
		return x.BaseForme
	}
	return ""
}

func (x *PSPokemonData) GetBaseSpecies() string {
	if x != nil {
		return x.BaseSpecies
	}
	return ""
}

func (x *PSPokemonData) GetBattleOnly() []string {
	if x != nil {
		return x.BattleOnly
	}
	return nil
}

func (x *PSPokemonData) GetCanGigantamax() string {
	if x != nil {
		return x.CanGigantamax
	}
	return ""
}

func (x *PSPokemonData) GetCanHatch() bool {
	if x != nil {
		return x.CanHatch
	}
	return false
}

func (x *PSPokemonData) GetCannotDynamax() bool {
	if x != nil {
		return x.CannotDynamax
	}
	return false
}

func (x *PSPokemonData) GetChangesFrom() string {
	if x != nil {
		return x.ChangesFrom
	}
	return ""
}

func (x *PSPokemonData) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *PSPokemonData) GetCosmeticFormes() []string {
	if x != nil {
		return x.CosmeticFormes
	}
	return nil
}

func (x *PSPokemonData) GetEggGroups() []string {
	if x != nil {
		return x.EggGroups
	}
	return nil
}

func (x *PSPokemonData) GetEvoCondition() string {
	if x != nil {
		return x.EvoCondition
	}
	return ""
}

func (x *PSPokemonData) GetEvoItem() string {
	if x != nil {
		return x.EvoItem
	}
	return ""
}

func (x *PSPokemonData) GetEvoLevel() int32 {
	if x != nil {
		return x.EvoLevel
	}
	return 0
}

func (x *PSPokemonData) GetEvoMove() string {
	if x != nil {
		return x.EvoMove
	}
	return ""
}

func (x *PSPokemonData) GetEvoRegion() string {
	if x != nil {
		return x.EvoRegion
	}
	return ""
}

func (x *PSPokemonData) GetEvoType() string {
	if x != nil {
		return x.EvoType
	}
	return ""
}

func (x *PSPokemonData) GetEvos() []string {
	if x != nil {
		return x.Evos
	}
	return nil
}

func (x *PSPokemonData) GetForceTeraType() string {
	if x != nil {
		return x.ForceTeraType
	}
	return ""
}

func (x *PSPokemonData) GetForme() string {
	if x != nil {
		return x.Forme
	}
	return ""
}

func (x *PSPokemonData) GetFormeOrder() []string {
	if x != nil {
		return x.FormeOrder
	}
	return nil
}

func (x *PSPokemonData) GetGen() int32 {
	if x != nil {
		return x.Gen
	}
	return 0
}

func (x *PSPokemonData) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PSPokemonData) GetHeightm() float32 {
	if x != nil {
		return x.Heightm
	}
	return 0
}

func (x *PSPokemonData) GetIsNonstandard() string {
	if x != nil {
		return x.IsNonstandard
	}
	return ""
}

func (x *PSPokemonData) GetMaxHP() int32 {
	if x != nil {
		return x.MaxHP
	}
	return 0
}

func (x *PSPokemonData) GetMother() string {
	if x != nil {
		return x.Mother
	}
	return ""
}

func (x *PSPokemonData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PSPokemonData) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *PSPokemonData) GetOtherFormes() []string {
	if x != nil {
		return x.OtherFormes
	}
	return nil
}

func (x *PSPokemonData) GetPrevo() string {
	if x != nil {
		return x.Prevo
	}
	return ""
}

func (x *PSPokemonData) GetRequiredAbility() string {
	if x != nil {
		return x.RequiredAbility
	}
	return ""
}

func (x *PSPokemonData) GetRequiredItem() string {
	if x != nil {
		return x.RequiredItem
	}
	return ""
}

func (x *PSPokemonData) GetRequiredItems() []string {
	if x != nil {
		return x.RequiredItems
	}
	return nil
}

func (x *PSPokemonData) GetRequiredMove() string {
	if x != nil {
		return x.RequiredMove
	}
	return ""
}

func (x *PSPokemonData) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PSPokemonData) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *PSPokemonData) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *PSPokemonData) GetWeightkg() float32 {
	if x != nil {
		return x.Weightkg
	}
	return 0
}

func (x *PSPokemonData) GetCaptureRate() int32 {
	if x != nil {
		return x.CaptureRate
	}
	return 0
}

func (x *PSPokemonData) GetGrowthRate() string {
	if x != nil {
		return x.GrowthRate
	}
	return ""
}

func (x *PSPokemonData) GetFormsSwitchable() bool {
	if x != nil {
		return x.FormsSwitchable
	}
	return false
}

func (x *PSPokemonData) GetHatchCounter() int32 {
	if x != nil {
		return x.HatchCounter
	}
	return 0
}

func (x *PSPokemonData) GetAbilities() *PSAbilities {
	if x != nil {
		return x.Abilities
	}
	return nil
}

func (x *PSPokemonData) GetBaseStats() *PSBasestats {
	if x != nil {
		return x.BaseStats
	}
	return nil
}

func (x *PSPokemonData) GetGenderRatio() *PSGenderratio {
	if x != nil {
		return x.GenderRatio
	}
	return nil
}

func (x *PSPokemonData) GetEfforts() *PSBasestats {
	if x != nil {
		return x.Efforts
	}
	return nil
}

func (x *PSPokemonData) GetBaseExperience() int32 {
	if x != nil {
		return x.BaseExperience
	}
	return 0
}

type PSAbilities struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	H    string `protobuf:"bytes,1,opt,name=H,proto3" json:"H,omitempty"`
	S    string `protobuf:"bytes,2,opt,name=S,proto3" json:"S,omitempty"`
	Key0 string `protobuf:"bytes,3,opt,name=key0,proto3" json:"key0,omitempty"`
	Key1 string `protobuf:"bytes,4,opt,name=key1,proto3" json:"key1,omitempty"`
}

func (x *PSAbilities) Reset() {
	*x = PSAbilities{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Pokedex_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PSAbilities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSAbilities) ProtoMessage() {}

func (x *PSAbilities) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSAbilities.ProtoReflect.Descriptor instead.
func (*PSAbilities) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{2}
}

func (x *PSAbilities) GetH() string {
	if x != nil {
		return x.H
	}
	return ""
}

func (x *PSAbilities) GetS() string {
	if x != nil {
		return x.S
	}
	return ""
}

func (x *PSAbilities) GetKey0() string {
	if x != nil {
		return x.Key0
	}
	return ""
}

func (x *PSAbilities) GetKey1() string {
	if x != nil {
		return x.Key1
	}
	return ""
}

type PSBasestats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Atk int32 `protobuf:"varint,1,opt,name=atk,proto3" json:"atk,omitempty"`
	Def int32 `protobuf:"varint,2,opt,name=def,proto3" json:"def,omitempty"`
	Hp  int32 `protobuf:"varint,3,opt,name=hp,proto3" json:"hp,omitempty"`
	Spa int32 `protobuf:"varint,4,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd int32 `protobuf:"varint,5,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe int32 `protobuf:"varint,6,opt,name=spe,proto3" json:"spe,omitempty"`
}

func (x *PSBasestats) Reset() {
	*x = PSBasestats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Pokedex_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PSBasestats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSBasestats) ProtoMessage() {}

func (x *PSBasestats) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSBasestats.ProtoReflect.Descriptor instead.
func (*PSBasestats) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{3}
}

func (x *PSBasestats) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PSBasestats) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PSBasestats) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PSBasestats) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PSBasestats) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PSBasestats) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type PSGenderratio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	F float32 `protobuf:"fixed32,1,opt,name=F,proto3" json:"F,omitempty"`
	M float32 `protobuf:"fixed32,2,opt,name=M,proto3" json:"M,omitempty"`
}

func (x *PSGenderratio) Reset() {
	*x = PSGenderratio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Pokedex_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PSGenderratio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSGenderratio) ProtoMessage() {}

func (x *PSGenderratio) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSGenderratio.ProtoReflect.Descriptor instead.
func (*PSGenderratio) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{4}
}

func (x *PSGenderratio) GetF() float32 {
	if x != nil {
		return x.F
	}
	return 0
}

func (x *PSGenderratio) GetM() float32 {
	if x != nil {
		return x.M
	}
	return 0
}

var File_MainServer_Pokedex_proto protoreflect.FileDescriptor

var file_MainServer_Pokedex_proto_rawDesc = []byte{
	0x0a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b,
	0x65, 0x64, 0x65, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0xa2, 0x01, 0x0a, 0x10, 0x50, 0x53, 0x50, 0x6f, 0x6b,
	0x65, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x12, 0x3a, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x53, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x4d, 0x61, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x52, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x53, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xef, 0x0b, 0x0a, 0x0d,
	0x50, 0x53, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x62,
	0x61, 0x73, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x24, 0x0a,
	0x0d, 0x63, 0x61, 0x6e, 0x47, 0x69, 0x67, 0x61, 0x6e, 0x74, 0x61, 0x6d, 0x61, 0x78, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x47, 0x69, 0x67, 0x61, 0x6e, 0x74, 0x61,
	0x6d, 0x61, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x6e, 0x48, 0x61, 0x74, 0x63, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x48, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x61, 0x78,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x61, 0x78, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x46, 0x72, 0x6f, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x26, 0x0a,
	0x0e, 0x63, 0x6f, 0x73, 0x6d, 0x65, 0x74, 0x69, 0x63, 0x46, 0x6f, 0x72, 0x6d, 0x65, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x73, 0x6d, 0x65, 0x74, 0x69, 0x63, 0x46,
	0x6f, 0x72, 0x6d, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x67, 0x67, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x65, 0x67, 0x67, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x76, 0x6f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x76, 0x6f, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76, 0x6f, 0x49, 0x74,
	0x65, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x6f, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x76, 0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x76, 0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x76, 0x6f, 0x4d, 0x6f, 0x76, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x76, 0x6f, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x6f, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x6f, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76, 0x6f, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x65, 0x76, 0x6f, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x65,
	0x76, 0x6f, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x54, 0x65, 0x72, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x54, 0x65, 0x72, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x14, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x67, 0x65, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x67, 0x65,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x73, 0x4e, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x72, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x78,
	0x48, 0x50, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x78, 0x48, 0x50, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x75, 0x6d, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a,
	0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x65, 0x76, 0x6f, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x72, 0x65, 0x76, 0x6f, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x41, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x41, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x76, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x25,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x6b, 0x67, 0x18, 0x26, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x6b, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x6f, 0x72,
	0x6d, 0x73, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x65, 0x72, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x68, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x09, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x53, 0x41, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x09, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x35,
	0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x53, 0x42, 0x61, 0x73, 0x65, 0x73, 0x74, 0x61, 0x74, 0x73, 0x52, 0x09, 0x62, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x53, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0b, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x12, 0x31, 0x0a, 0x07, 0x65, 0x66, 0x66, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x53, 0x42, 0x61, 0x73, 0x65, 0x73, 0x74, 0x61, 0x74, 0x73, 0x52, 0x07, 0x65, 0x66,
	0x66, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x45, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x62,
	0x61, 0x73, 0x65, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x51, 0x0a,
	0x0b, 0x50, 0x53, 0x41, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x0c, 0x0a, 0x01,
	0x48, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x48, 0x12, 0x0c, 0x0a, 0x01, 0x53, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x53, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x30,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x30, 0x12, 0x12, 0x0a, 0x04,
	0x6b, 0x65, 0x79, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x31,
	0x22, 0x77, 0x0a, 0x0b, 0x50, 0x53, 0x42, 0x61, 0x73, 0x65, 0x73, 0x74, 0x61, 0x74, 0x73, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x74, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x74,
	0x6b, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x64, 0x65, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x68, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x73, 0x70, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x70, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x73, 0x70, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x70, 0x65, 0x22, 0x2b, 0x0a, 0x0d, 0x50, 0x53, 0x47,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x0c, 0x0a, 0x01, 0x46, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x46, 0x12, 0x0c, 0x0a, 0x01, 0x4d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x01, 0x4d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Pokedex_proto_rawDescOnce sync.Once
	file_MainServer_Pokedex_proto_rawDescData = file_MainServer_Pokedex_proto_rawDesc
)

func file_MainServer_Pokedex_proto_rawDescGZIP() []byte {
	file_MainServer_Pokedex_proto_rawDescOnce.Do(func() {
		file_MainServer_Pokedex_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Pokedex_proto_rawDescData)
	})
	return file_MainServer_Pokedex_proto_rawDescData
}

var file_MainServer_Pokedex_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_Pokedex_proto_goTypes = []any{
	(*PSPokemonDataMap)(nil), // 0: MainServer.PSPokemonDataMap
	(*PSPokemonData)(nil),    // 1: MainServer.PSPokemonData
	(*PSAbilities)(nil),      // 2: MainServer.PSAbilities
	(*PSBasestats)(nil),      // 3: MainServer.PSBasestats
	(*PSGenderratio)(nil),    // 4: MainServer.PSGenderratio
	nil,                      // 5: MainServer.PSPokemonDataMap.DataEntry
}
var file_MainServer_Pokedex_proto_depIdxs = []int32{
	5, // 0: MainServer.PSPokemonDataMap.data:type_name -> MainServer.PSPokemonDataMap.DataEntry
	2, // 1: MainServer.PSPokemonData.abilities:type_name -> MainServer.PSAbilities
	3, // 2: MainServer.PSPokemonData.baseStats:type_name -> MainServer.PSBasestats
	4, // 3: MainServer.PSPokemonData.genderRatio:type_name -> MainServer.PSGenderratio
	3, // 4: MainServer.PSPokemonData.efforts:type_name -> MainServer.PSBasestats
	1, // 5: MainServer.PSPokemonDataMap.DataEntry.value:type_name -> MainServer.PSPokemonData
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_MainServer_Pokedex_proto_init() }
func file_MainServer_Pokedex_proto_init() {
	if File_MainServer_Pokedex_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Pokedex_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PSPokemonDataMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Pokedex_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*PSPokemonData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Pokedex_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PSAbilities); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Pokedex_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PSBasestats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Pokedex_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PSGenderratio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Pokedex_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Pokedex_proto_goTypes,
		DependencyIndexes: file_MainServer_Pokedex_proto_depIdxs,
		MessageInfos:      file_MainServer_Pokedex_proto_msgTypes,
	}.Build()
	File_MainServer_Pokedex_proto = out.File
	file_MainServer_Pokedex_proto_rawDesc = nil
	file_MainServer_Pokedex_proto_goTypes = nil
	file_MainServer_Pokedex_proto_depIdxs = nil
}
