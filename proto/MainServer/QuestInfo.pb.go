// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/QuestInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 这个是任务的描述 训练师接的任务信息请查看TrainerQuestInfo
type QuestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId         int32            `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`                                         // quest id
	QuestType       QuestType        `protobuf:"varint,2,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"`         // 类型
	QuestLevel      int32            `protobuf:"varint,3,opt,name=quest_level,json=questLevel,proto3" json:"quest_level,omitempty"`                                // 等级 （难度？）
	QuestStatus     QuestStatus      `protobuf:"varint,4,opt,name=quest_status,json=questStatus,proto3,enum=MainServer.QuestStatus" json:"quest_status,omitempty"` // 状态
	QuestUnlockId   int32            `protobuf:"varint,5,opt,name=quest_unlock_id,json=questUnlockId,proto3" json:"quest_unlock_id,omitempty"`                     //解锁条件id
	LinearQuestIds  []int32          `protobuf:"varint,6,rep,packed,name=linear_quest_ids,json=linearQuestIds,proto3" json:"linear_quest_ids,omitempty"`           // 该任务的线性任务id列表
	SingleQuest     bool             `protobuf:"varint,7,opt,name=single_quest,json=singleQuest,proto3" json:"single_quest,omitempty"`                             // 是否为单任务
	CurrentQuestIds []int32          `protobuf:"varint,8,rep,packed,name=current_quest_ids,json=currentQuestIds,proto3" json:"current_quest_ids,omitempty"`        // 当前进度要完成的任务列表(非单任务)
	QuestStrict     *QuestStrictInfo `protobuf:"bytes,9,opt,name=quest_strict,json=questStrict,proto3" json:"quest_strict,omitempty"`                              // 接下这个任务后对玩家的限制
	// QuestRewardInfo quest_reward = 9; // 奖励
	QuestRewardId        int32               `protobuf:"varint,10,opt,name=quest_reward_id,json=questRewardId,proto3" json:"quest_reward_id,omitempty"`                        // 奖励id
	QuestStartTime       int64               `protobuf:"varint,11,opt,name=quest_start_time,json=questStartTime,proto3" json:"quest_start_time,omitempty"`                     // 开始时间(ts 秒级别)
	QuestEndTime         int64               `protobuf:"varint,12,opt,name=quest_end_time,json=questEndTime,proto3" json:"quest_end_time,omitempty"`                           // 结束时间(ts 秒级别)
	QuestRepeatLimit     int32               `protobuf:"varint,13,opt,name=quest_repeat_limit,json=questRepeatLimit,proto3" json:"quest_repeat_limit,omitempty"`               // 重复限制（一天最多只能完成这么多次）
	QuestRepeatInterval  int32               `protobuf:"varint,14,opt,name=quest_repeat_interval,json=questRepeatInterval,proto3" json:"quest_repeat_interval,omitempty"`      // 重复间隔
	QuestBroadcast       *QuestBroadcastInfo `protobuf:"bytes,15,opt,name=quest_broadcast,json=questBroadcast,proto3" json:"quest_broadcast,omitempty"`                        // 广播
	QuestCompleteId      int32               `protobuf:"varint,16,opt,name=quest_complete_id,json=questCompleteId,proto3" json:"quest_complete_id,omitempty"`                  //完成条件id
	QuestRepeatLimitTime int32               `protobuf:"varint,17,opt,name=quest_repeat_limit_time,json=questRepeatLimitTime,proto3" json:"quest_repeat_limit_time,omitempty"` // 限制时间（训练家接受任务后必须要在这个时间内完成任务）
}

func (x *QuestInfo) Reset() {
	*x = QuestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestInfo) ProtoMessage() {}

func (x *QuestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestInfo.ProtoReflect.Descriptor instead.
func (*QuestInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{0}
}

func (x *QuestInfo) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *QuestInfo) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

func (x *QuestInfo) GetQuestLevel() int32 {
	if x != nil {
		return x.QuestLevel
	}
	return 0
}

func (x *QuestInfo) GetQuestStatus() QuestStatus {
	if x != nil {
		return x.QuestStatus
	}
	return QuestStatus_QuestStatus_close
}

func (x *QuestInfo) GetQuestUnlockId() int32 {
	if x != nil {
		return x.QuestUnlockId
	}
	return 0
}

func (x *QuestInfo) GetLinearQuestIds() []int32 {
	if x != nil {
		return x.LinearQuestIds
	}
	return nil
}

func (x *QuestInfo) GetSingleQuest() bool {
	if x != nil {
		return x.SingleQuest
	}
	return false
}

func (x *QuestInfo) GetCurrentQuestIds() []int32 {
	if x != nil {
		return x.CurrentQuestIds
	}
	return nil
}

func (x *QuestInfo) GetQuestStrict() *QuestStrictInfo {
	if x != nil {
		return x.QuestStrict
	}
	return nil
}

func (x *QuestInfo) GetQuestRewardId() int32 {
	if x != nil {
		return x.QuestRewardId
	}
	return 0
}

func (x *QuestInfo) GetQuestStartTime() int64 {
	if x != nil {
		return x.QuestStartTime
	}
	return 0
}

func (x *QuestInfo) GetQuestEndTime() int64 {
	if x != nil {
		return x.QuestEndTime
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatLimit() int32 {
	if x != nil {
		return x.QuestRepeatLimit
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatInterval() int32 {
	if x != nil {
		return x.QuestRepeatInterval
	}
	return 0
}

func (x *QuestInfo) GetQuestBroadcast() *QuestBroadcastInfo {
	if x != nil {
		return x.QuestBroadcast
	}
	return nil
}

func (x *QuestInfo) GetQuestCompleteId() int32 {
	if x != nil {
		return x.QuestCompleteId
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatLimitTime() int32 {
	if x != nil {
		return x.QuestRepeatLimitTime
	}
	return 0
}

type QuestStrictInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestStrictId int32            `protobuf:"varint,1,opt,name=quest_strict_id,json=questStrictId,proto3" json:"quest_strict_id,omitempty"` // 限制id
	QuestStricts  []*TrainerStrict `protobuf:"bytes,2,rep,name=quest_stricts,json=questStricts,proto3" json:"quest_stricts,omitempty"`       // 限制类型
}

func (x *QuestStrictInfo) Reset() {
	*x = QuestStrictInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestStrictInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestStrictInfo) ProtoMessage() {}

func (x *QuestStrictInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestStrictInfo.ProtoReflect.Descriptor instead.
func (*QuestStrictInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{1}
}

func (x *QuestStrictInfo) GetQuestStrictId() int32 {
	if x != nil {
		return x.QuestStrictId
	}
	return 0
}

func (x *QuestStrictInfo) GetQuestStricts() []*TrainerStrict {
	if x != nil {
		return x.QuestStricts
	}
	return nil
}

type QuestUnlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestUnlockId         int32                       `protobuf:"varint,1,opt,name=quest_unlock_id,json=questUnlockId,proto3" json:"quest_unlock_id,omitempty"`                        // 解锁id 配置表中配置
	QuestUnlockConditions []*QuestUnlockConditionInfo `protobuf:"bytes,2,rep,name=quest_unlock_conditions,json=questUnlockConditions,proto3" json:"quest_unlock_conditions,omitempty"` // 解锁条件
}

func (x *QuestUnlockInfo) Reset() {
	*x = QuestUnlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestUnlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockInfo) ProtoMessage() {}

func (x *QuestUnlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{2}
}

func (x *QuestUnlockInfo) GetQuestUnlockId() int32 {
	if x != nil {
		return x.QuestUnlockId
	}
	return 0
}

func (x *QuestUnlockInfo) GetQuestUnlockConditions() []*QuestUnlockConditionInfo {
	if x != nil {
		return x.QuestUnlockConditions
	}
	return nil
}

type QuestUnlockConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestUnlockType QuestUnlockType     `protobuf:"varint,1,opt,name=quest_unlock_type,json=questUnlockType,proto3,enum=MainServer.QuestUnlockType" json:"quest_unlock_type,omitempty"`
	QuestConditions *QuestConditionInfo `protobuf:"bytes,2,opt,name=quest_conditions,json=questConditions,proto3" json:"quest_conditions,omitempty"` // 解锁条件
}

func (x *QuestUnlockConditionInfo) Reset() {
	*x = QuestUnlockConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestUnlockConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockConditionInfo) ProtoMessage() {}

func (x *QuestUnlockConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{3}
}

func (x *QuestUnlockConditionInfo) GetQuestUnlockType() QuestUnlockType {
	if x != nil {
		return x.QuestUnlockType
	}
	return QuestUnlockType_QuestUnlockType_level
}

func (x *QuestUnlockConditionInfo) GetQuestConditions() *QuestConditionInfo {
	if x != nil {
		return x.QuestConditions
	}
	return nil
}

type QuestConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestConditionNameId string `protobuf:"bytes,1,opt,name=quest_condition_name_id,json=questConditionNameId,proto3" json:"quest_condition_name_id,omitempty"` // 条件名称id(可能是poke的nameid或者item的nameid等等等)
	QuestConditionCount  int32  `protobuf:"varint,2,opt,name=quest_condition_count,json=questConditionCount,proto3" json:"quest_condition_count,omitempty"`     // 条件数量
	JsonValue            string `protobuf:"bytes,3,opt,name=json_value,json=jsonValue,proto3" json:"json_value,omitempty"`                                      // json值 (一些特别的配置（）)
}

func (x *QuestConditionInfo) Reset() {
	*x = QuestConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestConditionInfo) ProtoMessage() {}

func (x *QuestConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{4}
}

func (x *QuestConditionInfo) GetQuestConditionNameId() string {
	if x != nil {
		return x.QuestConditionNameId
	}
	return ""
}

func (x *QuestConditionInfo) GetQuestConditionCount() int32 {
	if x != nil {
		return x.QuestConditionCount
	}
	return 0
}

func (x *QuestConditionInfo) GetJsonValue() string {
	if x != nil {
		return x.JsonValue
	}
	return ""
}

type QuestCompleteConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestUnlockType QuestCompleteType   `protobuf:"varint,1,opt,name=quest_unlock_type,json=questUnlockType,proto3,enum=MainServer.QuestCompleteType" json:"quest_unlock_type,omitempty"`
	QuestConditions *QuestConditionInfo `protobuf:"bytes,2,opt,name=quest_conditions,json=questConditions,proto3" json:"quest_conditions,omitempty"` // 解锁条件
}

func (x *QuestCompleteConditionInfo) Reset() {
	*x = QuestCompleteConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestCompleteConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteConditionInfo) ProtoMessage() {}

func (x *QuestCompleteConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{5}
}

func (x *QuestCompleteConditionInfo) GetQuestUnlockType() QuestCompleteType {
	if x != nil {
		return x.QuestUnlockType
	}
	return QuestCompleteType_QuestCompleteType_none
}

func (x *QuestCompleteConditionInfo) GetQuestConditions() *QuestConditionInfo {
	if x != nil {
		return x.QuestConditions
	}
	return nil
}

type QuestCompleteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestCompleteId         int32                         `protobuf:"varint,1,opt,name=quest_complete_id,json=questCompleteId,proto3" json:"quest_complete_id,omitempty"`                        // 完成id 配置表中配置
	QuestCompleteConditions []*QuestCompleteConditionInfo `protobuf:"bytes,2,rep,name=quest_complete_conditions,json=questCompleteConditions,proto3" json:"quest_complete_conditions,omitempty"` // 完成条件
}

func (x *QuestCompleteInfo) Reset() {
	*x = QuestCompleteInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestCompleteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteInfo) ProtoMessage() {}

func (x *QuestCompleteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{6}
}

func (x *QuestCompleteInfo) GetQuestCompleteId() int32 {
	if x != nil {
		return x.QuestCompleteId
	}
	return 0
}

func (x *QuestCompleteInfo) GetQuestCompleteConditions() []*QuestCompleteConditionInfo {
	if x != nil {
		return x.QuestCompleteConditions
	}
	return nil
}

type QuestRewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestRewardId int32               `protobuf:"varint,1,opt,name=quest_reward_id,json=questRewardId,proto3" json:"quest_reward_id,omitempty"` // 奖励id 可以去配置表中读取
	QuestRewards  []*QuestRewardValue `protobuf:"bytes,2,rep,name=quest_rewards,json=questRewards,proto3" json:"quest_rewards,omitempty"`       // 奖励
}

func (x *QuestRewardInfo) Reset() {
	*x = QuestRewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestRewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestRewardInfo) ProtoMessage() {}

func (x *QuestRewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestRewardInfo.ProtoReflect.Descriptor instead.
func (*QuestRewardInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{7}
}

func (x *QuestRewardInfo) GetQuestRewardId() int32 {
	if x != nil {
		return x.QuestRewardId
	}
	return 0
}

func (x *QuestRewardInfo) GetQuestRewards() []*QuestRewardValue {
	if x != nil {
		return x.QuestRewards
	}
	return nil
}

type QuestRewardValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestRewardType      QuestRewardType               `protobuf:"varint,1,opt,name=quest_reward_type,json=questRewardType,proto3,enum=MainServer.QuestRewardType" json:"quest_reward_type,omitempty"`                                // 奖励类型
	QuestRewardRate      float32                       `protobuf:"fixed32,2,opt,name=quest_reward_rate,json=questRewardRate,proto3" json:"quest_reward_rate,omitempty"`                                                               // 奖励概率
	QuestRewardCount     int32                         `protobuf:"varint,3,opt,name=quest_reward_count,json=questRewardCount,proto3" json:"quest_reward_count,omitempty"`                                                             // 奖励数量
	QuestRewardCountRate QuestRewardValueCountRateType `protobuf:"varint,4,opt,name=quest_reward_count_rate,json=questRewardCountRate,proto3,enum=MainServer.QuestRewardValueCountRateType" json:"quest_reward_count_rate,omitempty"` // 奖励数量概率
	DayWholeNetlocked    int32                         `protobuf:"varint,5,opt,name=day_whole_netlocked,json=dayWholeNetlocked,proto3" json:"day_whole_netlocked,omitempty"`                                                          // 一天全网锁定数量  //todo
}

func (x *QuestRewardValue) Reset() {
	*x = QuestRewardValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestRewardValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestRewardValue) ProtoMessage() {}

func (x *QuestRewardValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestRewardValue.ProtoReflect.Descriptor instead.
func (*QuestRewardValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{8}
}

func (x *QuestRewardValue) GetQuestRewardType() QuestRewardType {
	if x != nil {
		return x.QuestRewardType
	}
	return QuestRewardType_QuestRewardType_None
}

func (x *QuestRewardValue) GetQuestRewardRate() float32 {
	if x != nil {
		return x.QuestRewardRate
	}
	return 0
}

func (x *QuestRewardValue) GetQuestRewardCount() int32 {
	if x != nil {
		return x.QuestRewardCount
	}
	return 0
}

func (x *QuestRewardValue) GetQuestRewardCountRate() QuestRewardValueCountRateType {
	if x != nil {
		return x.QuestRewardCountRate
	}
	return QuestRewardValueCountRateType_QuestRewardValueCountRateType_None
}

func (x *QuestRewardValue) GetDayWholeNetlocked() int32 {
	if x != nil {
		return x.DayWholeNetlocked
	}
	return 0
}

type QuestBroadcastInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestBroadcastType  QuestBroadcastType `protobuf:"varint,1,opt,name=quest_broadcast_type,json=questBroadcastType,proto3,enum=MainServer.QuestBroadcastType" json:"quest_broadcast_type,omitempty"` // 广播类型
	QuestBroadcastValue string             `protobuf:"bytes,2,opt,name=quest_broadcast_value,json=questBroadcastValue,proto3" json:"quest_broadcast_value,omitempty"`                                  // 广播type对应的value (不是广播内容) 比如说什么地区
}

func (x *QuestBroadcastInfo) Reset() {
	*x = QuestBroadcastInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestBroadcastInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestBroadcastInfo) ProtoMessage() {}

func (x *QuestBroadcastInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestBroadcastInfo.ProtoReflect.Descriptor instead.
func (*QuestBroadcastInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{9}
}

func (x *QuestBroadcastInfo) GetQuestBroadcastType() QuestBroadcastType {
	if x != nil {
		return x.QuestBroadcastType
	}
	return QuestBroadcastType_QuestBroadcast_none
}

func (x *QuestBroadcastInfo) GetQuestBroadcastValue() string {
	if x != nil {
		return x.QuestBroadcastValue
	}
	return ""
}

type QuestTypeValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestTypeValueId       int32  `protobuf:"varint,1,opt,name=quest_type_value_id,json=questTypeValueId,proto3" json:"quest_type_value_id,omitempty"`                    // quest值id
	QuestTypeValue         int64  `protobuf:"varint,2,opt,name=quest_type_value,json=questTypeValue,proto3" json:"quest_type_value,omitempty"`                            // quest值
	IsStringValueId        bool   `protobuf:"varint,3,opt,name=is_string_value_id,json=isStringValueId,proto3" json:"is_string_value_id,omitempty"`                       // 是否为字符串id
	IsStringValue          bool   `protobuf:"varint,4,opt,name=is_string_value,json=isStringValue,proto3" json:"is_string_value,omitempty"`                               // 是否为字符串value
	QuestTypeValueString   string `protobuf:"bytes,5,opt,name=quest_type_value_string,json=questTypeValueString,proto3" json:"quest_type_value_string,omitempty"`         // quest值字符串
	QuestTypeValueStringId string `protobuf:"bytes,6,opt,name=quest_type_value_string_id,json=questTypeValueStringId,proto3" json:"quest_type_value_string_id,omitempty"` // quest值字符串id
}

func (x *QuestTypeValue) Reset() {
	*x = QuestTypeValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestTypeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestTypeValue) ProtoMessage() {}

func (x *QuestTypeValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestTypeValue.ProtoReflect.Descriptor instead.
func (*QuestTypeValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{10}
}

func (x *QuestTypeValue) GetQuestTypeValueId() int32 {
	if x != nil {
		return x.QuestTypeValueId
	}
	return 0
}

func (x *QuestTypeValue) GetQuestTypeValue() int64 {
	if x != nil {
		return x.QuestTypeValue
	}
	return 0
}

func (x *QuestTypeValue) GetIsStringValueId() bool {
	if x != nil {
		return x.IsStringValueId
	}
	return false
}

func (x *QuestTypeValue) GetIsStringValue() bool {
	if x != nil {
		return x.IsStringValue
	}
	return false
}

func (x *QuestTypeValue) GetQuestTypeValueString() string {
	if x != nil {
		return x.QuestTypeValueString
	}
	return ""
}

func (x *QuestTypeValue) GetQuestTypeValueStringId() string {
	if x != nil {
		return x.QuestTypeValueStringId
	}
	return ""
}

var File_MainServer_QuestInfo_proto protoreflect.FileDescriptor

var file_MainServer_QuestInfo_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x06, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x3a, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x69, 0x6e, 0x65,
	0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x0f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64,
	0x63, 0x61, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x79, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x5c,
	0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f,
	0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xae, 0x01, 0x0a,
	0x18, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x11, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9e, 0x01,
	0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb2,
	0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a,
	0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x19, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x7c, 0x0a, 0x0f, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x47, 0x0a, 0x11,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x60, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x61, 0x79, 0x5f, 0x77, 0x68, 0x6f, 0x6c, 0x65, 0x5f, 0x6e,
	0x65, 0x74, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x64, 0x61, 0x79, 0x57, 0x68, 0x6f, 0x6c, 0x65, 0x4e, 0x65, 0x74, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x22, 0x9a, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64,
	0x63, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x14, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f,
	0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb1,
	0x02, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x2d, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x69, 0x73, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x35, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x3a, 0x0a, 0x1a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_QuestInfo_proto_rawDescOnce sync.Once
	file_MainServer_QuestInfo_proto_rawDescData = file_MainServer_QuestInfo_proto_rawDesc
)

func file_MainServer_QuestInfo_proto_rawDescGZIP() []byte {
	file_MainServer_QuestInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_QuestInfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_QuestInfo_proto_rawDescData)
	})
	return file_MainServer_QuestInfo_proto_rawDescData
}

var file_MainServer_QuestInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_MainServer_QuestInfo_proto_goTypes = []any{
	(*QuestInfo)(nil),                  // 0: MainServer.QuestInfo
	(*QuestStrictInfo)(nil),            // 1: MainServer.QuestStrictInfo
	(*QuestUnlockInfo)(nil),            // 2: MainServer.QuestUnlockInfo
	(*QuestUnlockConditionInfo)(nil),   // 3: MainServer.QuestUnlockConditionInfo
	(*QuestConditionInfo)(nil),         // 4: MainServer.QuestConditionInfo
	(*QuestCompleteConditionInfo)(nil), // 5: MainServer.QuestCompleteConditionInfo
	(*QuestCompleteInfo)(nil),          // 6: MainServer.QuestCompleteInfo
	(*QuestRewardInfo)(nil),            // 7: MainServer.QuestRewardInfo
	(*QuestRewardValue)(nil),           // 8: MainServer.QuestRewardValue
	(*QuestBroadcastInfo)(nil),         // 9: MainServer.QuestBroadcastInfo
	(*QuestTypeValue)(nil),             // 10: MainServer.QuestTypeValue
	(QuestType)(0),                     // 11: MainServer.QuestType
	(QuestStatus)(0),                   // 12: MainServer.QuestStatus
	(*TrainerStrict)(nil),              // 13: MainServer.TrainerStrict
	(QuestUnlockType)(0),               // 14: MainServer.QuestUnlockType
	(QuestCompleteType)(0),             // 15: MainServer.QuestCompleteType
	(QuestRewardType)(0),               // 16: MainServer.QuestRewardType
	(QuestRewardValueCountRateType)(0), // 17: MainServer.QuestRewardValueCountRateType
	(QuestBroadcastType)(0),            // 18: MainServer.QuestBroadcastType
}
var file_MainServer_QuestInfo_proto_depIdxs = []int32{
	11, // 0: MainServer.QuestInfo.quest_type:type_name -> MainServer.QuestType
	12, // 1: MainServer.QuestInfo.quest_status:type_name -> MainServer.QuestStatus
	1,  // 2: MainServer.QuestInfo.quest_strict:type_name -> MainServer.QuestStrictInfo
	9,  // 3: MainServer.QuestInfo.quest_broadcast:type_name -> MainServer.QuestBroadcastInfo
	13, // 4: MainServer.QuestStrictInfo.quest_stricts:type_name -> MainServer.TrainerStrict
	3,  // 5: MainServer.QuestUnlockInfo.quest_unlock_conditions:type_name -> MainServer.QuestUnlockConditionInfo
	14, // 6: MainServer.QuestUnlockConditionInfo.quest_unlock_type:type_name -> MainServer.QuestUnlockType
	4,  // 7: MainServer.QuestUnlockConditionInfo.quest_conditions:type_name -> MainServer.QuestConditionInfo
	15, // 8: MainServer.QuestCompleteConditionInfo.quest_unlock_type:type_name -> MainServer.QuestCompleteType
	4,  // 9: MainServer.QuestCompleteConditionInfo.quest_conditions:type_name -> MainServer.QuestConditionInfo
	5,  // 10: MainServer.QuestCompleteInfo.quest_complete_conditions:type_name -> MainServer.QuestCompleteConditionInfo
	8,  // 11: MainServer.QuestRewardInfo.quest_rewards:type_name -> MainServer.QuestRewardValue
	16, // 12: MainServer.QuestRewardValue.quest_reward_type:type_name -> MainServer.QuestRewardType
	17, // 13: MainServer.QuestRewardValue.quest_reward_count_rate:type_name -> MainServer.QuestRewardValueCountRateType
	18, // 14: MainServer.QuestBroadcastInfo.quest_broadcast_type:type_name -> MainServer.QuestBroadcastType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_MainServer_QuestInfo_proto_init() }
func file_MainServer_QuestInfo_proto_init() {
	if File_MainServer_QuestInfo_proto != nil {
		return
	}
	file_MainServer_QuestType_proto_init()
	file_MainServer_TrainerStrict_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_QuestInfo_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*QuestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*QuestStrictInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*QuestUnlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*QuestUnlockConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*QuestConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*QuestCompleteConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QuestCompleteInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*QuestRewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*QuestRewardValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*QuestBroadcastInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*QuestTypeValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_QuestInfo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_QuestInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_QuestInfo_proto_depIdxs,
		MessageInfos:      file_MainServer_QuestInfo_proto_msgTypes,
	}.Build()
	File_MainServer_QuestInfo_proto = out.File
	file_MainServer_QuestInfo_proto_rawDesc = nil
	file_MainServer_QuestInfo_proto_goTypes = nil
	file_MainServer_QuestInfo_proto_depIdxs = nil
}
