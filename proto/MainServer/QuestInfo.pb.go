// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/QuestInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QuestInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestInfos   []*QuestInfo       `protobuf:"bytes,1,rep,name=quest_infos,json=questInfos,proto3" json:"quest_infos,omitempty"`
	QuestStricts []*QuestStrictInfo `protobuf:"bytes,2,rep,name=quest_stricts,json=questStricts,proto3" json:"quest_stricts,omitempty"`
	// repeated QuestUnlockInfo quest_unlocks = 3;
	QuestUnlockMap map[int32]*QuestUnlockInfo `protobuf:"bytes,3,rep,name=quest_unlock_map,json=questUnlockMap,proto3" json:"quest_unlock_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// repeated QuestCompleteInfo quest_completes = 4;
	QuestCompleteMap map[int32]*QuestCompleteInfo `protobuf:"bytes,4,rep,name=quest_complete_map,json=questCompleteMap,proto3" json:"quest_complete_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// repeated QuestRewardInfo quest_rewards = 5;
	QuestRewardsMap map[int32]*QuestRewardInfo `protobuf:"bytes,5,rep,name=quest_rewards_map,json=questRewardsMap,proto3" json:"quest_rewards_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	QuestTypeValues []*QuestTypeValue          `protobuf:"bytes,6,rep,name=quest_type_values,json=questTypeValues,proto3" json:"quest_type_values,omitempty"` // quest值
}

func (x *QuestInfoList) Reset() {
	*x = QuestInfoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestInfoList) ProtoMessage() {}

func (x *QuestInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestInfoList.ProtoReflect.Descriptor instead.
func (*QuestInfoList) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{0}
}

func (x *QuestInfoList) GetQuestInfos() []*QuestInfo {
	if x != nil {
		return x.QuestInfos
	}
	return nil
}

func (x *QuestInfoList) GetQuestStricts() []*QuestStrictInfo {
	if x != nil {
		return x.QuestStricts
	}
	return nil
}

func (x *QuestInfoList) GetQuestUnlockMap() map[int32]*QuestUnlockInfo {
	if x != nil {
		return x.QuestUnlockMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestCompleteMap() map[int32]*QuestCompleteInfo {
	if x != nil {
		return x.QuestCompleteMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestRewardsMap() map[int32]*QuestRewardInfo {
	if x != nil {
		return x.QuestRewardsMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestTypeValues() []*QuestTypeValue {
	if x != nil {
		return x.QuestTypeValues
	}
	return nil
}

// 这个是任务的描述 训练师接的任务信息请查看TrainerQuestInfo
type QuestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId         int32            `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`                                         // quest id
	QuestType       QuestType        `protobuf:"varint,2,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"`         // 类型
	QuestLevel      int32            `protobuf:"varint,3,opt,name=quest_level,json=questLevel,proto3" json:"quest_level,omitempty"`                                // 等级 （难度？）
	QuestStatus     QuestStatus      `protobuf:"varint,4,opt,name=quest_status,json=questStatus,proto3,enum=MainServer.QuestStatus" json:"quest_status,omitempty"` // 状态
	QuestUnlockInfo *QuestUnlockInfo `protobuf:"bytes,5,opt,name=quest_unlock_info,json=questUnlockInfo,proto3" json:"quest_unlock_info,omitempty"`                //解锁条件
	LinearQuests    *QuestListInfo   `protobuf:"bytes,6,opt,name=linear_quests,json=linearQuests,proto3" json:"linear_quests,omitempty"`                           // 该任务的线性任务id列表
	SingleQuest     bool             `protobuf:"varint,7,opt,name=single_quest,json=singleQuest,proto3" json:"single_quest,omitempty"`                             // 是否为单任务 //如果是单任务，但是current又有多个则随机 //或者指定（npc选择的时候应该）
	CurrentQuests   *QuestListInfo   `protobuf:"bytes,8,opt,name=current_quests,json=currentQuests,proto3" json:"current_quests,omitempty"`                        // 当前进度要完成的任务列表(非单任务)
	QuestStrict     *QuestStrictInfo `protobuf:"bytes,9,opt,name=quest_strict,json=questStrict,proto3" json:"quest_strict,omitempty"`                              // 接下这个任务后对玩家的限制
	// QuestRewardInfo quest_reward = 9; // 奖励
	QuestRewardInfo      *QuestRewardInfo    `protobuf:"bytes,10,opt,name=quest_reward_info,json=questRewardInfo,proto3" json:"quest_reward_info,omitempty"`                   // 奖励
	QuestStartTime       int64               `protobuf:"varint,11,opt,name=quest_start_time,json=questStartTime,proto3" json:"quest_start_time,omitempty"`                     // 开始时间(ts 秒级别)
	QuestEndTime         int64               `protobuf:"varint,12,opt,name=quest_end_time,json=questEndTime,proto3" json:"quest_end_time,omitempty"`                           // 结束时间(ts 秒级别)
	QuestRepeatLimit     int32               `protobuf:"varint,13,opt,name=quest_repeat_limit,json=questRepeatLimit,proto3" json:"quest_repeat_limit,omitempty"`               // 重复限制（一天最多只能完成这么多次）
	QuestRepeatInterval  int32               `protobuf:"varint,14,opt,name=quest_repeat_interval,json=questRepeatInterval,proto3" json:"quest_repeat_interval,omitempty"`      // 重复间隔
	QuestBroadcast       *QuestBroadcastInfo `protobuf:"bytes,15,opt,name=quest_broadcast,json=questBroadcast,proto3" json:"quest_broadcast,omitempty"`                        // 广播
	QuestCompleteInfo    *QuestCompleteInfo  `protobuf:"bytes,16,opt,name=quest_complete_info,json=questCompleteInfo,proto3" json:"quest_complete_info,omitempty"`             //完成条件id
	QuestRepeatLimitTime int32               `protobuf:"varint,17,opt,name=quest_repeat_limit_time,json=questRepeatLimitTime,proto3" json:"quest_repeat_limit_time,omitempty"` // 限制时间（训练家接受任务后必须要在这个时间内完成任务）
	Version              int32               `protobuf:"varint,18,opt,name=version,proto3" json:"version,omitempty"`                                                           // 版本号
}

func (x *QuestInfo) Reset() {
	*x = QuestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestInfo) ProtoMessage() {}

func (x *QuestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestInfo.ProtoReflect.Descriptor instead.
func (*QuestInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{1}
}

func (x *QuestInfo) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *QuestInfo) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

func (x *QuestInfo) GetQuestLevel() int32 {
	if x != nil {
		return x.QuestLevel
	}
	return 0
}

func (x *QuestInfo) GetQuestStatus() QuestStatus {
	if x != nil {
		return x.QuestStatus
	}
	return QuestStatus_QuestStatus_close
}

func (x *QuestInfo) GetQuestUnlockInfo() *QuestUnlockInfo {
	if x != nil {
		return x.QuestUnlockInfo
	}
	return nil
}

func (x *QuestInfo) GetLinearQuests() *QuestListInfo {
	if x != nil {
		return x.LinearQuests
	}
	return nil
}

func (x *QuestInfo) GetSingleQuest() bool {
	if x != nil {
		return x.SingleQuest
	}
	return false
}

func (x *QuestInfo) GetCurrentQuests() *QuestListInfo {
	if x != nil {
		return x.CurrentQuests
	}
	return nil
}

func (x *QuestInfo) GetQuestStrict() *QuestStrictInfo {
	if x != nil {
		return x.QuestStrict
	}
	return nil
}

func (x *QuestInfo) GetQuestRewardInfo() *QuestRewardInfo {
	if x != nil {
		return x.QuestRewardInfo
	}
	return nil
}

func (x *QuestInfo) GetQuestStartTime() int64 {
	if x != nil {
		return x.QuestStartTime
	}
	return 0
}

func (x *QuestInfo) GetQuestEndTime() int64 {
	if x != nil {
		return x.QuestEndTime
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatLimit() int32 {
	if x != nil {
		return x.QuestRepeatLimit
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatInterval() int32 {
	if x != nil {
		return x.QuestRepeatInterval
	}
	return 0
}

func (x *QuestInfo) GetQuestBroadcast() *QuestBroadcastInfo {
	if x != nil {
		return x.QuestBroadcast
	}
	return nil
}

func (x *QuestInfo) GetQuestCompleteInfo() *QuestCompleteInfo {
	if x != nil {
		return x.QuestCompleteInfo
	}
	return nil
}

func (x *QuestInfo) GetQuestRepeatLimitTime() int32 {
	if x != nil {
		return x.QuestRepeatLimitTime
	}
	return 0
}

func (x *QuestInfo) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type QuestListInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasList  bool    `protobuf:"varint,1,opt,name=has_list,json=hasList,proto3" json:"has_list,omitempty"`           // 是否有线性任务
	QuestIds []int32 `protobuf:"varint,2,rep,packed,name=quest_ids,json=questIds,proto3" json:"quest_ids,omitempty"` // 任务id列表
}

func (x *QuestListInfo) Reset() {
	*x = QuestListInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestListInfo) ProtoMessage() {}

func (x *QuestListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestListInfo.ProtoReflect.Descriptor instead.
func (*QuestListInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{2}
}

func (x *QuestListInfo) GetHasList() bool {
	if x != nil {
		return x.HasList
	}
	return false
}

func (x *QuestListInfo) GetQuestIds() []int32 {
	if x != nil {
		return x.QuestIds
	}
	return nil
}

type QuestStrictInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestStrictId int32            `protobuf:"varint,1,opt,name=quest_strict_id,json=questStrictId,proto3" json:"quest_strict_id,omitempty"` // 限制id
	QuestStricts  []*TrainerStrict `protobuf:"bytes,2,rep,name=quest_stricts,json=questStricts,proto3" json:"quest_stricts,omitempty"`       // 限制类型
}

func (x *QuestStrictInfo) Reset() {
	*x = QuestStrictInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestStrictInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestStrictInfo) ProtoMessage() {}

func (x *QuestStrictInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestStrictInfo.ProtoReflect.Descriptor instead.
func (*QuestStrictInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{3}
}

func (x *QuestStrictInfo) GetQuestStrictId() int32 {
	if x != nil {
		return x.QuestStrictId
	}
	return 0
}

func (x *QuestStrictInfo) GetQuestStricts() []*TrainerStrict {
	if x != nil {
		return x.QuestStricts
	}
	return nil
}

type QuestUnlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestUnlockId         int32                       `protobuf:"varint,1,opt,name=quest_unlock_id,json=questUnlockId,proto3" json:"quest_unlock_id,omitempty"`                        // 解锁id 配置表中配置
	QuestUnlockConditions []*QuestUnlockConditionInfo `protobuf:"bytes,2,rep,name=quest_unlock_conditions,json=questUnlockConditions,proto3" json:"quest_unlock_conditions,omitempty"` // 解锁条件
}

func (x *QuestUnlockInfo) Reset() {
	*x = QuestUnlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestUnlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockInfo) ProtoMessage() {}

func (x *QuestUnlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{4}
}

func (x *QuestUnlockInfo) GetQuestUnlockId() int32 {
	if x != nil {
		return x.QuestUnlockId
	}
	return 0
}

func (x *QuestUnlockInfo) GetQuestUnlockConditions() []*QuestUnlockConditionInfo {
	if x != nil {
		return x.QuestUnlockConditions
	}
	return nil
}

type QuestUnlockConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestUnlockType QuestUnlockType     `protobuf:"varint,1,opt,name=quest_unlock_type,json=questUnlockType,proto3,enum=MainServer.QuestUnlockType" json:"quest_unlock_type,omitempty"`
	QuestCondition  *QuestConditionInfo `protobuf:"bytes,2,opt,name=quest_condition,json=questCondition,proto3" json:"quest_condition,omitempty"` // 解锁条件
}

func (x *QuestUnlockConditionInfo) Reset() {
	*x = QuestUnlockConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestUnlockConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockConditionInfo) ProtoMessage() {}

func (x *QuestUnlockConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{5}
}

func (x *QuestUnlockConditionInfo) GetQuestUnlockType() QuestUnlockType {
	if x != nil {
		return x.QuestUnlockType
	}
	return QuestUnlockType_QuestUnlockType_none
}

func (x *QuestUnlockConditionInfo) GetQuestCondition() *QuestConditionInfo {
	if x != nil {
		return x.QuestCondition
	}
	return nil
}

type QuestConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConditionNameId string `protobuf:"bytes,1,opt,name=condition_name_id,json=conditionNameId,proto3" json:"condition_name_id,omitempty"` // 条件名称id(可能是poke的nameid或者item的nameid等等等)
	ConditionCount  int32  `protobuf:"varint,2,opt,name=condition_count,json=conditionCount,proto3" json:"condition_count,omitempty"`     // 条件数量
	JsonValue       string `protobuf:"bytes,3,opt,name=json_value,json=jsonValue,proto3" json:"json_value,omitempty"`                     // json值 (一些特别的配置（）)
	TimeLimit       int32  `protobuf:"varint,4,opt,name=time_limit,json=timeLimit,proto3" json:"time_limit,omitempty"`                    // 时间限制(秒) //比如任务是要在这个时间范围内的，超出后就无效了
	IsUsed          bool   `protobuf:"varint,5,opt,name=is_used,json=isUsed,proto3" json:"is_used,omitempty"`                             // 是否使用
	IsAdd           bool   `protobuf:"varint,6,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`                                // 是否为增加条件 (是否给予道具或poke，比如与某poke并肩作战)
}

func (x *QuestConditionInfo) Reset() {
	*x = QuestConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestConditionInfo) ProtoMessage() {}

func (x *QuestConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{6}
}

func (x *QuestConditionInfo) GetConditionNameId() string {
	if x != nil {
		return x.ConditionNameId
	}
	return ""
}

func (x *QuestConditionInfo) GetConditionCount() int32 {
	if x != nil {
		return x.ConditionCount
	}
	return 0
}

func (x *QuestConditionInfo) GetJsonValue() string {
	if x != nil {
		return x.JsonValue
	}
	return ""
}

func (x *QuestConditionInfo) GetTimeLimit() int32 {
	if x != nil {
		return x.TimeLimit
	}
	return 0
}

func (x *QuestConditionInfo) GetIsUsed() bool {
	if x != nil {
		return x.IsUsed
	}
	return false
}

func (x *QuestConditionInfo) GetIsAdd() bool {
	if x != nil {
		return x.IsAdd
	}
	return false
}

type QuestCompleteConditionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestCompleteType QuestCompleteType   `protobuf:"varint,1,opt,name=quest_complete_type,json=questCompleteType,proto3,enum=MainServer.QuestCompleteType" json:"quest_complete_type,omitempty"`
	QuestCondition    *QuestConditionInfo `protobuf:"bytes,2,opt,name=quest_condition,json=questCondition,proto3" json:"quest_condition,omitempty"` // 解锁条件
}

func (x *QuestCompleteConditionInfo) Reset() {
	*x = QuestCompleteConditionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestCompleteConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteConditionInfo) ProtoMessage() {}

func (x *QuestCompleteConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{7}
}

func (x *QuestCompleteConditionInfo) GetQuestCompleteType() QuestCompleteType {
	if x != nil {
		return x.QuestCompleteType
	}
	return QuestCompleteType_QuestCompleteType_none
}

func (x *QuestCompleteConditionInfo) GetQuestCondition() *QuestConditionInfo {
	if x != nil {
		return x.QuestCondition
	}
	return nil
}

type QuestCompleteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestCompleteId         int32                         `protobuf:"varint,1,opt,name=quest_complete_id,json=questCompleteId,proto3" json:"quest_complete_id,omitempty"`                        // 完成id 配置表中配置
	QuestCompleteConditions []*QuestCompleteConditionInfo `protobuf:"bytes,2,rep,name=quest_complete_conditions,json=questCompleteConditions,proto3" json:"quest_complete_conditions,omitempty"` // 完成条件
}

func (x *QuestCompleteInfo) Reset() {
	*x = QuestCompleteInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestCompleteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteInfo) ProtoMessage() {}

func (x *QuestCompleteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{8}
}

func (x *QuestCompleteInfo) GetQuestCompleteId() int32 {
	if x != nil {
		return x.QuestCompleteId
	}
	return 0
}

func (x *QuestCompleteInfo) GetQuestCompleteConditions() []*QuestCompleteConditionInfo {
	if x != nil {
		return x.QuestCompleteConditions
	}
	return nil
}

type QuestRewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestRewardId int32               `protobuf:"varint,1,opt,name=quest_reward_id,json=questRewardId,proto3" json:"quest_reward_id,omitempty"` // 奖励id 可以去配置表中读取
	QuestRewards  []*QuestRewardValue `protobuf:"bytes,2,rep,name=quest_rewards,json=questRewards,proto3" json:"quest_rewards,omitempty"`       // 奖励
	RandomCount   int32               `protobuf:"varint,3,opt,name=random_count,json=randomCount,proto3" json:"random_count,omitempty"`         // 随机数量 (0或者1)都是数量1
}

func (x *QuestRewardInfo) Reset() {
	*x = QuestRewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestRewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestRewardInfo) ProtoMessage() {}

func (x *QuestRewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestRewardInfo.ProtoReflect.Descriptor instead.
func (*QuestRewardInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{9}
}

func (x *QuestRewardInfo) GetQuestRewardId() int32 {
	if x != nil {
		return x.QuestRewardId
	}
	return 0
}

func (x *QuestRewardInfo) GetQuestRewards() []*QuestRewardValue {
	if x != nil {
		return x.QuestRewards
	}
	return nil
}

func (x *QuestRewardInfo) GetRandomCount() int32 {
	if x != nil {
		return x.RandomCount
	}
	return 0
}

type QuestRewardValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestRewardType      QuestRewardType               `protobuf:"varint,1,opt,name=quest_reward_type,json=questRewardType,proto3,enum=MainServer.QuestRewardType" json:"quest_reward_type,omitempty"`                                // 奖励类型
	QuestRewardRate      float32                       `protobuf:"fixed32,2,opt,name=quest_reward_rate,json=questRewardRate,proto3" json:"quest_reward_rate,omitempty"`                                                               // 奖励概率
	QuestRewardCount     int32                         `protobuf:"varint,3,opt,name=quest_reward_count,json=questRewardCount,proto3" json:"quest_reward_count,omitempty"`                                                             // 奖励数量
	QuestRewardCountRate QuestRewardValueCountRateType `protobuf:"varint,4,opt,name=quest_reward_count_rate,json=questRewardCountRate,proto3,enum=MainServer.QuestRewardValueCountRateType" json:"quest_reward_count_rate,omitempty"` // 奖励数量概率
	DayWholeNetlocked    int32                         `protobuf:"varint,5,opt,name=day_whole_netlocked,json=dayWholeNetlocked,proto3" json:"day_whole_netlocked,omitempty"`                                                          // 一天全网锁定数量  //todo
	QuestRewardValue     string                        `protobuf:"bytes,6,opt,name=quest_reward_value,json=questRewardValue,proto3" json:"quest_reward_value,omitempty"`                                                              // 具体奖励
}

func (x *QuestRewardValue) Reset() {
	*x = QuestRewardValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestRewardValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestRewardValue) ProtoMessage() {}

func (x *QuestRewardValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestRewardValue.ProtoReflect.Descriptor instead.
func (*QuestRewardValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{10}
}

func (x *QuestRewardValue) GetQuestRewardType() QuestRewardType {
	if x != nil {
		return x.QuestRewardType
	}
	return QuestRewardType_QuestRewardType_None
}

func (x *QuestRewardValue) GetQuestRewardRate() float32 {
	if x != nil {
		return x.QuestRewardRate
	}
	return 0
}

func (x *QuestRewardValue) GetQuestRewardCount() int32 {
	if x != nil {
		return x.QuestRewardCount
	}
	return 0
}

func (x *QuestRewardValue) GetQuestRewardCountRate() QuestRewardValueCountRateType {
	if x != nil {
		return x.QuestRewardCountRate
	}
	return QuestRewardValueCountRateType_QuestRewardValueCountRateType_None
}

func (x *QuestRewardValue) GetDayWholeNetlocked() int32 {
	if x != nil {
		return x.DayWholeNetlocked
	}
	return 0
}

func (x *QuestRewardValue) GetQuestRewardValue() string {
	if x != nil {
		return x.QuestRewardValue
	}
	return ""
}

type QuestBroadcastInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestBroadcastType  QuestBroadcastType `protobuf:"varint,1,opt,name=quest_broadcast_type,json=questBroadcastType,proto3,enum=MainServer.QuestBroadcastType" json:"quest_broadcast_type,omitempty"` // 广播类型
	QuestBroadcastValue string             `protobuf:"bytes,2,opt,name=quest_broadcast_value,json=questBroadcastValue,proto3" json:"quest_broadcast_value,omitempty"`                                  // 广播type对应的value (不是广播内容) 比如说什么地区
}

func (x *QuestBroadcastInfo) Reset() {
	*x = QuestBroadcastInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestBroadcastInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestBroadcastInfo) ProtoMessage() {}

func (x *QuestBroadcastInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestBroadcastInfo.ProtoReflect.Descriptor instead.
func (*QuestBroadcastInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{11}
}

func (x *QuestBroadcastInfo) GetQuestBroadcastType() QuestBroadcastType {
	if x != nil {
		return x.QuestBroadcastType
	}
	return QuestBroadcastType_QuestBroadcast_none
}

func (x *QuestBroadcastInfo) GetQuestBroadcastValue() string {
	if x != nil {
		return x.QuestBroadcastValue
	}
	return ""
}

type QuestTypeValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestTypeValueId       int32  `protobuf:"varint,1,opt,name=quest_type_value_id,json=questTypeValueId,proto3" json:"quest_type_value_id,omitempty"`                    // quest值id
	QuestTypeValue         int64  `protobuf:"varint,2,opt,name=quest_type_value,json=questTypeValue,proto3" json:"quest_type_value,omitempty"`                            // quest值
	IsStringValueId        bool   `protobuf:"varint,3,opt,name=is_string_value_id,json=isStringValueId,proto3" json:"is_string_value_id,omitempty"`                       // 是否为字符串id
	IsStringValue          bool   `protobuf:"varint,4,opt,name=is_string_value,json=isStringValue,proto3" json:"is_string_value,omitempty"`                               // 是否为字符串value
	QuestTypeValueString   string `protobuf:"bytes,5,opt,name=quest_type_value_string,json=questTypeValueString,proto3" json:"quest_type_value_string,omitempty"`         // quest值字符串
	QuestTypeValueStringId string `protobuf:"bytes,6,opt,name=quest_type_value_string_id,json=questTypeValueStringId,proto3" json:"quest_type_value_string_id,omitempty"` // quest值字符串id
}

func (x *QuestTypeValue) Reset() {
	*x = QuestTypeValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_QuestInfo_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestTypeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestTypeValue) ProtoMessage() {}

func (x *QuestTypeValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestTypeValue.ProtoReflect.Descriptor instead.
func (*QuestTypeValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{12}
}

func (x *QuestTypeValue) GetQuestTypeValueId() int32 {
	if x != nil {
		return x.QuestTypeValueId
	}
	return 0
}

func (x *QuestTypeValue) GetQuestTypeValue() int64 {
	if x != nil {
		return x.QuestTypeValue
	}
	return 0
}

func (x *QuestTypeValue) GetIsStringValueId() bool {
	if x != nil {
		return x.IsStringValueId
	}
	return false
}

func (x *QuestTypeValue) GetIsStringValue() bool {
	if x != nil {
		return x.IsStringValue
	}
	return false
}

func (x *QuestTypeValue) GetQuestTypeValueString() string {
	if x != nil {
		return x.QuestTypeValueString
	}
	return ""
}

func (x *QuestTypeValue) GetQuestTypeValueStringId() string {
	if x != nil {
		return x.QuestTypeValueStringId
	}
	return ""
}

var File_MainServer_QuestInfo_proto protoreflect.FileDescriptor

var file_MainServer_QuestInfo_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x06, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x40,
	0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x73,
	0x12, 0x57, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x61, 0x70, 0x12, 0x5d, 0x0a, 0x12, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x5a, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x4d, 0x61, 0x70, 0x12, 0x46, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x5e, 0x0a, 0x13,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x62, 0x0a, 0x15,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x5f, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xcb, 0x07, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0a, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x3a, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a,
	0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72,
	0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x12,
	0x4d, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35,
	0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x47, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x73, 0x22, 0x79, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12,
	0x5c, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xac, 0x01,
	0x0a, 0x18, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x11, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a,
	0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x73, 0x6f, 0x6e,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x73,
	0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x69, 0x73, 0x41, 0x64, 0x64, 0x22, 0xb4, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x62, 0x0a, 0x19, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x17, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xf5, 0x02, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x47, 0x0a, 0x11, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x60, 0x0a,
	0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x64, 0x61, 0x79, 0x5f, 0x77, 0x68, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x65, 0x74,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x64, 0x61,
	0x79, 0x57, 0x68, 0x6f, 0x6c, 0x65, 0x4e, 0x65, 0x74, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x9a, 0x01,
	0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb1, 0x02, 0x0a, 0x0e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2d, 0x0a,
	0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x3a, 0x0a, 0x1a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_QuestInfo_proto_rawDescOnce sync.Once
	file_MainServer_QuestInfo_proto_rawDescData = file_MainServer_QuestInfo_proto_rawDesc
)

func file_MainServer_QuestInfo_proto_rawDescGZIP() []byte {
	file_MainServer_QuestInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_QuestInfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_QuestInfo_proto_rawDescData)
	})
	return file_MainServer_QuestInfo_proto_rawDescData
}

var file_MainServer_QuestInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_MainServer_QuestInfo_proto_goTypes = []any{
	(*QuestInfoList)(nil),              // 0: MainServer.QuestInfoList
	(*QuestInfo)(nil),                  // 1: MainServer.QuestInfo
	(*QuestListInfo)(nil),              // 2: MainServer.QuestListInfo
	(*QuestStrictInfo)(nil),            // 3: MainServer.QuestStrictInfo
	(*QuestUnlockInfo)(nil),            // 4: MainServer.QuestUnlockInfo
	(*QuestUnlockConditionInfo)(nil),   // 5: MainServer.QuestUnlockConditionInfo
	(*QuestConditionInfo)(nil),         // 6: MainServer.QuestConditionInfo
	(*QuestCompleteConditionInfo)(nil), // 7: MainServer.QuestCompleteConditionInfo
	(*QuestCompleteInfo)(nil),          // 8: MainServer.QuestCompleteInfo
	(*QuestRewardInfo)(nil),            // 9: MainServer.QuestRewardInfo
	(*QuestRewardValue)(nil),           // 10: MainServer.QuestRewardValue
	(*QuestBroadcastInfo)(nil),         // 11: MainServer.QuestBroadcastInfo
	(*QuestTypeValue)(nil),             // 12: MainServer.QuestTypeValue
	nil,                                // 13: MainServer.QuestInfoList.QuestUnlockMapEntry
	nil,                                // 14: MainServer.QuestInfoList.QuestCompleteMapEntry
	nil,                                // 15: MainServer.QuestInfoList.QuestRewardsMapEntry
	(QuestType)(0),                     // 16: MainServer.QuestType
	(QuestStatus)(0),                   // 17: MainServer.QuestStatus
	(*TrainerStrict)(nil),              // 18: MainServer.TrainerStrict
	(QuestUnlockType)(0),               // 19: MainServer.QuestUnlockType
	(QuestCompleteType)(0),             // 20: MainServer.QuestCompleteType
	(QuestRewardType)(0),               // 21: MainServer.QuestRewardType
	(QuestRewardValueCountRateType)(0), // 22: MainServer.QuestRewardValueCountRateType
	(QuestBroadcastType)(0),            // 23: MainServer.QuestBroadcastType
}
var file_MainServer_QuestInfo_proto_depIdxs = []int32{
	1,  // 0: MainServer.QuestInfoList.quest_infos:type_name -> MainServer.QuestInfo
	3,  // 1: MainServer.QuestInfoList.quest_stricts:type_name -> MainServer.QuestStrictInfo
	13, // 2: MainServer.QuestInfoList.quest_unlock_map:type_name -> MainServer.QuestInfoList.QuestUnlockMapEntry
	14, // 3: MainServer.QuestInfoList.quest_complete_map:type_name -> MainServer.QuestInfoList.QuestCompleteMapEntry
	15, // 4: MainServer.QuestInfoList.quest_rewards_map:type_name -> MainServer.QuestInfoList.QuestRewardsMapEntry
	12, // 5: MainServer.QuestInfoList.quest_type_values:type_name -> MainServer.QuestTypeValue
	16, // 6: MainServer.QuestInfo.quest_type:type_name -> MainServer.QuestType
	17, // 7: MainServer.QuestInfo.quest_status:type_name -> MainServer.QuestStatus
	4,  // 8: MainServer.QuestInfo.quest_unlock_info:type_name -> MainServer.QuestUnlockInfo
	2,  // 9: MainServer.QuestInfo.linear_quests:type_name -> MainServer.QuestListInfo
	2,  // 10: MainServer.QuestInfo.current_quests:type_name -> MainServer.QuestListInfo
	3,  // 11: MainServer.QuestInfo.quest_strict:type_name -> MainServer.QuestStrictInfo
	9,  // 12: MainServer.QuestInfo.quest_reward_info:type_name -> MainServer.QuestRewardInfo
	11, // 13: MainServer.QuestInfo.quest_broadcast:type_name -> MainServer.QuestBroadcastInfo
	8,  // 14: MainServer.QuestInfo.quest_complete_info:type_name -> MainServer.QuestCompleteInfo
	18, // 15: MainServer.QuestStrictInfo.quest_stricts:type_name -> MainServer.TrainerStrict
	5,  // 16: MainServer.QuestUnlockInfo.quest_unlock_conditions:type_name -> MainServer.QuestUnlockConditionInfo
	19, // 17: MainServer.QuestUnlockConditionInfo.quest_unlock_type:type_name -> MainServer.QuestUnlockType
	6,  // 18: MainServer.QuestUnlockConditionInfo.quest_condition:type_name -> MainServer.QuestConditionInfo
	20, // 19: MainServer.QuestCompleteConditionInfo.quest_complete_type:type_name -> MainServer.QuestCompleteType
	6,  // 20: MainServer.QuestCompleteConditionInfo.quest_condition:type_name -> MainServer.QuestConditionInfo
	7,  // 21: MainServer.QuestCompleteInfo.quest_complete_conditions:type_name -> MainServer.QuestCompleteConditionInfo
	10, // 22: MainServer.QuestRewardInfo.quest_rewards:type_name -> MainServer.QuestRewardValue
	21, // 23: MainServer.QuestRewardValue.quest_reward_type:type_name -> MainServer.QuestRewardType
	22, // 24: MainServer.QuestRewardValue.quest_reward_count_rate:type_name -> MainServer.QuestRewardValueCountRateType
	23, // 25: MainServer.QuestBroadcastInfo.quest_broadcast_type:type_name -> MainServer.QuestBroadcastType
	4,  // 26: MainServer.QuestInfoList.QuestUnlockMapEntry.value:type_name -> MainServer.QuestUnlockInfo
	8,  // 27: MainServer.QuestInfoList.QuestCompleteMapEntry.value:type_name -> MainServer.QuestCompleteInfo
	9,  // 28: MainServer.QuestInfoList.QuestRewardsMapEntry.value:type_name -> MainServer.QuestRewardInfo
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_MainServer_QuestInfo_proto_init() }
func file_MainServer_QuestInfo_proto_init() {
	if File_MainServer_QuestInfo_proto != nil {
		return
	}
	file_MainServer_QuestType_proto_init()
	file_MainServer_TrainerStrict_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_QuestInfo_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*QuestInfoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*QuestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*QuestListInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*QuestStrictInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*QuestUnlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*QuestUnlockConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*QuestConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*QuestCompleteConditionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*QuestCompleteInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*QuestRewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*QuestRewardValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*QuestBroadcastInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_QuestInfo_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*QuestTypeValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_QuestInfo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_QuestInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_QuestInfo_proto_depIdxs,
		MessageInfos:      file_MainServer_QuestInfo_proto_msgTypes,
	}.Build()
	File_MainServer_QuestInfo_proto = out.File
	file_MainServer_QuestInfo_proto_rawDesc = nil
	file_MainServer_QuestInfo_proto_goTypes = nil
	file_MainServer_QuestInfo_proto_depIdxs = nil
}
