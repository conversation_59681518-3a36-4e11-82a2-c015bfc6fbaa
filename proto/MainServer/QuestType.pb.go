// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/QuestType.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// import "MainServer/Poke.proto";
//
//	message QuestTypeList {
//	    repeated QuestType quest_type_list = 1;
//	}
type QuestType int32

const (
	QuestType_QuestType_once                    QuestType = 0 // 一次性任务
	QuestType_QuestType_daily                   QuestType = 1 // 每日任务
	QuestType_QuestType_weekly                  QuestType = 2 // 每周任务
	QuestType_QuestType_monthly                 QuestType = 3 // 每月任务
	QuestType_QuestType_yearly                  QuestType = 4 // 每年任务
	QuestType_QuestType_repeat                  QuestType = 5 // 重复任务
	QuestType_QuestType_custom                  QuestType = 6 // 自定义任务
	QuestType_QuestType_activity                QuestType = 7 // 活动任务
	QuestType_QuestType_activity_original_first QuestType = 8 // 活动任务-原初任务
	QuestType_QuestType_team                    QuestType = 9 // 团队任务
)

// Enum value maps for QuestType.
var (
	QuestType_name = map[int32]string{
		0: "QuestType_once",
		1: "QuestType_daily",
		2: "QuestType_weekly",
		3: "QuestType_monthly",
		4: "QuestType_yearly",
		5: "QuestType_repeat",
		6: "QuestType_custom",
		7: "QuestType_activity",
		8: "QuestType_activity_original_first",
		9: "QuestType_team",
	}
	QuestType_value = map[string]int32{
		"QuestType_once":                    0,
		"QuestType_daily":                   1,
		"QuestType_weekly":                  2,
		"QuestType_monthly":                 3,
		"QuestType_yearly":                  4,
		"QuestType_repeat":                  5,
		"QuestType_custom":                  6,
		"QuestType_activity":                7,
		"QuestType_activity_original_first": 8,
		"QuestType_team":                    9,
	}
)

func (x QuestType) Enum() *QuestType {
	p := new(QuestType)
	*p = x
	return p
}

func (x QuestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[0].Descriptor()
}

func (QuestType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[0]
}

func (x QuestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestType.Descriptor instead.
func (QuestType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{0}
}

type QuestStatus int32

const (
	QuestStatus_QuestStatus_close QuestStatus = 0 // 关闭 关闭状态不可见
	QuestStatus_QuestStatus_wait  QuestStatus = 1 // 等待时间到开启
	QuestStatus_QuestStatus_open  QuestStatus = 2 // 开启 开启状态可见
)

// Enum value maps for QuestStatus.
var (
	QuestStatus_name = map[int32]string{
		0: "QuestStatus_close",
		1: "QuestStatus_wait",
		2: "QuestStatus_open",
	}
	QuestStatus_value = map[string]int32{
		"QuestStatus_close": 0,
		"QuestStatus_wait":  1,
		"QuestStatus_open":  2,
	}
)

func (x QuestStatus) Enum() *QuestStatus {
	p := new(QuestStatus)
	*p = x
	return p
}

func (x QuestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[1].Descriptor()
}

func (QuestStatus) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[1]
}

func (x QuestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestStatus.Descriptor instead.
func (QuestStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{1}
}

type QuestUnlockType int32

const (
	QuestUnlockType_QuestUnlockType_level        QuestUnlockType = 0 // 等级解锁
	QuestUnlockType_QuestUnlockType_quest        QuestUnlockType = 1 // 任务解锁
	QuestUnlockType_QuestUnlockType_item         QuestUnlockType = 2 // 道具解锁
	QuestUnlockType_QuestUnlockType_money        QuestUnlockType = 3 // 金钱解锁
	QuestUnlockType_QuestUnlockType_time         QuestUnlockType = 5 // 时间解锁
	QuestUnlockType_QuestUnlockType_poke         QuestUnlockType = 6 // poke解锁
	QuestUnlockType_QuestUnlockType_achievement  QuestUnlockType = 7 // 成就解锁
	QuestUnlockType_QuestUnlockType_title        QuestUnlockType = 8 // 称号解锁
	QuestUnlockType_QuestUnlockType_battle_level QuestUnlockType = 9 // 战斗等级解锁
)

// Enum value maps for QuestUnlockType.
var (
	QuestUnlockType_name = map[int32]string{
		0: "QuestUnlockType_level",
		1: "QuestUnlockType_quest",
		2: "QuestUnlockType_item",
		3: "QuestUnlockType_money",
		5: "QuestUnlockType_time",
		6: "QuestUnlockType_poke",
		7: "QuestUnlockType_achievement",
		8: "QuestUnlockType_title",
		9: "QuestUnlockType_battle_level",
	}
	QuestUnlockType_value = map[string]int32{
		"QuestUnlockType_level":        0,
		"QuestUnlockType_quest":        1,
		"QuestUnlockType_item":         2,
		"QuestUnlockType_money":        3,
		"QuestUnlockType_time":         5,
		"QuestUnlockType_poke":         6,
		"QuestUnlockType_achievement":  7,
		"QuestUnlockType_title":        8,
		"QuestUnlockType_battle_level": 9,
	}
)

func (x QuestUnlockType) Enum() *QuestUnlockType {
	p := new(QuestUnlockType)
	*p = x
	return p
}

func (x QuestUnlockType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestUnlockType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[2].Descriptor()
}

func (QuestUnlockType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[2]
}

func (x QuestUnlockType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestUnlockType.Descriptor instead.
func (QuestUnlockType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{2}
}

type QuestCompleteType int32

const (
	QuestCompleteType_QuestCompleteType_none         QuestCompleteType = 0 // 无
	QuestCompleteType_QuestCompleteType_battle_poke  QuestCompleteType = 1 // 击败poke
	QuestCompleteType_QuestCompleteType_battle_npc   QuestCompleteType = 2 // 击败某个npc
	QuestCompleteType_QuestCompleteType_collect_item QuestCompleteType = 3 // 收集道具
	QuestCompleteType_QuestCompleteType_collect_poke QuestCompleteType = 4 // 收集poke
	QuestCompleteType_QuestCompleteType_talk         QuestCompleteType = 5 // 和某个npc交谈
	QuestCompleteType_QuestCompleteType_deliver_item QuestCompleteType = 6 // 交付道具
	QuestCompleteType_QuestCompleteType_deliver_poke QuestCompleteType = 7 // 交付poke
	QuestCompleteType_QuestCompleteType_arrive_area  QuestCompleteType = 8 // 到达某个地点 （可以用name，比如添加一个碰撞体，到达那之后，就传送这个碰撞体的名称）
	QuestCompleteType_QuestCompleteType_custom       QuestCompleteType = 9 // 自定义
)

// Enum value maps for QuestCompleteType.
var (
	QuestCompleteType_name = map[int32]string{
		0: "QuestCompleteType_none",
		1: "QuestCompleteType_battle_poke",
		2: "QuestCompleteType_battle_npc",
		3: "QuestCompleteType_collect_item",
		4: "QuestCompleteType_collect_poke",
		5: "QuestCompleteType_talk",
		6: "QuestCompleteType_deliver_item",
		7: "QuestCompleteType_deliver_poke",
		8: "QuestCompleteType_arrive_area",
		9: "QuestCompleteType_custom",
	}
	QuestCompleteType_value = map[string]int32{
		"QuestCompleteType_none":         0,
		"QuestCompleteType_battle_poke":  1,
		"QuestCompleteType_battle_npc":   2,
		"QuestCompleteType_collect_item": 3,
		"QuestCompleteType_collect_poke": 4,
		"QuestCompleteType_talk":         5,
		"QuestCompleteType_deliver_item": 6,
		"QuestCompleteType_deliver_poke": 7,
		"QuestCompleteType_arrive_area":  8,
		"QuestCompleteType_custom":       9,
	}
)

func (x QuestCompleteType) Enum() *QuestCompleteType {
	p := new(QuestCompleteType)
	*p = x
	return p
}

func (x QuestCompleteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestCompleteType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[3].Descriptor()
}

func (QuestCompleteType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[3]
}

func (x QuestCompleteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestCompleteType.Descriptor instead.
func (QuestCompleteType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{3}
}

type QuestBroadcastType int32

const (
	QuestBroadcastType_QuestBroadcast_none   QuestBroadcastType = 0 // 无
	QuestBroadcastType_QuestBroadcast_system QuestBroadcastType = 1 // 系统广播
	QuestBroadcastType_QuestBroadcast_world  QuestBroadcastType = 2 // 世界广播
	QuestBroadcastType_QuestBroadcast_area   QuestBroadcastType = 3 // 地区广播
)

// Enum value maps for QuestBroadcastType.
var (
	QuestBroadcastType_name = map[int32]string{
		0: "QuestBroadcast_none",
		1: "QuestBroadcast_system",
		2: "QuestBroadcast_world",
		3: "QuestBroadcast_area",
	}
	QuestBroadcastType_value = map[string]int32{
		"QuestBroadcast_none":   0,
		"QuestBroadcast_system": 1,
		"QuestBroadcast_world":  2,
		"QuestBroadcast_area":   3,
	}
)

func (x QuestBroadcastType) Enum() *QuestBroadcastType {
	p := new(QuestBroadcastType)
	*p = x
	return p
}

func (x QuestBroadcastType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestBroadcastType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[4].Descriptor()
}

func (QuestBroadcastType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[4]
}

func (x QuestBroadcastType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestBroadcastType.Descriptor instead.
func (QuestBroadcastType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{4}
}

type QuestRewardType int32

const (
	QuestRewardType_QuestRewardType_None        QuestRewardType = 0
	QuestRewardType_QuestRewardType_Pokemon     QuestRewardType = 1 //宝可梦
	QuestRewardType_QuestRewardType_Item        QuestRewardType = 2 //道具
	QuestRewardType_QuestRewardType_Cloth       QuestRewardType = 3 //服装
	QuestRewardType_QuestRewardType_Money       QuestRewardType = 4 //金钱
	QuestRewardType_QuestRewardType_Exp         QuestRewardType = 5 //经验值
	QuestRewardType_QuestRewardType_Star        QuestRewardType = 6 //星星
	QuestRewardType_QuestRewardType_Title       QuestRewardType = 7 //称号
	QuestRewardType_QuestRewardType_Achievement QuestRewardType = 8 //成就
	QuestRewardType_QuestRewardType_BattleLevel QuestRewardType = 9 //战斗等级
)

// Enum value maps for QuestRewardType.
var (
	QuestRewardType_name = map[int32]string{
		0: "QuestRewardType_None",
		1: "QuestRewardType_Pokemon",
		2: "QuestRewardType_Item",
		3: "QuestRewardType_Cloth",
		4: "QuestRewardType_Money",
		5: "QuestRewardType_Exp",
		6: "QuestRewardType_Star",
		7: "QuestRewardType_Title",
		8: "QuestRewardType_Achievement",
		9: "QuestRewardType_BattleLevel",
	}
	QuestRewardType_value = map[string]int32{
		"QuestRewardType_None":        0,
		"QuestRewardType_Pokemon":     1,
		"QuestRewardType_Item":        2,
		"QuestRewardType_Cloth":       3,
		"QuestRewardType_Money":       4,
		"QuestRewardType_Exp":         5,
		"QuestRewardType_Star":        6,
		"QuestRewardType_Title":       7,
		"QuestRewardType_Achievement": 8,
		"QuestRewardType_BattleLevel": 9,
	}
)

func (x QuestRewardType) Enum() *QuestRewardType {
	p := new(QuestRewardType)
	*p = x
	return p
}

func (x QuestRewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestRewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[5].Descriptor()
}

func (QuestRewardType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[5]
}

func (x QuestRewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestRewardType.Descriptor instead.
func (QuestRewardType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{5}
}

type QuestRewardValueCountRateType int32

const (
	QuestRewardValueCountRateType_QuestRewardValueCountRateType_None          QuestRewardValueCountRateType = 0 //获得固定的数量
	QuestRewardValueCountRateType_QuestRewardValueCountRateType_Average       QuestRewardValueCountRateType = 1 //从1开始到最大值每个数量的概率都相同
	QuestRewardValueCountRateType_QuestRewardValueCountRateType_Stairs        QuestRewardValueCountRateType = 2 //阶梯 数值越小概率越大
	QuestRewardValueCountRateType_QuestRewardValueCountRateType_Mid_Max_Lower QuestRewardValueCountRateType = 3 //最大值概率最低 (从中间之前的概率占70%，并且平均， 后续的概率阶梯）
)

// Enum value maps for QuestRewardValueCountRateType.
var (
	QuestRewardValueCountRateType_name = map[int32]string{
		0: "QuestRewardValueCountRateType_None",
		1: "QuestRewardValueCountRateType_Average",
		2: "QuestRewardValueCountRateType_Stairs",
		3: "QuestRewardValueCountRateType_Mid_Max_Lower",
	}
	QuestRewardValueCountRateType_value = map[string]int32{
		"QuestRewardValueCountRateType_None":          0,
		"QuestRewardValueCountRateType_Average":       1,
		"QuestRewardValueCountRateType_Stairs":        2,
		"QuestRewardValueCountRateType_Mid_Max_Lower": 3,
	}
)

func (x QuestRewardValueCountRateType) Enum() *QuestRewardValueCountRateType {
	p := new(QuestRewardValueCountRateType)
	*p = x
	return p
}

func (x QuestRewardValueCountRateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestRewardValueCountRateType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[6].Descriptor()
}

func (QuestRewardValueCountRateType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[6]
}

func (x QuestRewardValueCountRateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestRewardValueCountRateType.Descriptor instead.
func (QuestRewardValueCountRateType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{6}
}

var File_MainServer_QuestType_proto protoreflect.FileDescriptor

var file_MainServer_QuestType_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2a, 0xf6, 0x01, 0x0a, 0x09, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x6f, 0x6e, 0x63, 0x65, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x10, 0x01, 0x12,
	0x14, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x77, 0x65, 0x65,
	0x6b, 0x6c, 0x79, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79,
	0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x10, 0x06, 0x12, 0x16,
	0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x10, 0x08, 0x12, 0x12, 0x0a,
	0x0e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x10,
	0x09, 0x2a, 0x50, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x15, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6f, 0x70, 0x65,
	0x6e, 0x10, 0x02, 0x2a, 0x8e, 0x02, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x01, 0x12, 0x18, 0x0a,
	0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x70, 0x6f, 0x6b, 0x65, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55,
	0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x55, 0x6e, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x10, 0x09, 0x2a, 0xe1, 0x02, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x6e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x62, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x5f, 0x70, 0x6f, 0x6b, 0x65, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x6e, 0x70, 0x63, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x10, 0x03, 0x12,
	0x22, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x70, 0x6f, 0x6b,
	0x65, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x61, 0x6c, 0x6b, 0x10, 0x05, 0x12,
	0x22, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x70, 0x6f, 0x6b, 0x65, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x10, 0x09, 0x2a, 0x7b, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x13, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64,
	0x63, 0x61, 0x73, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x61,
	0x72, 0x65, 0x61, 0x10, 0x03, 0x2a, 0xa8, 0x02, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e,
	0x65, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6c,
	0x6f, 0x74, 0x68, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x10, 0x04,
	0x12, 0x17, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x45, 0x78, 0x70, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x74, 0x61,
	0x72, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x10, 0x07, 0x12, 0x1f,
	0x0a, 0x1b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x08, 0x12,
	0x1f, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10, 0x09,
	0x2a, 0xcd, 0x01, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x74, 0x61, 0x69, 0x72, 0x73, 0x10, 0x02, 0x12,
	0x2f, 0x0a, 0x2b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4d, 0x69, 0x64, 0x5f, 0x4d, 0x61, 0x78, 0x5f, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x10, 0x03,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_QuestType_proto_rawDescOnce sync.Once
	file_MainServer_QuestType_proto_rawDescData = file_MainServer_QuestType_proto_rawDesc
)

func file_MainServer_QuestType_proto_rawDescGZIP() []byte {
	file_MainServer_QuestType_proto_rawDescOnce.Do(func() {
		file_MainServer_QuestType_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_QuestType_proto_rawDescData)
	})
	return file_MainServer_QuestType_proto_rawDescData
}

var file_MainServer_QuestType_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_MainServer_QuestType_proto_goTypes = []any{
	(QuestType)(0),                     // 0: MainServer.QuestType
	(QuestStatus)(0),                   // 1: MainServer.QuestStatus
	(QuestUnlockType)(0),               // 2: MainServer.QuestUnlockType
	(QuestCompleteType)(0),             // 3: MainServer.QuestCompleteType
	(QuestBroadcastType)(0),            // 4: MainServer.QuestBroadcastType
	(QuestRewardType)(0),               // 5: MainServer.QuestRewardType
	(QuestRewardValueCountRateType)(0), // 6: MainServer.QuestRewardValueCountRateType
}
var file_MainServer_QuestType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_QuestType_proto_init() }
func file_MainServer_QuestType_proto_init() {
	if File_MainServer_QuestType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_QuestType_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_QuestType_proto_goTypes,
		DependencyIndexes: file_MainServer_QuestType_proto_depIdxs,
		EnumInfos:         file_MainServer_QuestType_proto_enumTypes,
	}.Build()
	File_MainServer_QuestType_proto = out.File
	file_MainServer_QuestType_proto_rawDesc = nil
	file_MainServer_QuestType_proto_goTypes = nil
	file_MainServer_QuestType_proto_depIdxs = nil
}
