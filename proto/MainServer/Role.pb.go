// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Role.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoleDutyType int32

const (
	RoleDutyType_role_normal  RoleDutyType = 0
	RoleDutyType_role_battle  RoleDutyType = 1
	RoleDutyType_role_healing RoleDutyType = 2
	RoleDutyType_role_store   RoleDutyType = 3
)

// Enum value maps for RoleDutyType.
var (
	RoleDutyType_name = map[int32]string{
		0: "role_normal",
		1: "role_battle",
		2: "role_healing",
		3: "role_store",
	}
	RoleDutyType_value = map[string]int32{
		"role_normal":  0,
		"role_battle":  1,
		"role_healing": 2,
		"role_store":   3,
	}
)

func (x RoleDutyType) Enum() *RoleDutyType {
	p := new(RoleDutyType)
	*p = x
	return p
}

func (x RoleDutyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleDutyType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Role_proto_enumTypes[0].Descriptor()
}

func (RoleDutyType) Type() protoreflect.EnumType {
	return &file_MainServer_Role_proto_enumTypes[0]
}

func (x RoleDutyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleDutyType.Descriptor instead.
func (RoleDutyType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

type NpcRoleConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Duty           RoleDutyType       `protobuf:"varint,2,opt,name=duty,proto3,enum=MainServer.RoleDutyType" json:"duty,omitempty"`
	Cloth          *TrainerCloth      `protobuf:"bytes,3,opt,name=cloth,proto3" json:"cloth,omitempty"`
	FollowPoke     *TrainerFollowPoke `protobuf:"bytes,4,opt,name=follow_poke,json=followPoke,proto3" json:"follow_poke,omitempty"` //跟随的宝可梦
	Gender         string             `protobuf:"bytes,5,opt,name=gender,proto3" json:"gender,omitempty"`
	DialogElements []string           `protobuf:"bytes,6,rep,name=dialog_elements,json=dialogElements,proto3" json:"dialog_elements,omitempty"`
}

func (x *NpcRoleConfig) Reset() {
	*x = NpcRoleConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Role_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NpcRoleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpcRoleConfig) ProtoMessage() {}

func (x *NpcRoleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Role_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpcRoleConfig.ProtoReflect.Descriptor instead.
func (*NpcRoleConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_Role_proto_rawDescGZIP(), []int{0}
}

func (x *NpcRoleConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NpcRoleConfig) GetDuty() RoleDutyType {
	if x != nil {
		return x.Duty
	}
	return RoleDutyType_role_normal
}

func (x *NpcRoleConfig) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

func (x *NpcRoleConfig) GetFollowPoke() *TrainerFollowPoke {
	if x != nil {
		return x.FollowPoke
	}
	return nil
}

func (x *NpcRoleConfig) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *NpcRoleConfig) GetDialogElements() []string {
	if x != nil {
		return x.DialogElements
	}
	return nil
}

var File_MainServer_Role_proto protoreflect.FileDescriptor

var file_MainServer_Role_proto_rawDesc = []byte{
	0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x52, 0x6f, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x1a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x02, 0x0a,
	0x0d, 0x4e, 0x70, 0x63, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x75, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x44, 0x75, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x64, 0x75, 0x74, 0x79,
	0x12, 0x2e, 0x0a, 0x05, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x05, 0x63, 0x6c, 0x6f, 0x74, 0x68,
	0x12, 0x3e, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x6f, 0x6b, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x50, 0x6f, 0x6b, 0x65, 0x52, 0x0a, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x6f, 0x6b, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x61, 0x6c,
	0x6f, 0x67, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0e, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2a, 0x52, 0x0a, 0x0c, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x75, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x6c,
	0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x10, 0x03, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Role_proto_rawDescOnce sync.Once
	file_MainServer_Role_proto_rawDescData = file_MainServer_Role_proto_rawDesc
)

func file_MainServer_Role_proto_rawDescGZIP() []byte {
	file_MainServer_Role_proto_rawDescOnce.Do(func() {
		file_MainServer_Role_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Role_proto_rawDescData)
	})
	return file_MainServer_Role_proto_rawDescData
}

var file_MainServer_Role_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Role_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_Role_proto_goTypes = []any{
	(RoleDutyType)(0),         // 0: MainServer.RoleDutyType
	(*NpcRoleConfig)(nil),     // 1: MainServer.NpcRoleConfig
	(*TrainerCloth)(nil),      // 2: MainServer.TrainerCloth
	(*TrainerFollowPoke)(nil), // 3: MainServer.TrainerFollowPoke
}
var file_MainServer_Role_proto_depIdxs = []int32{
	0, // 0: MainServer.NpcRoleConfig.duty:type_name -> MainServer.RoleDutyType
	2, // 1: MainServer.NpcRoleConfig.cloth:type_name -> MainServer.TrainerCloth
	3, // 2: MainServer.NpcRoleConfig.follow_poke:type_name -> MainServer.TrainerFollowPoke
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_Role_proto_init() }
func file_MainServer_Role_proto_init() {
	if File_MainServer_Role_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_TrainerCloth_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Role_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*NpcRoleConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Role_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Role_proto_goTypes,
		DependencyIndexes: file_MainServer_Role_proto_depIdxs,
		EnumInfos:         file_MainServer_Role_proto_enumTypes,
		MessageInfos:      file_MainServer_Role_proto_msgTypes,
	}.Build()
	File_MainServer_Role_proto = out.File
	file_MainServer_Role_proto_rawDesc = nil
	file_MainServer_Role_proto_goTypes = nil
	file_MainServer_Role_proto_depIdxs = nil
}
