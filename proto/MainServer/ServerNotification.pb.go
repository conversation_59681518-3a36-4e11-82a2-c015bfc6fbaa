// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/ServerNotification.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerNotificationType int32

const (
	ServerNotificationType_ServerNotificationType_None          ServerNotificationType = 0
	ServerNotificationType_ServerNotificationType_NewVersion    ServerNotificationType = 1
	ServerNotificationType_ServerNotificationType_InviteBattle  ServerNotificationType = 2
	ServerNotificationType_ServerNotificationType_MatchJoin     ServerNotificationType = 3
	ServerNotificationType_ServerNotificationType_SwopInfo      ServerNotificationType = 4
	ServerNotificationType_ServerNotificationType_UpdateTrainer ServerNotificationType = 5
	ServerNotificationType_ServerNotificationType_BattleMessage ServerNotificationType = 100
	ServerNotificationType_ServerNotificationType_BattlePrepare ServerNotificationType = 101
	ServerNotificationType_ServerNotificationType_BattleInit    ServerNotificationType = 102
	ServerNotificationType_ServerNotificationType_BattleChoice  ServerNotificationType = 103
	ServerNotificationType_ServerNotificationType_BattleResult  ServerNotificationType = 104
	ServerNotificationType_ServerNotificationType_BattleUrge    ServerNotificationType = 105
	ServerNotificationType_ServerNotificationType_NewEmail      ServerNotificationType = 200
)

// Enum value maps for ServerNotificationType.
var (
	ServerNotificationType_name = map[int32]string{
		0:   "ServerNotificationType_None",
		1:   "ServerNotificationType_NewVersion",
		2:   "ServerNotificationType_InviteBattle",
		3:   "ServerNotificationType_MatchJoin",
		4:   "ServerNotificationType_SwopInfo",
		5:   "ServerNotificationType_UpdateTrainer",
		100: "ServerNotificationType_BattleMessage",
		101: "ServerNotificationType_BattlePrepare",
		102: "ServerNotificationType_BattleInit",
		103: "ServerNotificationType_BattleChoice",
		104: "ServerNotificationType_BattleResult",
		105: "ServerNotificationType_BattleUrge",
		200: "ServerNotificationType_NewEmail",
	}
	ServerNotificationType_value = map[string]int32{
		"ServerNotificationType_None":          0,
		"ServerNotificationType_NewVersion":    1,
		"ServerNotificationType_InviteBattle":  2,
		"ServerNotificationType_MatchJoin":     3,
		"ServerNotificationType_SwopInfo":      4,
		"ServerNotificationType_UpdateTrainer": 5,
		"ServerNotificationType_BattleMessage": 100,
		"ServerNotificationType_BattlePrepare": 101,
		"ServerNotificationType_BattleInit":    102,
		"ServerNotificationType_BattleChoice":  103,
		"ServerNotificationType_BattleResult":  104,
		"ServerNotificationType_BattleUrge":    105,
		"ServerNotificationType_NewEmail":      200,
	}
)

func (x ServerNotificationType) Enum() *ServerNotificationType {
	p := new(ServerNotificationType)
	*p = x
	return p
}

func (x ServerNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[0].Descriptor()
}

func (ServerNotificationType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[0]
}

func (x ServerNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerNotificationType.Descriptor instead.
func (ServerNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

type InviteBattleType int32

const (
	InviteBattleType_InviteBattleType_None   InviteBattleType = 0
	InviteBattleType_InviteBattleType_Normal InviteBattleType = 1
	InviteBattleType_InviteBattleType_Force  InviteBattleType = 2
)

// Enum value maps for InviteBattleType.
var (
	InviteBattleType_name = map[int32]string{
		0: "InviteBattleType_None",
		1: "InviteBattleType_Normal",
		2: "InviteBattleType_Force",
	}
	InviteBattleType_value = map[string]int32{
		"InviteBattleType_None":   0,
		"InviteBattleType_Normal": 1,
		"InviteBattleType_Force":  2,
	}
)

func (x InviteBattleType) Enum() *InviteBattleType {
	p := new(InviteBattleType)
	*p = x
	return p
}

func (x InviteBattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InviteBattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[1].Descriptor()
}

func (InviteBattleType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[1]
}

func (x InviteBattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InviteBattleType.Descriptor instead.
func (InviteBattleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

type InviteBattleNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Proposer   *Trainer         `protobuf:"bytes,1,opt,name=proposer,proto3" json:"proposer,omitempty"`
	InviteType InviteBattleType `protobuf:"varint,2,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
}

func (x *InviteBattleNotification) Reset() {
	*x = InviteBattleNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleNotification) ProtoMessage() {}

func (x *InviteBattleNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleNotification.ProtoReflect.Descriptor instead.
func (*InviteBattleNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

func (x *InviteBattleNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *InviteBattleNotification) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

type MatchJoinNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchId  string   `protobuf:"bytes,1,opt,name=matchId,proto3" json:"matchId,omitempty"`
	Proposer *Trainer `protobuf:"bytes,2,opt,name=proposer,proto3" json:"proposer,omitempty"`
	Target   *Trainer `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
}

func (x *MatchJoinNotification) Reset() {
	*x = MatchJoinNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchJoinNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchJoinNotification) ProtoMessage() {}

func (x *MatchJoinNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchJoinNotification.ProtoReflect.Descriptor instead.
func (*MatchJoinNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

func (x *MatchJoinNotification) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchJoinNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *MatchJoinNotification) GetTarget() *Trainer {
	if x != nil {
		return x.Target
	}
	return nil
}

type SwopInfoNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Swop   *SwopInfo `protobuf:"bytes,1,opt,name=swop,proto3" json:"swop,omitempty"`
	Sender *Trainer  `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
}

func (x *SwopInfoNotification) Reset() {
	*x = SwopInfoNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwopInfoNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopInfoNotification) ProtoMessage() {}

func (x *SwopInfoNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopInfoNotification.ProtoReflect.Descriptor instead.
func (*SwopInfoNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{2}
}

func (x *SwopInfoNotification) GetSwop() *SwopInfo {
	if x != nil {
		return x.Swop
	}
	return nil
}

func (x *SwopInfoNotification) GetSender() *Trainer {
	if x != nil {
		return x.Sender
	}
	return nil
}

type UpdateTrainerNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trainer *Trainer `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
}

func (x *UpdateTrainerNotification) Reset() {
	*x = UpdateTrainerNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTrainerNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTrainerNotification) ProtoMessage() {}

func (x *UpdateTrainerNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTrainerNotification.ProtoReflect.Descriptor instead.
func (*UpdateTrainerNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTrainerNotification) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

var File_MainServer_ServerNotification_proto protoreflect.FileDescriptor

var file_MainServer_ServerNotification_proto_rawDesc = []byte{
	0x0a, 0x23, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x1a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x77, 0x6f, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x89, 0x01, 0x0a, 0x18, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2f, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72,
	0x12, 0x3c, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8f,
	0x01, 0x0a, 0x15, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x6f,
	0x73, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x22, 0x6d, 0x0a, 0x14, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x77, 0x6f, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x73, 0x77,
	0x6f, 0x70, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22,
	0x4a, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x07,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x52, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2a, 0x98, 0x04, 0x0a, 0x16,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4e, 0x65, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x27,
	0x0a, 0x23, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x03, 0x12, 0x23, 0x0a,
	0x1f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x10, 0x64, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x10, 0x65,
	0x12, 0x25, 0x0a, 0x21, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x49, 0x6e, 0x69, 0x74, 0x10, 0x66, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x10, 0x67,
	0x12, 0x27, 0x0a, 0x23, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x10, 0x68, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x55, 0x72, 0x67, 0x65, 0x10, 0x69,
	0x12, 0x24, 0x0a, 0x1f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x65, 0x77, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x10, 0xc8, 0x01, 0x2a, 0x66, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x10, 0x02, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_ServerNotification_proto_rawDescOnce sync.Once
	file_MainServer_ServerNotification_proto_rawDescData = file_MainServer_ServerNotification_proto_rawDesc
)

func file_MainServer_ServerNotification_proto_rawDescGZIP() []byte {
	file_MainServer_ServerNotification_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerNotification_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_ServerNotification_proto_rawDescData)
	})
	return file_MainServer_ServerNotification_proto_rawDescData
}

var file_MainServer_ServerNotification_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_ServerNotification_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_ServerNotification_proto_goTypes = []any{
	(ServerNotificationType)(0),       // 0: MainServer.ServerNotificationType
	(InviteBattleType)(0),             // 1: MainServer.InviteBattleType
	(*InviteBattleNotification)(nil),  // 2: MainServer.InviteBattleNotification
	(*MatchJoinNotification)(nil),     // 3: MainServer.MatchJoinNotification
	(*SwopInfoNotification)(nil),      // 4: MainServer.SwopInfoNotification
	(*UpdateTrainerNotification)(nil), // 5: MainServer.UpdateTrainerNotification
	(*Trainer)(nil),                   // 6: MainServer.Trainer
	(*SwopInfo)(nil),                  // 7: MainServer.SwopInfo
}
var file_MainServer_ServerNotification_proto_depIdxs = []int32{
	6, // 0: MainServer.InviteBattleNotification.proposer:type_name -> MainServer.Trainer
	1, // 1: MainServer.InviteBattleNotification.inviteType:type_name -> MainServer.InviteBattleType
	6, // 2: MainServer.MatchJoinNotification.proposer:type_name -> MainServer.Trainer
	6, // 3: MainServer.MatchJoinNotification.target:type_name -> MainServer.Trainer
	7, // 4: MainServer.SwopInfoNotification.swop:type_name -> MainServer.SwopInfo
	6, // 5: MainServer.SwopInfoNotification.sender:type_name -> MainServer.Trainer
	6, // 6: MainServer.UpdateTrainerNotification.trainer:type_name -> MainServer.Trainer
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_MainServer_ServerNotification_proto_init() }
func file_MainServer_ServerNotification_proto_init() {
	if File_MainServer_ServerNotification_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_Swop_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_ServerNotification_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerNotification_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*MatchJoinNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerNotification_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SwopInfoNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerNotification_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateTrainerNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_ServerNotification_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerNotification_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerNotification_proto_depIdxs,
		EnumInfos:         file_MainServer_ServerNotification_proto_enumTypes,
		MessageInfos:      file_MainServer_ServerNotification_proto_msgTypes,
	}.Build()
	File_MainServer_ServerNotification_proto = out.File
	file_MainServer_ServerNotification_proto_rawDesc = nil
	file_MainServer_ServerNotification_proto_goTypes = nil
	file_MainServer_ServerNotification_proto_depIdxs = nil
}
