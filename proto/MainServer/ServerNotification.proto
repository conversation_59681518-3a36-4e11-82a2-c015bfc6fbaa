syntax = "proto3";
package MainServer;

import "MainServer/Trainer.proto";

enum ServerNotificationType {
    ServerNotificationType_None = 0;
    ServerNotificationType_NewVersion = 1;
    ServerNotificationType_InviteBattle = 2;
    ServerNotificationType_MatchJoin = 3;
    ServerNotificationType_BattleMessage = 100;
    ServerNotificationType_BattlePrepare = 101;
    ServerNotificationType_BattleInit = 102;
    ServerNotificationType_BattleChoice = 103;
    ServerNotificationType_BattleResult = 104;
    ServerNotificationType_BattleUrge = 105;
}

enum InviteBattleType {
    InviteBattleType_None = 0;
    InviteBattleType_Normal = 1;
    InviteBattleType_Force = 2;
}

message InviteBattleNotification {
    Trainer proposer = 1;
    InviteBattleType inviteType = 2;
}

message MatchJoinNotification {
    string matchId = 1;
    Trainer proposer = 2;
    Trainer target = 3;
}
