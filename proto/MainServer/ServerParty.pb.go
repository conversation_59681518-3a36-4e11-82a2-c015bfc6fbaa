// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/ServerParty.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PartyMessageType int32

const (
	PartyMessageType_party       PartyMessageType = 0
	PartyMessageType_partyBattle PartyMessageType = 1
)

// Enum value maps for PartyMessageType.
var (
	PartyMessageType_name = map[int32]string{
		0: "party",
		1: "partyBattle",
	}
	PartyMessageType_value = map[string]int32{
		"party":       0,
		"partyBattle": 1,
	}
)

func (x PartyMessageType) Enum() *PartyMessageType {
	p := new(PartyMessageType)
	*p = x
	return p
}

func (x PartyMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PartyMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerParty_proto_enumTypes[0].Descriptor()
}

func (PartyMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerParty_proto_enumTypes[0]
}

func (x PartyMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PartyMessageType.Descriptor instead.
func (PartyMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerParty_proto_rawDescGZIP(), []int{0}
}

type PartyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sender      *Trainer         `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`
	MessaegType PartyMessageType `protobuf:"varint,2,opt,name=messaegType,proto3,enum=MainServer.PartyMessageType" json:"messaegType,omitempty"`
	SenderPokes []*Poke          `protobuf:"bytes,3,rep,name=senderPokes,proto3" json:"senderPokes,omitempty"`
	Prepare     *BattlePrepare   `protobuf:"bytes,4,opt,name=prepare,proto3" json:"prepare,omitempty"`
}

func (x *PartyMessage) Reset() {
	*x = PartyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerParty_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyMessage) ProtoMessage() {}

func (x *PartyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerParty_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyMessage.ProtoReflect.Descriptor instead.
func (*PartyMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerParty_proto_rawDescGZIP(), []int{0}
}

func (x *PartyMessage) GetSender() *Trainer {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *PartyMessage) GetMessaegType() PartyMessageType {
	if x != nil {
		return x.MessaegType
	}
	return PartyMessageType_party
}

func (x *PartyMessage) GetSenderPokes() []*Poke {
	if x != nil {
		return x.SenderPokes
	}
	return nil
}

func (x *PartyMessage) GetPrepare() *BattlePrepare {
	if x != nil {
		return x.Prepare
	}
	return nil
}

var File_MainServer_ServerParty_proto protoreflect.FileDescriptor

var file_MainServer_ServerParty_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x50, 0x61, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1b, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f,
	0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x0c, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x06,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x65,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x65, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x50, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x0b, 0x73,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x6f, 0x6b, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x70, 0x72,
	0x65, 0x70, 0x61, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x52, 0x07, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x2a,
	0x2e, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x70, 0x61, 0x72, 0x74, 0x79, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x79, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x10, 0x01, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_ServerParty_proto_rawDescOnce sync.Once
	file_MainServer_ServerParty_proto_rawDescData = file_MainServer_ServerParty_proto_rawDesc
)

func file_MainServer_ServerParty_proto_rawDescGZIP() []byte {
	file_MainServer_ServerParty_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerParty_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_ServerParty_proto_rawDescData)
	})
	return file_MainServer_ServerParty_proto_rawDescData
}

var file_MainServer_ServerParty_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ServerParty_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_ServerParty_proto_goTypes = []any{
	(PartyMessageType)(0), // 0: MainServer.PartyMessageType
	(*PartyMessage)(nil),  // 1: MainServer.PartyMessage
	(*Trainer)(nil),       // 2: MainServer.Trainer
	(*Poke)(nil),          // 3: MainServer.Poke
	(*BattlePrepare)(nil), // 4: MainServer.BattlePrepare
}
var file_MainServer_ServerParty_proto_depIdxs = []int32{
	2, // 0: MainServer.PartyMessage.sender:type_name -> MainServer.Trainer
	0, // 1: MainServer.PartyMessage.messaegType:type_name -> MainServer.PartyMessageType
	3, // 2: MainServer.PartyMessage.senderPokes:type_name -> MainServer.Poke
	4, // 3: MainServer.PartyMessage.prepare:type_name -> MainServer.BattlePrepare
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_ServerParty_proto_init() }
func file_MainServer_ServerParty_proto_init() {
	if File_MainServer_ServerParty_proto != nil {
		return
	}
	file_MainServer_BattleInfo_proto_init()
	file_MainServer_Trainer_proto_init()
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_ServerParty_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*PartyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_ServerParty_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerParty_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerParty_proto_depIdxs,
		EnumInfos:         file_MainServer_ServerParty_proto_enumTypes,
		MessageInfos:      file_MainServer_ServerParty_proto_msgTypes,
	}.Build()
	File_MainServer_ServerParty_proto = out.File
	file_MainServer_ServerParty_proto_rawDesc = nil
	file_MainServer_ServerParty_proto_goTypes = nil
	file_MainServer_ServerParty_proto_depIdxs = nil
}
