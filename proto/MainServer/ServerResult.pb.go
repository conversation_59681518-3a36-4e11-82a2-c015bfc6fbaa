// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/ServerResult.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求
type SinglePokeBoxParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoxType  PokeBoxType `protobuf:"varint,1,opt,name=boxType,proto3,enum=MainServer.PokeBoxType" json:"boxType,omitempty"`
	Index    int32       `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	UpdateTs int64       `protobuf:"varint,3,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
}

func (x *SinglePokeBoxParam) Reset() {
	*x = SinglePokeBoxParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SinglePokeBoxParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SinglePokeBoxParam) ProtoMessage() {}

func (x *SinglePokeBoxParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SinglePokeBoxParam.ProtoReflect.Descriptor instead.
func (*SinglePokeBoxParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{0}
}

func (x *SinglePokeBoxParam) GetBoxType() PokeBoxType {
	if x != nil {
		return x.BoxType
	}
	return PokeBoxType_normal
}

func (x *SinglePokeBoxParam) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SinglePokeBoxParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type FollowPokeParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeId int64 `protobuf:"varint,1,opt,name=pokeId,proto3" json:"pokeId,omitempty"`
	Remove bool  `protobuf:"varint,2,opt,name=remove,proto3" json:"remove,omitempty"`
	Ride   bool  `protobuf:"varint,3,opt,name=ride,proto3" json:"ride,omitempty"`
}

func (x *FollowPokeParam) Reset() {
	*x = FollowPokeParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowPokeParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowPokeParam) ProtoMessage() {}

func (x *FollowPokeParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowPokeParam.ProtoReflect.Descriptor instead.
func (*FollowPokeParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{1}
}

func (x *FollowPokeParam) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *FollowPokeParam) GetRemove() bool {
	if x != nil {
		return x.Remove
	}
	return false
}

func (x *FollowPokeParam) GetRide() bool {
	if x != nil {
		return x.Ride
	}
	return false
}

type InviteBattleParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrainerId  int64            `protobuf:"varint,1,opt,name=trainerId,proto3" json:"trainerId,omitempty"`
	InviteType InviteBattleType `protobuf:"varint,2,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	BattleType BattleType       `protobuf:"varint,3,opt,name=battleType,proto3,enum=MainServer.BattleType" json:"battleType,omitempty"`
}

func (x *InviteBattleParam) Reset() {
	*x = InviteBattleParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleParam) ProtoMessage() {}

func (x *InviteBattleParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleParam.ProtoReflect.Descriptor instead.
func (*InviteBattleParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{2}
}

func (x *InviteBattleParam) GetTrainerId() int64 {
	if x != nil {
		return x.TrainerId
	}
	return 0
}

func (x *InviteBattleParam) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

func (x *InviteBattleParam) GetBattleType() BattleType {
	if x != nil {
		return x.BattleType
	}
	return BattleType_BattleType_Unknow
}

type InviteBattleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *InviteBattleResponse) Reset() {
	*x = InviteBattleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleResponse) ProtoMessage() {}

func (x *InviteBattleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleResponse.ProtoReflect.Descriptor instead.
func (*InviteBattleResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{3}
}

func (x *InviteBattleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InviteBattleResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type InviteBattleRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProposerId       int64            `protobuf:"varint,1,opt,name=proposerId,proto3" json:"proposerId,omitempty"`
	TargetId         int64            `protobuf:"varint,2,opt,name=targetId,proto3" json:"targetId,omitempty"`
	InviteType       InviteBattleType `protobuf:"varint,3,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	InviteTime       int64            `protobuf:"varint,4,opt,name=inviteTime,proto3" json:"inviteTime,omitempty"`
	Responded        bool             `protobuf:"varint,5,opt,name=responded,proto3" json:"responded,omitempty"`
	Accepted         bool             `protobuf:"varint,6,opt,name=accepted,proto3" json:"accepted,omitempty"`
	InviteBattleType BattleType       `protobuf:"varint,7,opt,name=inviteBattleType,proto3,enum=MainServer.BattleType" json:"inviteBattleType,omitempty"`
}

func (x *InviteBattleRecord) Reset() {
	*x = InviteBattleRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleRecord) ProtoMessage() {}

func (x *InviteBattleRecord) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleRecord.ProtoReflect.Descriptor instead.
func (*InviteBattleRecord) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{4}
}

func (x *InviteBattleRecord) GetProposerId() int64 {
	if x != nil {
		return x.ProposerId
	}
	return 0
}

func (x *InviteBattleRecord) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *InviteBattleRecord) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

func (x *InviteBattleRecord) GetInviteTime() int64 {
	if x != nil {
		return x.InviteTime
	}
	return 0
}

func (x *InviteBattleRecord) GetResponded() bool {
	if x != nil {
		return x.Responded
	}
	return false
}

func (x *InviteBattleRecord) GetAccepted() bool {
	if x != nil {
		return x.Accepted
	}
	return false
}

func (x *InviteBattleRecord) GetInviteBattleType() BattleType {
	if x != nil {
		return x.InviteBattleType
	}
	return BattleType_BattleType_Unknow
}

type InviteBattleAcceptParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProposerId int64 `protobuf:"varint,1,opt,name=proposerId,proto3" json:"proposerId,omitempty"`
	Accept     bool  `protobuf:"varint,2,opt,name=accept,proto3" json:"accept,omitempty"`
}

func (x *InviteBattleAcceptParam) Reset() {
	*x = InviteBattleAcceptParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleAcceptParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleAcceptParam) ProtoMessage() {}

func (x *InviteBattleAcceptParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleAcceptParam.ProtoReflect.Descriptor instead.
func (*InviteBattleAcceptParam) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{5}
}

func (x *InviteBattleAcceptParam) GetProposerId() int64 {
	if x != nil {
		return x.ProposerId
	}
	return 0
}

func (x *InviteBattleAcceptParam) GetAccept() bool {
	if x != nil {
		return x.Accept
	}
	return false
}

type InviteBattleAcceptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MatchId string `protobuf:"bytes,3,opt,name=matchId,proto3" json:"matchId,omitempty"`
}

func (x *InviteBattleAcceptResponse) Reset() {
	*x = InviteBattleAcceptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteBattleAcceptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleAcceptResponse) ProtoMessage() {}

func (x *InviteBattleAcceptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleAcceptResponse.ProtoReflect.Descriptor instead.
func (*InviteBattleAcceptResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{6}
}

func (x *InviteBattleAcceptResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InviteBattleAcceptResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InviteBattleAcceptResponse) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

// 响应
type PokesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts    int64   `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Pokes []*Poke `protobuf:"bytes,3,rep,name=pokes,proto3" json:"pokes,omitempty"`
}

func (x *PokesResult) Reset() {
	*x = PokesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokesResult) ProtoMessage() {}

func (x *PokesResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokesResult.ProtoReflect.Descriptor instead.
func (*PokesResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{7}
}

func (x *PokesResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokesResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PokesResult) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type PokeBoxResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32      `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts   int64      `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Boxs []*PokeBox `protobuf:"bytes,3,rep,name=boxs,proto3" json:"boxs,omitempty"`
}

func (x *PokeBoxResult) Reset() {
	*x = PokeBoxResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PokeBoxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxResult) ProtoMessage() {}

func (x *PokeBoxResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxResult.ProtoReflect.Descriptor instead.
func (*PokeBoxResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{8}
}

func (x *PokeBoxResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PokeBoxResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PokeBoxResult) GetBoxs() []*PokeBox {
	if x != nil {
		return x.Boxs
	}
	return nil
}

type SinglePokeBoxResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts    int64    `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Box   *PokeBox `protobuf:"bytes,3,opt,name=box,proto3" json:"box,omitempty"`
	Pokes []*Poke  `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
}

func (x *SinglePokeBoxResult) Reset() {
	*x = SinglePokeBoxResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SinglePokeBoxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SinglePokeBoxResult) ProtoMessage() {}

func (x *SinglePokeBoxResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SinglePokeBoxResult.ProtoReflect.Descriptor instead.
func (*SinglePokeBoxResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{9}
}

func (x *SinglePokeBoxResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SinglePokeBoxResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *SinglePokeBoxResult) GetBox() *PokeBox {
	if x != nil {
		return x.Box
	}
	return nil
}

func (x *SinglePokeBoxResult) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type LoginSuccessResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LoginSuccessResult) Reset() {
	*x = LoginSuccessResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginSuccessResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginSuccessResult) ProtoMessage() {}

func (x *LoginSuccessResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginSuccessResult.ProtoReflect.Descriptor instead.
func (*LoginSuccessResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{10}
}

type TrainersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trainers      []*Trainer `protobuf:"bytes,1,rep,name=trainers,proto3" json:"trainers,omitempty"`
	PartyTrainers []*Trainer `protobuf:"bytes,2,rep,name=partyTrainers,proto3" json:"partyTrainers,omitempty"`
}

func (x *TrainersResult) Reset() {
	*x = TrainersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainersResult) ProtoMessage() {}

func (x *TrainersResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainersResult.ProtoReflect.Descriptor instead.
func (*TrainersResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{11}
}

func (x *TrainersResult) GetTrainers() []*Trainer {
	if x != nil {
		return x.Trainers
	}
	return nil
}

func (x *TrainersResult) GetPartyTrainers() []*Trainer {
	if x != nil {
		return x.PartyTrainers
	}
	return nil
}

type TrainerSelectResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trainer *Trainer    `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	Config  *GameConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *TrainerSelectResult) Reset() {
	*x = TrainerSelectResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerSelectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerSelectResult) ProtoMessage() {}

func (x *TrainerSelectResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerSelectResult.ProtoReflect.Descriptor instead.
func (*TrainerSelectResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{12}
}

func (x *TrainerSelectResult) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

func (x *TrainerSelectResult) GetConfig() *GameConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type InventorysResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32        `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts         int64        `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	Inventorys []*Inventory `protobuf:"bytes,3,rep,name=inventorys,proto3" json:"inventorys,omitempty"`
}

func (x *InventorysResult) Reset() {
	*x = InventorysResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InventorysResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorysResult) ProtoMessage() {}

func (x *InventorysResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorysResult.ProtoReflect.Descriptor instead.
func (*InventorysResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{13}
}

func (x *InventorysResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *InventorysResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *InventorysResult) GetInventorys() []*Inventory {
	if x != nil {
		return x.Inventorys
	}
	return nil
}

type GameConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OneUserMaxBoxCount        int32 `protobuf:"varint,1,opt,name=oneUserMaxBoxCount,proto3" json:"oneUserMaxBoxCount,omitempty"`
	OneUserSpecialMaxBoxCount int32 `protobuf:"varint,2,opt,name=oneUserSpecialMaxBoxCount,proto3" json:"oneUserSpecialMaxBoxCount,omitempty"`
}

func (x *GameConfig) Reset() {
	*x = GameConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameConfig) ProtoMessage() {}

func (x *GameConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameConfig.ProtoReflect.Descriptor instead.
func (*GameConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{14}
}

func (x *GameConfig) GetOneUserMaxBoxCount() int32 {
	if x != nil {
		return x.OneUserMaxBoxCount
	}
	return 0
}

func (x *GameConfig) GetOneUserSpecialMaxBoxCount() int32 {
	if x != nil {
		return x.OneUserSpecialMaxBoxCount
	}
	return 0
}

type MatchResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32              `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Ts         int64              `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	NewMatchId string             `protobuf:"bytes,3,opt,name=newMatchId,proto3" json:"newMatchId,omitempty"`
	Matchs     []*BattleMatchInfo `protobuf:"bytes,4,rep,name=matchs,proto3" json:"matchs,omitempty"`
}

func (x *MatchResult) Reset() {
	*x = MatchResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResult) ProtoMessage() {}

func (x *MatchResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResult.ProtoReflect.Descriptor instead.
func (*MatchResult) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{15}
}

func (x *MatchResult) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *MatchResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *MatchResult) GetNewMatchId() string {
	if x != nil {
		return x.NewMatchId
	}
	return ""
}

func (x *MatchResult) GetMatchs() []*BattleMatchInfo {
	if x != nil {
		return x.Matchs
	}
	return nil
}

// 更新宝可梦物品的请求参数
type RpcUpdatePokeItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid     int64  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`         // 宝可梦ID
	Destroy bool   `protobuf:"varint,2,opt,name=destroy,proto3" json:"destroy,omitempty"` // 是否销毁物品（可选，默认为false）
	Item    string `protobuf:"bytes,3,opt,name=item,proto3" json:"item,omitempty"`        // 新物品名称（可选，如果不提供则只移除当前物品）
}

func (x *RpcUpdatePokeItemRequest) Reset() {
	*x = RpcUpdatePokeItemRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerResult_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcUpdatePokeItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdatePokeItemRequest) ProtoMessage() {}

func (x *RpcUpdatePokeItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerResult_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdatePokeItemRequest.ProtoReflect.Descriptor instead.
func (*RpcUpdatePokeItemRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerResult_proto_rawDescGZIP(), []int{16}
}

func (x *RpcUpdatePokeItemRequest) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RpcUpdatePokeItemRequest) GetDestroy() bool {
	if x != nil {
		return x.Destroy
	}
	return false
}

func (x *RpcUpdatePokeItemRequest) GetItem() string {
	if x != nil {
		return x.Item
	}
	return ""
}

var File_MainServer_ServerResult_proto protoreflect.FileDescriptor

var file_MainServer_ServerResult_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50,
	0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2f, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x23, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x79, 0x0a, 0x12, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65,
	0x42, 0x6f, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x31, 0x0a, 0x07, 0x62, 0x6f, 0x78, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x62, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0x55, 0x0a,
	0x0f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x6f, 0x6b, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x70, 0x6f, 0x6b, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x69, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04,
	0x72, 0x69, 0x64, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4a,
	0x0a, 0x14, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x12, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a,
	0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x51, 0x0a, 0x17, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x22, 0x6a, 0x0a, 0x1a,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x0b, 0x50, 0x6f, 0x6b, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x70,
	0x6f, 0x6b, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x05, 0x70, 0x6f,
	0x6b, 0x65, 0x73, 0x22, 0x5c, 0x0a, 0x0d, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x6f, 0x78, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x52, 0x04, 0x62, 0x6f, 0x78,
	0x73, 0x22, 0x88, 0x01, 0x0a, 0x13, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65,
	0x42, 0x6f, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x25, 0x0a,
	0x03, 0x62, 0x6f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x42, 0x6f, 0x78, 0x52,
	0x03, 0x62, 0x6f, 0x78, 0x12, 0x26, 0x0a, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x22, 0x14, 0x0a, 0x12,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x7c, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73,
	0x22, 0x74, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x07, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x6d, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x6f, 0x72, 0x79, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x35,
	0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x65, 0x6e,
	0x74, 0x6f, 0x72, 0x79, 0x73, 0x22, 0x7a, 0x0a, 0x0a, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x2e, 0x0a, 0x12, 0x6f, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61,
	0x78, 0x42, 0x6f, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x6f, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x78, 0x42, 0x6f, 0x78, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x6f, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x61, 0x78, 0x42, 0x6f, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x6f, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x61, 0x78, 0x42, 0x6f, 0x78, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x86, 0x01, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x06, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x06, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x73, 0x22, 0x5a, 0x0a, 0x18, 0x52, 0x70,
	0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x74,
	0x72, 0x6f, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x73, 0x74, 0x72,
	0x6f, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_ServerResult_proto_rawDescOnce sync.Once
	file_MainServer_ServerResult_proto_rawDescData = file_MainServer_ServerResult_proto_rawDesc
)

func file_MainServer_ServerResult_proto_rawDescGZIP() []byte {
	file_MainServer_ServerResult_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerResult_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_ServerResult_proto_rawDescData)
	})
	return file_MainServer_ServerResult_proto_rawDescData
}

var file_MainServer_ServerResult_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_MainServer_ServerResult_proto_goTypes = []any{
	(*SinglePokeBoxParam)(nil),         // 0: MainServer.SinglePokeBoxParam
	(*FollowPokeParam)(nil),            // 1: MainServer.FollowPokeParam
	(*InviteBattleParam)(nil),          // 2: MainServer.InviteBattleParam
	(*InviteBattleResponse)(nil),       // 3: MainServer.InviteBattleResponse
	(*InviteBattleRecord)(nil),         // 4: MainServer.InviteBattleRecord
	(*InviteBattleAcceptParam)(nil),    // 5: MainServer.InviteBattleAcceptParam
	(*InviteBattleAcceptResponse)(nil), // 6: MainServer.InviteBattleAcceptResponse
	(*PokesResult)(nil),                // 7: MainServer.PokesResult
	(*PokeBoxResult)(nil),              // 8: MainServer.PokeBoxResult
	(*SinglePokeBoxResult)(nil),        // 9: MainServer.SinglePokeBoxResult
	(*LoginSuccessResult)(nil),         // 10: MainServer.LoginSuccessResult
	(*TrainersResult)(nil),             // 11: MainServer.TrainersResult
	(*TrainerSelectResult)(nil),        // 12: MainServer.TrainerSelectResult
	(*InventorysResult)(nil),           // 13: MainServer.InventorysResult
	(*GameConfig)(nil),                 // 14: MainServer.GameConfig
	(*MatchResult)(nil),                // 15: MainServer.MatchResult
	(*RpcUpdatePokeItemRequest)(nil),   // 16: MainServer.RpcUpdatePokeItemRequest
	(PokeBoxType)(0),                   // 17: MainServer.PokeBoxType
	(InviteBattleType)(0),              // 18: MainServer.InviteBattleType
	(BattleType)(0),                    // 19: MainServer.BattleType
	(*Poke)(nil),                       // 20: MainServer.Poke
	(*PokeBox)(nil),                    // 21: MainServer.PokeBox
	(*Trainer)(nil),                    // 22: MainServer.Trainer
	(*Inventory)(nil),                  // 23: MainServer.Inventory
	(*BattleMatchInfo)(nil),            // 24: MainServer.BattleMatchInfo
}
var file_MainServer_ServerResult_proto_depIdxs = []int32{
	17, // 0: MainServer.SinglePokeBoxParam.boxType:type_name -> MainServer.PokeBoxType
	18, // 1: MainServer.InviteBattleParam.inviteType:type_name -> MainServer.InviteBattleType
	19, // 2: MainServer.InviteBattleParam.battleType:type_name -> MainServer.BattleType
	18, // 3: MainServer.InviteBattleRecord.inviteType:type_name -> MainServer.InviteBattleType
	19, // 4: MainServer.InviteBattleRecord.inviteBattleType:type_name -> MainServer.BattleType
	20, // 5: MainServer.PokesResult.pokes:type_name -> MainServer.Poke
	21, // 6: MainServer.PokeBoxResult.boxs:type_name -> MainServer.PokeBox
	21, // 7: MainServer.SinglePokeBoxResult.box:type_name -> MainServer.PokeBox
	20, // 8: MainServer.SinglePokeBoxResult.pokes:type_name -> MainServer.Poke
	22, // 9: MainServer.TrainersResult.trainers:type_name -> MainServer.Trainer
	22, // 10: MainServer.TrainersResult.partyTrainers:type_name -> MainServer.Trainer
	22, // 11: MainServer.TrainerSelectResult.trainer:type_name -> MainServer.Trainer
	14, // 12: MainServer.TrainerSelectResult.config:type_name -> MainServer.GameConfig
	23, // 13: MainServer.InventorysResult.inventorys:type_name -> MainServer.Inventory
	24, // 14: MainServer.MatchResult.matchs:type_name -> MainServer.BattleMatchInfo
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_MainServer_ServerResult_proto_init() }
func file_MainServer_ServerResult_proto_init() {
	if File_MainServer_ServerResult_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	file_MainServer_PokeBox_proto_init()
	file_MainServer_Trainer_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_BattleMatch_proto_init()
	file_MainServer_ServerNotification_proto_init()
	file_MainServer_BattleInfo_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_ServerResult_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SinglePokeBoxParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*FollowPokeParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleAcceptParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*InviteBattleAcceptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PokesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PokeBoxResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SinglePokeBoxResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*LoginSuccessResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*TrainersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerSelectResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*InventorysResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GameConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*MatchResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_ServerResult_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*RpcUpdatePokeItemRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_ServerResult_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerResult_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerResult_proto_depIdxs,
		MessageInfos:      file_MainServer_ServerResult_proto_msgTypes,
	}.Build()
	File_MainServer_ServerResult_proto = out.File
	file_MainServer_ServerResult_proto_rawDesc = nil
	file_MainServer_ServerResult_proto_goTypes = nil
	file_MainServer_ServerResult_proto_depIdxs = nil
}
