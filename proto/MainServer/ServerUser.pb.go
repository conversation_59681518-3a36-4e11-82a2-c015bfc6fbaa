// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/ServerUser.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PublicUserStatus 存储公开状态（不包含敏感数据）
//
//	message PublicUserStatus {
//	    string loc = 1;  // 仅公开位置
//	}
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_ServerUser_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerUser_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerUser_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_MainServer_ServerUser_proto protoreflect.FileDescriptor

var file_MainServer_ServerUser_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x2a, 0x0a, 0x04, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_ServerUser_proto_rawDescOnce sync.Once
	file_MainServer_ServerUser_proto_rawDescData = file_MainServer_ServerUser_proto_rawDesc
)

func file_MainServer_ServerUser_proto_rawDescGZIP() []byte {
	file_MainServer_ServerUser_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerUser_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_ServerUser_proto_rawDescData)
	})
	return file_MainServer_ServerUser_proto_rawDescData
}

var file_MainServer_ServerUser_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_ServerUser_proto_goTypes = []any{
	(*User)(nil), // 0: MainServer.User
}
var file_MainServer_ServerUser_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_ServerUser_proto_init() }
func file_MainServer_ServerUser_proto_init() {
	if File_MainServer_ServerUser_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_ServerUser_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_ServerUser_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerUser_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerUser_proto_depIdxs,
		MessageInfos:      file_MainServer_ServerUser_proto_msgTypes,
	}.Build()
	File_MainServer_ServerUser_proto = out.File
	file_MainServer_ServerUser_proto_rawDesc = nil
	file_MainServer_ServerUser_proto_goTypes = nil
	file_MainServer_ServerUser_proto_depIdxs = nil
}
