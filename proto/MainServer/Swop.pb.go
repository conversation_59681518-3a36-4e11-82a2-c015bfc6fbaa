// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Swop.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SwopState int32

const (
	SwopState_INIT       SwopState = 0
	SwopState_REQUESTED  SwopState = 1
	SwopState_ACCEPTED   SwopState = 2
	SwopState_SELECTING  SwopState = 3
	SwopState_SUBMITTED  SwopState = 4
	SwopState_CONFIRMING SwopState = 5
	SwopState_CONFIRMED  SwopState = 6
	SwopState_COMPLETED  SwopState = 7
	SwopState_CANCELLED  SwopState = 8
	SwopState_CLOSED     SwopState = 9
)

// Enum value maps for SwopState.
var (
	SwopState_name = map[int32]string{
		0: "INIT",
		1: "REQUESTED",
		2: "ACCEPTED",
		3: "SELECTING",
		4: "SUBMITTED",
		5: "CONFIRMING",
		6: "CONFIRMED",
		7: "COMPLETED",
		8: "CANCELLED",
		9: "CLOSED",
	}
	SwopState_value = map[string]int32{
		"INIT":       0,
		"REQUESTED":  1,
		"ACCEPTED":   2,
		"SELECTING":  3,
		"SUBMITTED":  4,
		"CONFIRMING": 5,
		"CONFIRMED":  6,
		"COMPLETED":  7,
		"CANCELLED":  8,
		"CLOSED":     9,
	}
)

func (x SwopState) Enum() *SwopState {
	p := new(SwopState)
	*p = x
	return p
}

func (x SwopState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwopState) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Swop_proto_enumTypes[0].Descriptor()
}

func (SwopState) Type() protoreflect.EnumType {
	return &file_MainServer_Swop_proto_enumTypes[0]
}

func (x SwopState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwopState.Descriptor instead.
func (SwopState) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{0}
}

type BuyInventoryItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Count    int32  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *BuyInventoryItemInfo) Reset() {
	*x = BuyInventoryItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Swop_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuyInventoryItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuyInventoryItemInfo) ProtoMessage() {}

func (x *BuyInventoryItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuyInventoryItemInfo.ProtoReflect.Descriptor instead.
func (*BuyInventoryItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{0}
}

func (x *BuyInventoryItemInfo) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *BuyInventoryItemInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BuyInventoryItemInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SwopItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Count int32         `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Type  InventoryType `protobuf:"varint,4,opt,name=type,proto3,enum=MainServer.InventoryType" json:"type,omitempty"`
}

func (x *SwopItem) Reset() {
	*x = SwopItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Swop_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwopItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopItem) ProtoMessage() {}

func (x *SwopItem) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopItem.ProtoReflect.Descriptor instead.
func (*SwopItem) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{1}
}

func (x *SwopItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwopItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwopItem) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SwopItem) GetType() InventoryType {
	if x != nil {
		return x.Type
	}
	return InventoryType_inventory_nor
}

type SwopContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PokeIds []int64     `protobuf:"varint,1,rep,packed,name=poke_ids,json=pokeIds,proto3" json:"poke_ids,omitempty"`
	Pokes   []*Poke     `protobuf:"bytes,2,rep,name=pokes,proto3" json:"pokes,omitempty"`
	Items   []*SwopItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Coin    int64       `protobuf:"varint,4,opt,name=coin,proto3" json:"coin,omitempty"`
}

func (x *SwopContent) Reset() {
	*x = SwopContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Swop_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwopContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopContent) ProtoMessage() {}

func (x *SwopContent) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopContent.ProtoReflect.Descriptor instead.
func (*SwopContent) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{2}
}

func (x *SwopContent) GetPokeIds() []int64 {
	if x != nil {
		return x.PokeIds
	}
	return nil
}

func (x *SwopContent) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *SwopContent) GetItems() []*SwopItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *SwopContent) GetCoin() int64 {
	if x != nil {
		return x.Coin
	}
	return 0
}

type SwopInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SwopId           int64        `protobuf:"varint,1,opt,name=swop_id,json=swopId,proto3" json:"swop_id,omitempty"`
	InitiatorId      int64        `protobuf:"varint,2,opt,name=initiator_id,json=initiatorId,proto3" json:"initiator_id,omitempty"`
	TargetId         int64        `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	InitiatorState   SwopState    `protobuf:"varint,4,opt,name=initiator_state,json=initiatorState,proto3,enum=MainServer.SwopState" json:"initiator_state,omitempty"`
	TargetState      SwopState    `protobuf:"varint,5,opt,name=target_state,json=targetState,proto3,enum=MainServer.SwopState" json:"target_state,omitempty"`
	InitiatorContent *SwopContent `protobuf:"bytes,6,opt,name=initiator_content,json=initiatorContent,proto3" json:"initiator_content,omitempty"`
	TargetContent    *SwopContent `protobuf:"bytes,7,opt,name=target_content,json=targetContent,proto3" json:"target_content,omitempty"`
	CreateTs         int64        `protobuf:"varint,8,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	UpdateTs         int64        `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
}

func (x *SwopInfo) Reset() {
	*x = SwopInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Swop_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwopInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopInfo) ProtoMessage() {}

func (x *SwopInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopInfo.ProtoReflect.Descriptor instead.
func (*SwopInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{3}
}

func (x *SwopInfo) GetSwopId() int64 {
	if x != nil {
		return x.SwopId
	}
	return 0
}

func (x *SwopInfo) GetInitiatorId() int64 {
	if x != nil {
		return x.InitiatorId
	}
	return 0
}

func (x *SwopInfo) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *SwopInfo) GetInitiatorState() SwopState {
	if x != nil {
		return x.InitiatorState
	}
	return SwopState_INIT
}

func (x *SwopInfo) GetTargetState() SwopState {
	if x != nil {
		return x.TargetState
	}
	return SwopState_INIT
}

func (x *SwopInfo) GetInitiatorContent() *SwopContent {
	if x != nil {
		return x.InitiatorContent
	}
	return nil
}

func (x *SwopInfo) GetTargetContent() *SwopContent {
	if x != nil {
		return x.TargetContent
	}
	return nil
}

func (x *SwopInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *SwopInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type SwopChangeContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SwopId  int64        `protobuf:"varint,1,opt,name=swop_id,json=swopId,proto3" json:"swop_id,omitempty"`
	Content *SwopContent `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SwopChangeContentInfo) Reset() {
	*x = SwopChangeContentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Swop_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwopChangeContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopChangeContentInfo) ProtoMessage() {}

func (x *SwopChangeContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopChangeContentInfo.ProtoReflect.Descriptor instead.
func (*SwopChangeContentInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{4}
}

func (x *SwopChangeContentInfo) GetSwopId() int64 {
	if x != nil {
		return x.SwopId
	}
	return 0
}

func (x *SwopChangeContentInfo) GetContent() *SwopContent {
	if x != nil {
		return x.Content
	}
	return nil
}

var File_MainServer_Swop_proto protoreflect.FileDescriptor

var file_MainServer_Swop_proto_rawDesc = []byte{
	0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x53, 0x77, 0x6f,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x1a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a, 0x14, 0x42, 0x75, 0x79, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x73, 0x0a, 0x08, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x0b, 0x53, 0x77,
	0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x6b,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x70, 0x6f, 0x6b,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x22, 0x9d, 0x03, 0x0a,
	0x08, 0x53, 0x77, 0x6f, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x77, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x77, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x11,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0x63, 0x0a, 0x15,
	0x53, 0x77, 0x6f, 0x70, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x77, 0x6f, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x77, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x31,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x6f,
	0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2a, 0x99, 0x01, 0x0a, 0x09, 0x53, 0x77, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x08, 0x0a, 0x04, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x45,
	0x50, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x49,
	0x4e, 0x47, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x08, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x09, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_Swop_proto_rawDescOnce sync.Once
	file_MainServer_Swop_proto_rawDescData = file_MainServer_Swop_proto_rawDesc
)

func file_MainServer_Swop_proto_rawDescGZIP() []byte {
	file_MainServer_Swop_proto_rawDescOnce.Do(func() {
		file_MainServer_Swop_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Swop_proto_rawDescData)
	})
	return file_MainServer_Swop_proto_rawDescData
}

var file_MainServer_Swop_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Swop_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_MainServer_Swop_proto_goTypes = []any{
	(SwopState)(0),                // 0: MainServer.SwopState
	(*BuyInventoryItemInfo)(nil),  // 1: MainServer.BuyInventoryItemInfo
	(*SwopItem)(nil),              // 2: MainServer.SwopItem
	(*SwopContent)(nil),           // 3: MainServer.SwopContent
	(*SwopInfo)(nil),              // 4: MainServer.SwopInfo
	(*SwopChangeContentInfo)(nil), // 5: MainServer.SwopChangeContentInfo
	(InventoryType)(0),            // 6: MainServer.InventoryType
	(*Poke)(nil),                  // 7: MainServer.Poke
}
var file_MainServer_Swop_proto_depIdxs = []int32{
	6, // 0: MainServer.SwopItem.type:type_name -> MainServer.InventoryType
	7, // 1: MainServer.SwopContent.pokes:type_name -> MainServer.Poke
	2, // 2: MainServer.SwopContent.items:type_name -> MainServer.SwopItem
	0, // 3: MainServer.SwopInfo.initiator_state:type_name -> MainServer.SwopState
	0, // 4: MainServer.SwopInfo.target_state:type_name -> MainServer.SwopState
	3, // 5: MainServer.SwopInfo.initiator_content:type_name -> MainServer.SwopContent
	3, // 6: MainServer.SwopInfo.target_content:type_name -> MainServer.SwopContent
	3, // 7: MainServer.SwopChangeContentInfo.content:type_name -> MainServer.SwopContent
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_MainServer_Swop_proto_init() }
func file_MainServer_Swop_proto_init() {
	if File_MainServer_Swop_proto != nil {
		return
	}
	file_MainServer_Inventory_proto_init()
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Swop_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BuyInventoryItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Swop_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SwopItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Swop_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SwopContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Swop_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SwopInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Swop_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SwopChangeContentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Swop_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Swop_proto_goTypes,
		DependencyIndexes: file_MainServer_Swop_proto_depIdxs,
		EnumInfos:         file_MainServer_Swop_proto_enumTypes,
		MessageInfos:      file_MainServer_Swop_proto_msgTypes,
	}.Build()
	File_MainServer_Swop_proto = out.File
	file_MainServer_Swop_proto_rawDesc = nil
	file_MainServer_Swop_proto_goTypes = nil
	file_MainServer_Swop_proto_depIdxs = nil
}
