// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/Trainer.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerActionType int32

const (
	TrainerActionType_idle   TrainerActionType = 0
	TrainerActionType_battle TrainerActionType = 1
)

// Enum value maps for TrainerActionType.
var (
	TrainerActionType_name = map[int32]string{
		0: "idle",
		1: "battle",
	}
	TrainerActionType_value = map[string]int32{
		"idle":   0,
		"battle": 1,
	}
)

func (x TrainerActionType) Enum() *TrainerActionType {
	p := new(TrainerActionType)
	*p = x
	return p
}

func (x TrainerActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Trainer_proto_enumTypes[0].Descriptor()
}

func (TrainerActionType) Type() protoreflect.EnumType {
	return &file_MainServer_Trainer_proto_enumTypes[0]
}

func (x TrainerActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerActionType.Descriptor instead.
func (TrainerActionType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{0}
}

type TrainerSpecialRight int32

const (
	TrainerSpecialRight_TrainerNone    TrainerSpecialRight = 0
	TrainerSpecialRight_TrainerVip     TrainerSpecialRight = 1
	TrainerSpecialRight_TrainerPremium TrainerSpecialRight = 2
)

// Enum value maps for TrainerSpecialRight.
var (
	TrainerSpecialRight_name = map[int32]string{
		0: "TrainerNone",
		1: "TrainerVip",
		2: "TrainerPremium",
	}
	TrainerSpecialRight_value = map[string]int32{
		"TrainerNone":    0,
		"TrainerVip":     1,
		"TrainerPremium": 2,
	}
)

func (x TrainerSpecialRight) Enum() *TrainerSpecialRight {
	p := new(TrainerSpecialRight)
	*p = x
	return p
}

func (x TrainerSpecialRight) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerSpecialRight) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Trainer_proto_enumTypes[1].Descriptor()
}

func (TrainerSpecialRight) Type() protoreflect.EnumType {
	return &file_MainServer_Trainer_proto_enumTypes[1]
}

func (x TrainerSpecialRight) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerSpecialRight.Descriptor instead.
func (TrainerSpecialRight) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{1}
}

type Trainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid          string                      `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Name         string                      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Gender       string                      `protobuf:"bytes,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Loc          string                      `protobuf:"bytes,5,opt,name=loc,proto3" json:"loc,omitempty"` // regin_id|[x,y,z]
	PokeIds      []string                    `protobuf:"bytes,6,rep,name=poke_ids,json=pokeIds,proto3" json:"poke_ids,omitempty"`
	Action       TrainerActionType           `protobuf:"varint,7,opt,name=action,proto3,enum=MainServer.TrainerActionType" json:"action,omitempty"`
	Items        map[string]*TrainerItemInfo `protobuf:"bytes,8,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //使用的道具 //key为道具id //后面的info
	Badges       []string                    `protobuf:"bytes,9,rep,name=badges,proto3" json:"badges,omitempty"`                                                                                       //微章
	Belong       string                      `protobuf:"bytes,10,opt,name=belong,proto3" json:"belong,omitempty"`                                                                                      //所属阵营
	GroupId      string                      `protobuf:"bytes,11,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`                                                                     //玩家团
	Cloth        *TrainerCloth               `protobuf:"bytes,12,opt,name=cloth,proto3" json:"cloth,omitempty"`
	Coin         int64                       `protobuf:"varint,13,opt,name=coin,proto3" json:"coin,omitempty"`                                                                         //硬币
	BelongInfo   *TrainerBelongInfo          `protobuf:"bytes,14,opt,name=belong_info,json=belongInfo,proto3" json:"belong_info,omitempty"`                                            //阵营贡献
	SpecialCoin  int64                       `protobuf:"varint,15,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`                                        //特殊硬币 //比如用充值卡兑换的
	FollowPoke   *TrainerFollowPoke          `protobuf:"bytes,16,opt,name=follow_poke,json=followPoke,proto3" json:"follow_poke,omitempty"`                                            //跟随的宝可梦
	CreateTs     int64                       `protobuf:"varint,17,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                                 // 创建时间戳
	UpdateTs     int64                       `protobuf:"varint,18,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                                 // 更新时间戳
	OnlineTime   int64                       `protobuf:"varint,19,opt,name=online_time,json=onlineTime,proto3" json:"online_time,omitempty"`                                           // 在线时间
	SpecialRight TrainerSpecialRight         `protobuf:"varint,20,opt,name=special_right,json=specialRight,proto3,enum=MainServer.TrainerSpecialRight" json:"special_right,omitempty"` //特殊权限
	BoxStatus    *TrainerBoxStatus           `protobuf:"bytes,21,opt,name=box_status,json=boxStatus,proto3" json:"box_status,omitempty"`                                               //盒子状态
	SessionInfo  *TrainerSessionInfo         `protobuf:"bytes,22,opt,name=sessionInfo,proto3" json:"sessionInfo,omitempty"`                                                            //不存入数据库的数据
}

func (x *Trainer) Reset() {
	*x = Trainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trainer) ProtoMessage() {}

func (x *Trainer) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trainer.ProtoReflect.Descriptor instead.
func (*Trainer) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{0}
}

func (x *Trainer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Trainer) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Trainer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Trainer) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Trainer) GetLoc() string {
	if x != nil {
		return x.Loc
	}
	return ""
}

func (x *Trainer) GetPokeIds() []string {
	if x != nil {
		return x.PokeIds
	}
	return nil
}

func (x *Trainer) GetAction() TrainerActionType {
	if x != nil {
		return x.Action
	}
	return TrainerActionType_idle
}

func (x *Trainer) GetItems() map[string]*TrainerItemInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Trainer) GetBadges() []string {
	if x != nil {
		return x.Badges
	}
	return nil
}

func (x *Trainer) GetBelong() string {
	if x != nil {
		return x.Belong
	}
	return ""
}

func (x *Trainer) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Trainer) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

func (x *Trainer) GetCoin() int64 {
	if x != nil {
		return x.Coin
	}
	return 0
}

func (x *Trainer) GetBelongInfo() *TrainerBelongInfo {
	if x != nil {
		return x.BelongInfo
	}
	return nil
}

func (x *Trainer) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *Trainer) GetFollowPoke() *TrainerFollowPoke {
	if x != nil {
		return x.FollowPoke
	}
	return nil
}

func (x *Trainer) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Trainer) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *Trainer) GetOnlineTime() int64 {
	if x != nil {
		return x.OnlineTime
	}
	return 0
}

func (x *Trainer) GetSpecialRight() TrainerSpecialRight {
	if x != nil {
		return x.SpecialRight
	}
	return TrainerSpecialRight_TrainerNone
}

func (x *Trainer) GetBoxStatus() *TrainerBoxStatus {
	if x != nil {
		return x.BoxStatus
	}
	return nil
}

func (x *Trainer) GetSessionInfo() *TrainerSessionInfo {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

type TrainerItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseTs    int64 `protobuf:"varint,1,opt,name=useTs,proto3" json:"useTs,omitempty"`
	ExpireTs int64 `protobuf:"varint,2,opt,name=expireTs,proto3" json:"expireTs,omitempty"`
}

func (x *TrainerItemInfo) Reset() {
	*x = TrainerItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerItemInfo) ProtoMessage() {}

func (x *TrainerItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerItemInfo.ProtoReflect.Descriptor instead.
func (*TrainerItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerItemInfo) GetUseTs() int64 {
	if x != nil {
		return x.UseTs
	}
	return 0
}

func (x *TrainerItemInfo) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

type TrainerBelongInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contribution int64 `protobuf:"varint,1,opt,name=contribution,proto3" json:"contribution,omitempty"` //阵营贡献
	Level        int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *TrainerBelongInfo) Reset() {
	*x = TrainerBelongInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerBelongInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBelongInfo) ProtoMessage() {}

func (x *TrainerBelongInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBelongInfo.ProtoReflect.Descriptor instead.
func (*TrainerBelongInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{2}
}

func (x *TrainerBelongInfo) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *TrainerBelongInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type TrainerBoxStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActiveBoxes        int32 `protobuf:"varint,1,opt,name=active_boxes,json=activeBoxes,proto3" json:"active_boxes,omitempty"`
	SpecialActiveBoxes int32 `protobuf:"varint,2,opt,name=special_active_boxes,json=specialActiveBoxes,proto3" json:"special_active_boxes,omitempty"`
}

func (x *TrainerBoxStatus) Reset() {
	*x = TrainerBoxStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerBoxStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBoxStatus) ProtoMessage() {}

func (x *TrainerBoxStatus) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBoxStatus.ProtoReflect.Descriptor instead.
func (*TrainerBoxStatus) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{3}
}

func (x *TrainerBoxStatus) GetActiveBoxes() int32 {
	if x != nil {
		return x.ActiveBoxes
	}
	return 0
}

func (x *TrainerBoxStatus) GetSpecialActiveBoxes() int32 {
	if x != nil {
		return x.SpecialActiveBoxes
	}
	return 0
}

type TrainerSessionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BattlePokes  []*Poke         `protobuf:"bytes,1,rep,name=battlePokes,proto3" json:"battlePokes,omitempty"`                          //战斗的poke
	SessionEndTs int64           `protobuf:"varint,2,opt,name=session_end_ts,json=sessionEndTs,proto3" json:"session_end_ts,omitempty"` //离线时间 //不存入数据库
	LocInfo      *TrainerLocInfo `protobuf:"bytes,3,opt,name=locInfo,proto3" json:"locInfo,omitempty"`                                  //上一个位置信息，//不存入数据库 //位置信息
	MatchId      string          `protobuf:"bytes,4,opt,name=matchId,proto3" json:"matchId,omitempty"`
}

func (x *TrainerSessionInfo) Reset() {
	*x = TrainerSessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerSessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerSessionInfo) ProtoMessage() {}

func (x *TrainerSessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerSessionInfo.ProtoReflect.Descriptor instead.
func (*TrainerSessionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{4}
}

func (x *TrainerSessionInfo) GetBattlePokes() []*Poke {
	if x != nil {
		return x.BattlePokes
	}
	return nil
}

func (x *TrainerSessionInfo) GetSessionEndTs() int64 {
	if x != nil {
		return x.SessionEndTs
	}
	return 0
}

func (x *TrainerSessionInfo) GetLocInfo() *TrainerLocInfo {
	if x != nil {
		return x.LocInfo
	}
	return nil
}

func (x *TrainerSessionInfo) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

type TrainerCloth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nbody string `protobuf:"bytes,1,opt,name=nbody,proto3" json:"nbody,omitempty"`
}

func (x *TrainerCloth) Reset() {
	*x = TrainerCloth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerCloth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerCloth) ProtoMessage() {}

func (x *TrainerCloth) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerCloth.ProtoReflect.Descriptor instead.
func (*TrainerCloth) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{5}
}

func (x *TrainerCloth) GetNbody() string {
	if x != nil {
		return x.Nbody
	}
	return ""
}

type TrainerFollowPoke struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pokes []*TrainerFollowPokeInfo `protobuf:"bytes,1,rep,name=pokes,proto3" json:"pokes,omitempty"`
	Ride  string                   `protobuf:"bytes,2,opt,name=ride,proto3" json:"ride,omitempty"` //乘骑
}

func (x *TrainerFollowPoke) Reset() {
	*x = TrainerFollowPoke{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerFollowPoke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerFollowPoke) ProtoMessage() {}

func (x *TrainerFollowPoke) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerFollowPoke.ProtoReflect.Descriptor instead.
func (*TrainerFollowPoke) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{6}
}

func (x *TrainerFollowPoke) GetPokes() []*TrainerFollowPokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *TrainerFollowPoke) GetRide() string {
	if x != nil {
		return x.Ride
	}
	return ""
}

type TrainerFollowPokeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Shiny  int32  `protobuf:"varint,3,opt,name=shiny,proto3" json:"shiny,omitempty"`
	Gender string `protobuf:"bytes,4,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *TrainerFollowPokeInfo) Reset() {
	*x = TrainerFollowPokeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerFollowPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerFollowPokeInfo) ProtoMessage() {}

func (x *TrainerFollowPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerFollowPokeInfo.ProtoReflect.Descriptor instead.
func (*TrainerFollowPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{7}
}

func (x *TrainerFollowPokeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerFollowPokeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrainerFollowPokeInfo) GetShiny() int32 {
	if x != nil {
		return x.Shiny
	}
	return 0
}

func (x *TrainerFollowPokeInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

type TrainerLocInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Loc     *TrainerLoc   `protobuf:"bytes,1,opt,name=loc,proto3" json:"loc,omitempty"`
	LocLine []*TrainerLoc `protobuf:"bytes,2,rep,name=locLine,proto3" json:"locLine,omitempty"`
	Speed   float32       `protobuf:"fixed32,3,opt,name=speed,proto3" json:"speed,omitempty"`
}

func (x *TrainerLocInfo) Reset() {
	*x = TrainerLocInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerLocInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLocInfo) ProtoMessage() {}

func (x *TrainerLocInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLocInfo.ProtoReflect.Descriptor instead.
func (*TrainerLocInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{8}
}

func (x *TrainerLocInfo) GetLoc() *TrainerLoc {
	if x != nil {
		return x.Loc
	}
	return nil
}

func (x *TrainerLocInfo) GetLocLine() []*TrainerLoc {
	if x != nil {
		return x.LocLine
	}
	return nil
}

func (x *TrainerLocInfo) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type TrainerLoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReginId string  `protobuf:"bytes,1,opt,name=regin_id,json=reginId,proto3" json:"regin_id,omitempty"`
	X       float32 `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"`
	Y       float32 `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
	Z       float32 `protobuf:"fixed32,4,opt,name=z,proto3" json:"z,omitempty"`
}

func (x *TrainerLoc) Reset() {
	*x = TrainerLoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_Trainer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerLoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerLoc) ProtoMessage() {}

func (x *TrainerLoc) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Trainer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerLoc.ProtoReflect.Descriptor instead.
func (*TrainerLoc) Descriptor() ([]byte, []int) {
	return file_MainServer_Trainer_proto_rawDescGZIP(), []int{9}
}

func (x *TrainerLoc) GetReginId() string {
	if x != nil {
		return x.ReginId
	}
	return ""
}

func (x *TrainerLoc) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *TrainerLoc) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *TrainerLoc) GetZ() float32 {
	if x != nil {
		return x.Z
	}
	return 0
}

var File_MainServer_Trainer_proto protoreflect.FileDescriptor

var file_MainServer_Trainer_proto_rawDesc = []byte{
	0x0a, 0x18, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2f, 0x50, 0x6f, 0x6b, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x07,
	0x0a, 0x07, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6f, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x6b,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x6b,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x64, 0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x62, 0x61, 0x64, 0x67, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x65, 0x6c,
	0x6f, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x65, 0x6c, 0x6f, 0x6e,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x05,
	0x63, 0x6c, 0x6f, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x05, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x69, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x69, 0x6e,
	0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x69, 0x6e,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43,
	0x6f, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x6f,
	0x6b, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x0a, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50,
	0x6f, 0x6b, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x44,
	0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x52, 0x69, 0x67, 0x68, 0x74, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x62, 0x6f, 0x78, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x62, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x55, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x43, 0x0a, 0x0f, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a,
	0x05, 0x75, 0x73, 0x65, 0x54, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x75, 0x73,
	0x65, 0x54, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x73, 0x22,
	0x4d, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x67,
	0x0a, 0x10, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6f, 0x78,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x42, 0x6f, 0x78, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x42, 0x6f, 0x78, 0x65, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32,
	0x0a, 0x0b, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6f, 0x6b, 0x65, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6f, 0x6b,
	0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x6c, 0x6f, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f,
	0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x60,
	0x0a, 0x11, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50,
	0x6f, 0x6b, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x6f, 0x6b,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x70, 0x6f, 0x6b, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x69, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x69, 0x64, 0x65,
	0x22, 0x69, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x50, 0x6f, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x68, 0x69, 0x6e, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x68,
	0x69, 0x6e, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x0e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28,
	0x0a, 0x03, 0x6c, 0x6f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x4d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x4c, 0x6f, 0x63, 0x52, 0x03, 0x6c, 0x6f, 0x63, 0x12, 0x30, 0x0a, 0x07, 0x6c, 0x6f, 0x63, 0x4c,
	0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x4d, 0x61, 0x69, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f,
	0x63, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x22, 0x51, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x7a, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x01, 0x7a, 0x2a, 0x29, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x69, 0x64, 0x6c, 0x65,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x10, 0x01, 0x2a, 0x4a,
	0x0a, 0x13, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x56, 0x69, 0x70, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_MainServer_Trainer_proto_rawDescOnce sync.Once
	file_MainServer_Trainer_proto_rawDescData = file_MainServer_Trainer_proto_rawDesc
)

func file_MainServer_Trainer_proto_rawDescGZIP() []byte {
	file_MainServer_Trainer_proto_rawDescOnce.Do(func() {
		file_MainServer_Trainer_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_Trainer_proto_rawDescData)
	})
	return file_MainServer_Trainer_proto_rawDescData
}

var file_MainServer_Trainer_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_Trainer_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_MainServer_Trainer_proto_goTypes = []any{
	(TrainerActionType)(0),        // 0: MainServer.TrainerActionType
	(TrainerSpecialRight)(0),      // 1: MainServer.TrainerSpecialRight
	(*Trainer)(nil),               // 2: MainServer.Trainer
	(*TrainerItemInfo)(nil),       // 3: MainServer.TrainerItemInfo
	(*TrainerBelongInfo)(nil),     // 4: MainServer.TrainerBelongInfo
	(*TrainerBoxStatus)(nil),      // 5: MainServer.TrainerBoxStatus
	(*TrainerSessionInfo)(nil),    // 6: MainServer.TrainerSessionInfo
	(*TrainerCloth)(nil),          // 7: MainServer.TrainerCloth
	(*TrainerFollowPoke)(nil),     // 8: MainServer.TrainerFollowPoke
	(*TrainerFollowPokeInfo)(nil), // 9: MainServer.TrainerFollowPokeInfo
	(*TrainerLocInfo)(nil),        // 10: MainServer.TrainerLocInfo
	(*TrainerLoc)(nil),            // 11: MainServer.TrainerLoc
	nil,                           // 12: MainServer.Trainer.ItemsEntry
	(*Poke)(nil),                  // 13: MainServer.Poke
}
var file_MainServer_Trainer_proto_depIdxs = []int32{
	0,  // 0: MainServer.Trainer.action:type_name -> MainServer.TrainerActionType
	12, // 1: MainServer.Trainer.items:type_name -> MainServer.Trainer.ItemsEntry
	7,  // 2: MainServer.Trainer.cloth:type_name -> MainServer.TrainerCloth
	4,  // 3: MainServer.Trainer.belong_info:type_name -> MainServer.TrainerBelongInfo
	8,  // 4: MainServer.Trainer.follow_poke:type_name -> MainServer.TrainerFollowPoke
	1,  // 5: MainServer.Trainer.special_right:type_name -> MainServer.TrainerSpecialRight
	5,  // 6: MainServer.Trainer.box_status:type_name -> MainServer.TrainerBoxStatus
	6,  // 7: MainServer.Trainer.sessionInfo:type_name -> MainServer.TrainerSessionInfo
	13, // 8: MainServer.TrainerSessionInfo.battlePokes:type_name -> MainServer.Poke
	10, // 9: MainServer.TrainerSessionInfo.locInfo:type_name -> MainServer.TrainerLocInfo
	9,  // 10: MainServer.TrainerFollowPoke.pokes:type_name -> MainServer.TrainerFollowPokeInfo
	11, // 11: MainServer.TrainerLocInfo.loc:type_name -> MainServer.TrainerLoc
	11, // 12: MainServer.TrainerLocInfo.locLine:type_name -> MainServer.TrainerLoc
	3,  // 13: MainServer.Trainer.ItemsEntry.value:type_name -> MainServer.TrainerItemInfo
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_MainServer_Trainer_proto_init() }
func file_MainServer_Trainer_proto_init() {
	if File_MainServer_Trainer_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_Trainer_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Trainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerBelongInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerBoxStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerSessionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerCloth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerFollowPoke); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerFollowPokeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerLocInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_Trainer_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerLoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_Trainer_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Trainer_proto_goTypes,
		DependencyIndexes: file_MainServer_Trainer_proto_depIdxs,
		EnumInfos:         file_MainServer_Trainer_proto_enumTypes,
		MessageInfos:      file_MainServer_Trainer_proto_msgTypes,
	}.Build()
	File_MainServer_Trainer_proto = out.File
	file_MainServer_Trainer_proto_rawDesc = nil
	file_MainServer_Trainer_proto_goTypes = nil
	file_MainServer_Trainer_proto_depIdxs = nil
}
