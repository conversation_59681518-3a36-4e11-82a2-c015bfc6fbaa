// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerBadge.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerBadgeType int32

const (
	TrainerBadgeType_TRAINER_BADAGE_NONE TrainerBadgeType = 0
)

// Enum value maps for TrainerBadgeType.
var (
	TrainerBadgeType_name = map[int32]string{
		0: "TRAINER_BADAGE_NONE",
	}
	TrainerBadgeType_value = map[string]int32{
		"TRAINER_BADAGE_NONE": 0,
	}
)

func (x TrainerBadgeType) Enum() *TrainerBadgeType {
	p := new(TrainerBadgeType)
	*p = x
	return p
}

func (x TrainerBadgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerBadgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerBadge_proto_enumTypes[0].Descriptor()
}

func (TrainerBadgeType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerBadge_proto_enumTypes[0]
}

func (x TrainerBadgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerBadgeType.Descriptor instead.
func (TrainerBadgeType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

type TrainerBadges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Badges       []*TrainerBadge `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
	LastUpdateTs int64           `protobuf:"varint,2,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
}

func (x *TrainerBadges) Reset() {
	*x = TrainerBadges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerBadges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadges) ProtoMessage() {}

func (x *TrainerBadges) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadges.ProtoReflect.Descriptor instead.
func (*TrainerBadges) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerBadges) GetBadges() []*TrainerBadge {
	if x != nil {
		return x.Badges
	}
	return nil
}

func (x *TrainerBadges) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

type TrainerBadge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     TrainerBadgeType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerBadgeType" json:"type,omitempty"`
	CreateTs int64            `protobuf:"varint,2,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
}

func (x *TrainerBadge) Reset() {
	*x = TrainerBadge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerBadge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadge) ProtoMessage() {}

func (x *TrainerBadge) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadge.ProtoReflect.Descriptor instead.
func (*TrainerBadge) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerBadge) GetType() TrainerBadgeType {
	if x != nil {
		return x.Type
	}
	return TrainerBadgeType_TRAINER_BADAGE_NONE
}

func (x *TrainerBadge) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

var File_MainServer_TrainerBadge_proto protoreflect.FileDescriptor

var file_MainServer_TrainerBadge_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x64, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x67, 0x0a, 0x0d, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x64, 0x67, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x06,
	0x62, 0x61, 0x64, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x42, 0x61, 0x64, 0x67, 0x65, 0x52, 0x06, 0x62, 0x61, 0x64, 0x67, 0x65, 0x73, 0x12, 0x24,
	0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x73, 0x22, 0x5d, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42,
	0x61, 0x64, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x73, 0x2a, 0x2b, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x61,
	0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x49, 0x4e,
	0x45, 0x52, 0x5f, 0x42, 0x41, 0x44, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerBadge_proto_rawDescOnce sync.Once
	file_MainServer_TrainerBadge_proto_rawDescData = file_MainServer_TrainerBadge_proto_rawDesc
)

func file_MainServer_TrainerBadge_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerBadge_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerBadge_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerBadge_proto_rawDescData)
	})
	return file_MainServer_TrainerBadge_proto_rawDescData
}

var file_MainServer_TrainerBadge_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerBadge_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerBadge_proto_goTypes = []any{
	(TrainerBadgeType)(0), // 0: MainServer.TrainerBadgeType
	(*TrainerBadges)(nil), // 1: MainServer.TrainerBadges
	(*TrainerBadge)(nil),  // 2: MainServer.TrainerBadge
}
var file_MainServer_TrainerBadge_proto_depIdxs = []int32{
	2, // 0: MainServer.TrainerBadges.badges:type_name -> MainServer.TrainerBadge
	0, // 1: MainServer.TrainerBadge.type:type_name -> MainServer.TrainerBadgeType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerBadge_proto_init() }
func file_MainServer_TrainerBadge_proto_init() {
	if File_MainServer_TrainerBadge_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerBadge_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerBadges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerBadge_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerBadge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerBadge_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerBadge_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerBadge_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerBadge_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerBadge_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerBadge_proto = out.File
	file_MainServer_TrainerBadge_proto_rawDesc = nil
	file_MainServer_TrainerBadge_proto_goTypes = nil
	file_MainServer_TrainerBadge_proto_depIdxs = nil
}
