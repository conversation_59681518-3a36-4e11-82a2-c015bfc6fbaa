// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerCloth.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerClothType int32

const (
	TrainerClothType_CLOTH_TYPE_OLD_NIN   TrainerClothType = 0
	TrainerClothType_CLOTH_TYPE_HAT       TrainerClothType = 1 // 帽子
	TrainerClothType_CLOTH_TYPE_TOP       TrainerClothType = 2 // 上衣
	TrainerClothType_CLOTH_TYPE_BOTTOM    TrainerClothType = 3 // 下装
	TrainerClothType_CLOTH_TYPE_SHOES     TrainerClothType = 4 // 鞋子
	TrainerClothType_CLOTH_TYPE_ACCESSORY TrainerClothType = 5 // 配饰
)

// Enum value maps for TrainerClothType.
var (
	TrainerClothType_name = map[int32]string{
		0: "CLOTH_TYPE_OLD_NIN",
		1: "CLOTH_TYPE_HAT",
		2: "CLOTH_TYPE_TOP",
		3: "CLOTH_TYPE_BOTTOM",
		4: "CLOTH_TYPE_SHOES",
		5: "CLOTH_TYPE_ACCESSORY",
	}
	TrainerClothType_value = map[string]int32{
		"CLOTH_TYPE_OLD_NIN":   0,
		"CLOTH_TYPE_HAT":       1,
		"CLOTH_TYPE_TOP":       2,
		"CLOTH_TYPE_BOTTOM":    3,
		"CLOTH_TYPE_SHOES":     4,
		"CLOTH_TYPE_ACCESSORY": 5,
	}
)

func (x TrainerClothType) Enum() *TrainerClothType {
	p := new(TrainerClothType)
	*p = x
	return p
}

func (x TrainerClothType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerClothType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerCloth_proto_enumTypes[0].Descriptor()
}

func (TrainerClothType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerCloth_proto_enumTypes[0]
}

func (x TrainerClothType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerClothType.Descriptor instead.
func (TrainerClothType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{0}
}

// 单个训练师服装
type TrainerCloth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     TrainerClothType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"`
	Name     string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CreateTs int64            `protobuf:"varint,3,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 创建时间戳
	UpdateTs int64            `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
}

func (x *TrainerCloth) Reset() {
	*x = TrainerCloth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerCloth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerCloth) ProtoMessage() {}

func (x *TrainerCloth) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerCloth.ProtoReflect.Descriptor instead.
func (*TrainerCloth) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerCloth) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *TrainerCloth) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrainerCloth) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *TrainerCloth) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 训练师服装盒子 (某个品类下的全部服饰)
type TrainerBoxCloth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cloths map[string]*TrainerCloth `protobuf:"bytes,1,rep,name=cloths,proto3" json:"cloths,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // clothId -> TrainerCloth
}

func (x *TrainerBoxCloth) Reset() {
	*x = TrainerBoxCloth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerBoxCloth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBoxCloth) ProtoMessage() {}

func (x *TrainerBoxCloth) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBoxCloth.ProtoReflect.Descriptor instead.
func (*TrainerBoxCloth) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerBoxCloth) GetCloths() map[string]*TrainerCloth {
	if x != nil {
		return x.Cloths
	}
	return nil
}

// RPC请求：添加训练师服装
type RpcAddTrainerClothRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    TrainerClothType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId string           `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
	Cloth   *TrainerCloth    `protobuf:"bytes,3,opt,name=cloth,proto3" json:"cloth,omitempty"`                                 // 服装数据
}

func (x *RpcAddTrainerClothRequest) Reset() {
	*x = RpcAddTrainerClothRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcAddTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerClothRequest) ProtoMessage() {}

func (x *RpcAddTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{2}
}

func (x *RpcAddTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcAddTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

func (x *RpcAddTrainerClothRequest) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

// RPC响应：添加训练师服装
type RpcAddTrainerClothResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *RpcAddTrainerClothResponse) Reset() {
	*x = RpcAddTrainerClothResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcAddTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerClothResponse) ProtoMessage() {}

func (x *RpcAddTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{3}
}

func (x *RpcAddTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcAddTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RPC请求：删除训练师服装
type RpcRemoveTrainerClothRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    TrainerClothType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId string           `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
}

func (x *RpcRemoveTrainerClothRequest) Reset() {
	*x = RpcRemoveTrainerClothRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcRemoveTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRemoveTrainerClothRequest) ProtoMessage() {}

func (x *RpcRemoveTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRemoveTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcRemoveTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{4}
}

func (x *RpcRemoveTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcRemoveTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

// RPC响应：删除训练师服装
type RpcRemoveTrainerClothResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *RpcRemoveTrainerClothResponse) Reset() {
	*x = RpcRemoveTrainerClothResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcRemoveTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRemoveTrainerClothResponse) ProtoMessage() {}

func (x *RpcRemoveTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRemoveTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcRemoveTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{5}
}

func (x *RpcRemoveTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcRemoveTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RPC请求：获取训练师服装盒子
type RpcGetTrainerClothRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type TrainerClothType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
}

func (x *RpcGetTrainerClothRequest) Reset() {
	*x = RpcGetTrainerClothRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerClothRequest) ProtoMessage() {}

func (x *RpcGetTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{6}
}

func (x *RpcGetTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

// RPC响应：获取训练师服装盒子
type RpcGetTrainerClothResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClothBox *TrainerBoxCloth `protobuf:"bytes,1,opt,name=cloth_box,json=clothBox,proto3" json:"cloth_box,omitempty"` // 服装盒子数据
}

func (x *RpcGetTrainerClothResponse) Reset() {
	*x = RpcGetTrainerClothResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerClothResponse) ProtoMessage() {}

func (x *RpcGetTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{7}
}

func (x *RpcGetTrainerClothResponse) GetClothBox() *TrainerBoxCloth {
	if x != nil {
		return x.ClothBox
	}
	return nil
}

// RPC响应：获取所有训练师服装盒子
type RpcGetAllTrainerClothResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClothBoxes map[int32]*TrainerBoxCloth `protobuf:"bytes,1,rep,name=cloth_boxes,json=clothBoxes,proto3" json:"cloth_boxes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 服装盒子映射 (类型 -> 服装盒子)
}

func (x *RpcGetAllTrainerClothResponse) Reset() {
	*x = RpcGetAllTrainerClothResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetAllTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAllTrainerClothResponse) ProtoMessage() {}

func (x *RpcGetAllTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAllTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcGetAllTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{8}
}

func (x *RpcGetAllTrainerClothResponse) GetClothBoxes() map[int32]*TrainerBoxCloth {
	if x != nil {
		return x.ClothBoxes
	}
	return nil
}

// RPC请求：更新训练师服装
type RpcUpdateTrainerClothRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    TrainerClothType `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId string           `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
}

func (x *RpcUpdateTrainerClothRequest) Reset() {
	*x = RpcUpdateTrainerClothRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcUpdateTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateTrainerClothRequest) ProtoMessage() {}

func (x *RpcUpdateTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcUpdateTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{9}
}

func (x *RpcUpdateTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcUpdateTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

// RPC响应：更新训练师服装
type RpcUpdateTrainerClothResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
}

func (x *RpcUpdateTrainerClothResponse) Reset() {
	*x = RpcUpdateTrainerClothResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerCloth_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcUpdateTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateTrainerClothResponse) ProtoMessage() {}

func (x *RpcUpdateTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcUpdateTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{10}
}

func (x *RpcUpdateTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcUpdateTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_TrainerCloth_proto protoreflect.FileDescriptor

var file_MainServer_TrainerCloth_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x8e, 0x01, 0x0a, 0x0c,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43,
	0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0xa7, 0x01, 0x0a,
	0x0f, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78, 0x43, 0x6c, 0x6f, 0x74, 0x68,
	0x12, 0x3f, 0x0a, 0x06, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x2e, 0x43, 0x6c,
	0x6f, 0x74, 0x68, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x6c, 0x6f, 0x74, 0x68,
	0x73, 0x1a, 0x53, 0x0a, 0x0b, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x98, 0x01, 0x0a, 0x19, 0x52, 0x70, 0x63, 0x41, 0x64,
	0x64, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x05, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x05, 0x63, 0x6c, 0x6f, 0x74,
	0x68, 0x22, 0x50, 0x0a, 0x1a, 0x52, 0x70, 0x63, 0x41, 0x64, 0x64, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x6b, 0x0a, 0x1c, 0x52, 0x70, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x49, 0x64,
	0x22, 0x53, 0x0a, 0x1d, 0x52, 0x70, 0x63, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x4d, 0x0a, 0x19, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x56, 0x0a, 0x1a, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x5f, 0x62, 0x6f, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78, 0x43, 0x6c, 0x6f,
	0x74, 0x68, 0x52, 0x08, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x42, 0x6f, 0x78, 0x22, 0xd7, 0x01, 0x0a,
	0x1d, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a,
	0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x74, 0x68, 0x5f, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6c, 0x6f, 0x74, 0x68, 0x42, 0x6f, 0x78, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a,
	0x63, 0x6c, 0x6f, 0x74, 0x68, 0x42, 0x6f, 0x78, 0x65, 0x73, 0x1a, 0x5a, 0x0a, 0x0f, 0x43, 0x6c,
	0x6f, 0x74, 0x68, 0x42, 0x6f, 0x78, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x42, 0x6f, 0x78, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6b, 0x0a, 0x1c, 0x52, 0x70, 0x63, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x6f, 0x74,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x6f, 0x74,
	0x68, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x1d, 0x52, 0x70, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x99, 0x01, 0x0a, 0x10, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x12, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4c, 0x44, 0x5f,
	0x4e, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x48, 0x41, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4c, 0x4f,
	0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x50, 0x10, 0x02, 0x12, 0x15, 0x0a,
	0x11, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x45, 0x53, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4c,
	0x4f, 0x54, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f,
	0x52, 0x59, 0x10, 0x05, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerCloth_proto_rawDescOnce sync.Once
	file_MainServer_TrainerCloth_proto_rawDescData = file_MainServer_TrainerCloth_proto_rawDesc
)

func file_MainServer_TrainerCloth_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerCloth_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerCloth_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerCloth_proto_rawDescData)
	})
	return file_MainServer_TrainerCloth_proto_rawDescData
}

var file_MainServer_TrainerCloth_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerCloth_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_MainServer_TrainerCloth_proto_goTypes = []any{
	(TrainerClothType)(0),                 // 0: MainServer.TrainerClothType
	(*TrainerCloth)(nil),                  // 1: MainServer.TrainerCloth
	(*TrainerBoxCloth)(nil),               // 2: MainServer.TrainerBoxCloth
	(*RpcAddTrainerClothRequest)(nil),     // 3: MainServer.RpcAddTrainerClothRequest
	(*RpcAddTrainerClothResponse)(nil),    // 4: MainServer.RpcAddTrainerClothResponse
	(*RpcRemoveTrainerClothRequest)(nil),  // 5: MainServer.RpcRemoveTrainerClothRequest
	(*RpcRemoveTrainerClothResponse)(nil), // 6: MainServer.RpcRemoveTrainerClothResponse
	(*RpcGetTrainerClothRequest)(nil),     // 7: MainServer.RpcGetTrainerClothRequest
	(*RpcGetTrainerClothResponse)(nil),    // 8: MainServer.RpcGetTrainerClothResponse
	(*RpcGetAllTrainerClothResponse)(nil), // 9: MainServer.RpcGetAllTrainerClothResponse
	(*RpcUpdateTrainerClothRequest)(nil),  // 10: MainServer.RpcUpdateTrainerClothRequest
	(*RpcUpdateTrainerClothResponse)(nil), // 11: MainServer.RpcUpdateTrainerClothResponse
	nil,                                   // 12: MainServer.TrainerBoxCloth.ClothsEntry
	nil,                                   // 13: MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry
}
var file_MainServer_TrainerCloth_proto_depIdxs = []int32{
	0,  // 0: MainServer.TrainerCloth.type:type_name -> MainServer.TrainerClothType
	12, // 1: MainServer.TrainerBoxCloth.cloths:type_name -> MainServer.TrainerBoxCloth.ClothsEntry
	0,  // 2: MainServer.RpcAddTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	1,  // 3: MainServer.RpcAddTrainerClothRequest.cloth:type_name -> MainServer.TrainerCloth
	0,  // 4: MainServer.RpcRemoveTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	0,  // 5: MainServer.RpcGetTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	2,  // 6: MainServer.RpcGetTrainerClothResponse.cloth_box:type_name -> MainServer.TrainerBoxCloth
	13, // 7: MainServer.RpcGetAllTrainerClothResponse.cloth_boxes:type_name -> MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry
	0,  // 8: MainServer.RpcUpdateTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	1,  // 9: MainServer.TrainerBoxCloth.ClothsEntry.value:type_name -> MainServer.TrainerCloth
	2,  // 10: MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry.value:type_name -> MainServer.TrainerBoxCloth
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerCloth_proto_init() }
func file_MainServer_TrainerCloth_proto_init() {
	if File_MainServer_TrainerCloth_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerCloth_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerCloth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerBoxCloth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RpcAddTrainerClothRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RpcAddTrainerClothResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RpcRemoveTrainerClothRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*RpcRemoveTrainerClothResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetTrainerClothRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetTrainerClothResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetAllTrainerClothResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RpcUpdateTrainerClothRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerCloth_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*RpcUpdateTrainerClothResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerCloth_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerCloth_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerCloth_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerCloth_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerCloth_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerCloth_proto = out.File
	file_MainServer_TrainerCloth_proto_rawDesc = nil
	file_MainServer_TrainerCloth_proto_goTypes = nil
	file_MainServer_TrainerCloth_proto_depIdxs = nil
}
