// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerQuest.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerQuestStatus int32

const (
	TrainerQuestStatus_TrainerQuestStatus_none    TrainerQuestStatus = 0 // 未接受
	TrainerQuestStatus_TrainerQuestStatus_accept  TrainerQuestStatus = 1 // 已接受
	TrainerQuestStatus_TrainerQuestStatus_finish  TrainerQuestStatus = 2 // 已完成
	TrainerQuestStatus_TrainerQuestStatus_reward  TrainerQuestStatus = 3 // 已领取奖励
	TrainerQuestStatus_TrainerQuestStatus_cancel  TrainerQuestStatus = 4 // 已取消
	TrainerQuestStatus_TrainerQuestStatus_timeout TrainerQuestStatus = 5 // 已超时
	TrainerQuestStatus_TrainerQuestStatus_close   TrainerQuestStatus = 6 // 已关闭
	TrainerQuestStatus_TrainerQuestStatus_failed  TrainerQuestStatus = 7 // 已重复
)

// Enum value maps for TrainerQuestStatus.
var (
	TrainerQuestStatus_name = map[int32]string{
		0: "TrainerQuestStatus_none",
		1: "TrainerQuestStatus_accept",
		2: "TrainerQuestStatus_finish",
		3: "TrainerQuestStatus_reward",
		4: "TrainerQuestStatus_cancel",
		5: "TrainerQuestStatus_timeout",
		6: "TrainerQuestStatus_close",
		7: "TrainerQuestStatus_failed",
	}
	TrainerQuestStatus_value = map[string]int32{
		"TrainerQuestStatus_none":    0,
		"TrainerQuestStatus_accept":  1,
		"TrainerQuestStatus_finish":  2,
		"TrainerQuestStatus_reward":  3,
		"TrainerQuestStatus_cancel":  4,
		"TrainerQuestStatus_timeout": 5,
		"TrainerQuestStatus_close":   6,
		"TrainerQuestStatus_failed":  7,
	}
)

func (x TrainerQuestStatus) Enum() *TrainerQuestStatus {
	p := new(TrainerQuestStatus)
	*p = x
	return p
}

func (x TrainerQuestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerQuestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerQuest_proto_enumTypes[0].Descriptor()
}

func (TrainerQuestStatus) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerQuest_proto_enumTypes[0]
}

func (x TrainerQuestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerQuestStatus.Descriptor instead.
func (TrainerQuestStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{0}
}

type TrainerQuest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid              int64                    `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	QuestId          int64                    `protobuf:"varint,3,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	QuestType        QuestType                `protobuf:"varint,4,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"` //这个在领取的时候就确定了
	QuestStatus      TrainerQuestStatus       `protobuf:"varint,5,opt,name=quest_status,json=questStatus,proto3,enum=MainServer.TrainerQuestStatus" json:"quest_status,omitempty"`
	QuestCurrentInfo *TrainerQuestCurrentInfo `protobuf:"bytes,6,opt,name=quest_current_info,json=questCurrentInfo,proto3" json:"quest_current_info,omitempty"`
	// map<string, int32> quest_progress = 6; // 进度 表示当前完成了多少，比如击败了多少个poke啥的
	QuestStartTime       int64      `protobuf:"varint,7,opt,name=quest_start_time,json=questStartTime,proto3" json:"quest_start_time,omitempty"`                     // 开始时间
	QuestEndTime         int64      `protobuf:"varint,8,opt,name=quest_end_time,json=questEndTime,proto3" json:"quest_end_time,omitempty"`                           // 结束时间 （可能是完成，可能是超时等）
	QuestRepeatLimitTime int32      `protobuf:"varint,9,opt,name=quest_repeat_limit_time,json=questRepeatLimitTime,proto3" json:"quest_repeat_limit_time,omitempty"` // 时间限制
	QuestInfo            *QuestInfo `protobuf:"bytes,10,opt,name=quest_info,json=questInfo,proto3" json:"quest_info,omitempty"`                                      // 任务信息 可能要隐藏部分信息
	UpdateTs             int64      `protobuf:"varint,11,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                        // 更新时间
}

func (x *TrainerQuest) Reset() {
	*x = TrainerQuest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerQuest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerQuest) ProtoMessage() {}

func (x *TrainerQuest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerQuest.ProtoReflect.Descriptor instead.
func (*TrainerQuest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerQuest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrainerQuest) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TrainerQuest) GetQuestId() int64 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *TrainerQuest) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

func (x *TrainerQuest) GetQuestStatus() TrainerQuestStatus {
	if x != nil {
		return x.QuestStatus
	}
	return TrainerQuestStatus_TrainerQuestStatus_none
}

func (x *TrainerQuest) GetQuestCurrentInfo() *TrainerQuestCurrentInfo {
	if x != nil {
		return x.QuestCurrentInfo
	}
	return nil
}

func (x *TrainerQuest) GetQuestStartTime() int64 {
	if x != nil {
		return x.QuestStartTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestEndTime() int64 {
	if x != nil {
		return x.QuestEndTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestRepeatLimitTime() int32 {
	if x != nil {
		return x.QuestRepeatLimitTime
	}
	return 0
}

func (x *TrainerQuest) GetQuestInfo() *QuestInfo {
	if x != nil {
		return x.QuestInfo
	}
	return nil
}

func (x *TrainerQuest) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type TrainerQuestCurrentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentQuest          *QuestInfo       `protobuf:"bytes,1,opt,name=current_quest,json=currentQuest,proto3" json:"current_quest,omitempty"`                                                                                                                       //(当前任务的线性任务的id) 因为有可能会随机任务所以这个地方记录下确定的任务才能知道也好完成什么，完成的条件是什么
	QuestDefaultCondition map[string]int32 `protobuf:"bytes,2,rep,name=quest_default_condition,json=questDefaultCondition,proto3" json:"quest_default_condition,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //需要完成的条件 //接受的时候记录
	QuestProgress         map[string]int32 `protobuf:"bytes,3,rep,name=quest_progress,json=questProgress,proto3" json:"quest_progress,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                           // 进度 表示当前完成了多少，比如击败了多少个poke啥的 上面的好用于对比
}

func (x *TrainerQuestCurrentInfo) Reset() {
	*x = TrainerQuestCurrentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerQuestCurrentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerQuestCurrentInfo) ProtoMessage() {}

func (x *TrainerQuestCurrentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerQuestCurrentInfo.ProtoReflect.Descriptor instead.
func (*TrainerQuestCurrentInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerQuestCurrentInfo) GetCurrentQuest() *QuestInfo {
	if x != nil {
		return x.CurrentQuest
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetQuestDefaultCondition() map[string]int32 {
	if x != nil {
		return x.QuestDefaultCondition
	}
	return nil
}

func (x *TrainerQuestCurrentInfo) GetQuestProgress() map[string]int32 {
	if x != nil {
		return x.QuestProgress
	}
	return nil
}

// 接受任务
type RpcAcceptQuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId int32 `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
}

func (x *RpcAcceptQuestRequest) Reset() {
	*x = RpcAcceptQuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcAcceptQuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAcceptQuestRequest) ProtoMessage() {}

func (x *RpcAcceptQuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAcceptQuestRequest.ProtoReflect.Descriptor instead.
func (*RpcAcceptQuestRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{2}
}

func (x *RpcAcceptQuestRequest) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

type RpcAcceptQuestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *RpcAcceptQuestResponse) Reset() {
	*x = RpcAcceptQuestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcAcceptQuestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAcceptQuestResponse) ProtoMessage() {}

func (x *RpcAcceptQuestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAcceptQuestResponse.ProtoReflect.Descriptor instead.
func (*RpcAcceptQuestResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{3}
}

func (x *RpcAcceptQuestResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcAcceptQuestResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 完成任务
type RpcCompleteQuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId int32 `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
}

func (x *RpcCompleteQuestRequest) Reset() {
	*x = RpcCompleteQuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcCompleteQuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcCompleteQuestRequest) ProtoMessage() {}

func (x *RpcCompleteQuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcCompleteQuestRequest.ProtoReflect.Descriptor instead.
func (*RpcCompleteQuestRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{4}
}

func (x *RpcCompleteQuestRequest) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

type RpcCompleteQuestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *RpcCompleteQuestResponse) Reset() {
	*x = RpcCompleteQuestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcCompleteQuestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcCompleteQuestResponse) ProtoMessage() {}

func (x *RpcCompleteQuestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcCompleteQuestResponse.ProtoReflect.Descriptor instead.
func (*RpcCompleteQuestResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{5}
}

func (x *RpcCompleteQuestResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcCompleteQuestResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 领取任务奖励
type RpcClaimQuestRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId int32 `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
}

func (x *RpcClaimQuestRewardRequest) Reset() {
	*x = RpcClaimQuestRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcClaimQuestRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcClaimQuestRewardRequest) ProtoMessage() {}

func (x *RpcClaimQuestRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcClaimQuestRewardRequest.ProtoReflect.Descriptor instead.
func (*RpcClaimQuestRewardRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{6}
}

func (x *RpcClaimQuestRewardRequest) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

type RpcClaimQuestRewardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *RpcClaimQuestRewardResponse) Reset() {
	*x = RpcClaimQuestRewardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcClaimQuestRewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcClaimQuestRewardResponse) ProtoMessage() {}

func (x *RpcClaimQuestRewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcClaimQuestRewardResponse.ProtoReflect.Descriptor instead.
func (*RpcClaimQuestRewardResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{7}
}

func (x *RpcClaimQuestRewardResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcClaimQuestRewardResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取可用任务列表
type RpcGetAvailableQuestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestType QuestType `protobuf:"varint,1,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"` // 可选，筛选特定类型的任务
}

func (x *RpcGetAvailableQuestsRequest) Reset() {
	*x = RpcGetAvailableQuestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetAvailableQuestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAvailableQuestsRequest) ProtoMessage() {}

func (x *RpcGetAvailableQuestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAvailableQuestsRequest.ProtoReflect.Descriptor instead.
func (*RpcGetAvailableQuestsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{8}
}

func (x *RpcGetAvailableQuestsRequest) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

type RpcGetAvailableQuestsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool         `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string       `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Quests  []*QuestInfo `protobuf:"bytes,3,rep,name=quests,proto3" json:"quests,omitempty"`
}

func (x *RpcGetAvailableQuestsResponse) Reset() {
	*x = RpcGetAvailableQuestsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetAvailableQuestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAvailableQuestsResponse) ProtoMessage() {}

func (x *RpcGetAvailableQuestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAvailableQuestsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetAvailableQuestsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{9}
}

func (x *RpcGetAvailableQuestsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcGetAvailableQuestsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcGetAvailableQuestsResponse) GetQuests() []*QuestInfo {
	if x != nil {
		return x.Quests
	}
	return nil
}

// 获取训练师任务列表
type RpcGetTrainerQuestsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success       bool            `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string          `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TrainerQuests []*TrainerQuest `protobuf:"bytes,3,rep,name=trainer_quests,json=trainerQuests,proto3" json:"trainer_quests,omitempty"`
}

func (x *RpcGetTrainerQuestsResponse) Reset() {
	*x = RpcGetTrainerQuestsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcGetTrainerQuestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerQuestsResponse) ProtoMessage() {}

func (x *RpcGetTrainerQuestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerQuestsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerQuestsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{10}
}

func (x *RpcGetTrainerQuestsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcGetTrainerQuestsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcGetTrainerQuestsResponse) GetTrainerQuests() []*TrainerQuest {
	if x != nil {
		return x.TrainerQuests
	}
	return nil
}

// 更新任务进度
type RpcUpdateQuestProgressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestId  int32 `protobuf:"varint,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`
	Progress int32 `protobuf:"varint,2,opt,name=progress,proto3" json:"progress,omitempty"`
}

func (x *RpcUpdateQuestProgressRequest) Reset() {
	*x = RpcUpdateQuestProgressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcUpdateQuestProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateQuestProgressRequest) ProtoMessage() {}

func (x *RpcUpdateQuestProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateQuestProgressRequest.ProtoReflect.Descriptor instead.
func (*RpcUpdateQuestProgressRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{11}
}

func (x *RpcUpdateQuestProgressRequest) GetQuestId() int32 {
	if x != nil {
		return x.QuestId
	}
	return 0
}

func (x *RpcUpdateQuestProgressRequest) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

type RpcUpdateQuestProgressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *RpcUpdateQuestProgressResponse) Reset() {
	*x = RpcUpdateQuestProgressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerQuest_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcUpdateQuestProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateQuestProgressResponse) ProtoMessage() {}

func (x *RpcUpdateQuestProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerQuest_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateQuestProgressResponse.ProtoReflect.Descriptor instead.
func (*RpcUpdateQuestProgressResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerQuest_proto_rawDescGZIP(), []int{12}
}

func (x *RpcUpdateQuestProgressResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcUpdateQuestProgressResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_TrainerQuest_proto protoreflect.FileDescriptor

var file_MainServer_TrainerQuest_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1a, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf1, 0x03, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x34, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a,
	0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a,
	0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0xb8, 0x03, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x76, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3e, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x48, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x73, 0x74, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x40, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x32, 0x0a, 0x15, 0x52, 0x70, 0x63, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x16, 0x52, 0x70, 0x63, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x34, 0x0a, 0x17, 0x52, 0x70, 0x63, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x18, 0x52, 0x70,
	0x63, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x37, 0x0a, 0x1a, 0x52, 0x70,
	0x63, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x1b, 0x52, 0x70, 0x63, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x54, 0x0a, 0x1c, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x82, 0x01, 0x0a,
	0x1d, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x22, 0x92, 0x01, 0x0a, 0x1b, 0x52, 0x70, 0x63, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0x56, 0x0a, 0x1d, 0x52, 0x70, 0x63, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x54,
	0x0a, 0x1e, 0x52, 0x70, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2a, 0x8a, 0x02, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x54,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x6e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10,
	0x07, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerQuest_proto_rawDescOnce sync.Once
	file_MainServer_TrainerQuest_proto_rawDescData = file_MainServer_TrainerQuest_proto_rawDesc
)

func file_MainServer_TrainerQuest_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerQuest_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerQuest_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerQuest_proto_rawDescData)
	})
	return file_MainServer_TrainerQuest_proto_rawDescData
}

var file_MainServer_TrainerQuest_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerQuest_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_MainServer_TrainerQuest_proto_goTypes = []any{
	(TrainerQuestStatus)(0),                // 0: MainServer.TrainerQuestStatus
	(*TrainerQuest)(nil),                   // 1: MainServer.TrainerQuest
	(*TrainerQuestCurrentInfo)(nil),        // 2: MainServer.TrainerQuestCurrentInfo
	(*RpcAcceptQuestRequest)(nil),          // 3: MainServer.RpcAcceptQuestRequest
	(*RpcAcceptQuestResponse)(nil),         // 4: MainServer.RpcAcceptQuestResponse
	(*RpcCompleteQuestRequest)(nil),        // 5: MainServer.RpcCompleteQuestRequest
	(*RpcCompleteQuestResponse)(nil),       // 6: MainServer.RpcCompleteQuestResponse
	(*RpcClaimQuestRewardRequest)(nil),     // 7: MainServer.RpcClaimQuestRewardRequest
	(*RpcClaimQuestRewardResponse)(nil),    // 8: MainServer.RpcClaimQuestRewardResponse
	(*RpcGetAvailableQuestsRequest)(nil),   // 9: MainServer.RpcGetAvailableQuestsRequest
	(*RpcGetAvailableQuestsResponse)(nil),  // 10: MainServer.RpcGetAvailableQuestsResponse
	(*RpcGetTrainerQuestsResponse)(nil),    // 11: MainServer.RpcGetTrainerQuestsResponse
	(*RpcUpdateQuestProgressRequest)(nil),  // 12: MainServer.RpcUpdateQuestProgressRequest
	(*RpcUpdateQuestProgressResponse)(nil), // 13: MainServer.RpcUpdateQuestProgressResponse
	nil,                                    // 14: MainServer.TrainerQuestCurrentInfo.QuestDefaultConditionEntry
	nil,                                    // 15: MainServer.TrainerQuestCurrentInfo.QuestProgressEntry
	(QuestType)(0),                         // 16: MainServer.QuestType
	(*QuestInfo)(nil),                      // 17: MainServer.QuestInfo
}
var file_MainServer_TrainerQuest_proto_depIdxs = []int32{
	16, // 0: MainServer.TrainerQuest.quest_type:type_name -> MainServer.QuestType
	0,  // 1: MainServer.TrainerQuest.quest_status:type_name -> MainServer.TrainerQuestStatus
	2,  // 2: MainServer.TrainerQuest.quest_current_info:type_name -> MainServer.TrainerQuestCurrentInfo
	17, // 3: MainServer.TrainerQuest.quest_info:type_name -> MainServer.QuestInfo
	17, // 4: MainServer.TrainerQuestCurrentInfo.current_quest:type_name -> MainServer.QuestInfo
	14, // 5: MainServer.TrainerQuestCurrentInfo.quest_default_condition:type_name -> MainServer.TrainerQuestCurrentInfo.QuestDefaultConditionEntry
	15, // 6: MainServer.TrainerQuestCurrentInfo.quest_progress:type_name -> MainServer.TrainerQuestCurrentInfo.QuestProgressEntry
	16, // 7: MainServer.RpcGetAvailableQuestsRequest.quest_type:type_name -> MainServer.QuestType
	17, // 8: MainServer.RpcGetAvailableQuestsResponse.quests:type_name -> MainServer.QuestInfo
	1,  // 9: MainServer.RpcGetTrainerQuestsResponse.trainer_quests:type_name -> MainServer.TrainerQuest
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerQuest_proto_init() }
func file_MainServer_TrainerQuest_proto_init() {
	if File_MainServer_TrainerQuest_proto != nil {
		return
	}
	file_MainServer_QuestType_proto_init()
	file_MainServer_QuestInfo_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerQuest_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerQuest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerQuestCurrentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RpcAcceptQuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RpcAcceptQuestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RpcCompleteQuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*RpcCompleteQuestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RpcClaimQuestRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RpcClaimQuestRewardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetAvailableQuestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetAvailableQuestsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*RpcGetTrainerQuestsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*RpcUpdateQuestProgressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerQuest_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*RpcUpdateQuestProgressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerQuest_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerQuest_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerQuest_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerQuest_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerQuest_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerQuest_proto = out.File
	file_MainServer_TrainerQuest_proto_rawDesc = nil
	file_MainServer_TrainerQuest_proto_goTypes = nil
	file_MainServer_TrainerQuest_proto_depIdxs = nil
}
