// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerStrict.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerStrictSource int32

const (
	TrainerStrictSource_TrainerStrictSource_None                TrainerStrictSource = 0
	TrainerStrictSource_TrainerStrictSource_Quest               TrainerStrictSource = 1
	TrainerStrictSource_TrainerStrictSource_Battle_Force_Invite TrainerStrictSource = 2 //战斗强制邀请
)

// Enum value maps for TrainerStrictSource.
var (
	TrainerStrictSource_name = map[int32]string{
		0: "TrainerStrictSource_None",
		1: "TrainerStrictSource_Quest",
		2: "TrainerStrictSource_Battle_Force_Invite",
	}
	TrainerStrictSource_value = map[string]int32{
		"TrainerStrictSource_None":                0,
		"TrainerStrictSource_Quest":               1,
		"TrainerStrictSource_Battle_Force_Invite": 2,
	}
)

func (x TrainerStrictSource) Enum() *TrainerStrictSource {
	p := new(TrainerStrictSource)
	*p = x
	return p
}

func (x TrainerStrictSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerStrictSource) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerStrict_proto_enumTypes[0].Descriptor()
}

func (TrainerStrictSource) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerStrict_proto_enumTypes[0]
}

func (x TrainerStrictSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerStrictSource.Descriptor instead.
func (TrainerStrictSource) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{0}
}

type TrainerStrictType int32

const (
	TrainerStrictType_TrainerStrictType_None                  TrainerStrictType = 0
	TrainerStrictType_TrainerStrictType_CantFly               TrainerStrictType = 1 //不能飞行
	TrainerStrictType_TrainerStrictType_CantRide              TrainerStrictType = 2 //不能乘骑
	TrainerStrictType_TrainerStrictType_CantCatch             TrainerStrictType = 3 //不能捕捉
	TrainerStrictType_TrainerStrictType_CantBattle            TrainerStrictType = 4 //不能战斗
	TrainerStrictType_TrainerStrictType_CantMove              TrainerStrictType = 5 //不能移动
	TrainerStrictType_TrainerStrictType_CantRun               TrainerStrictType = 6 //不能跑
	TrainerStrictType_TrainerStrictType_CantUpdateAroundPokes TrainerStrictType = 7 //不能更新周围宝可梦
	TrainerStrictType_TrainerStrictType_FirstPoke             TrainerStrictType = 8 //第一个宝可梦
)

// Enum value maps for TrainerStrictType.
var (
	TrainerStrictType_name = map[int32]string{
		0: "TrainerStrictType_None",
		1: "TrainerStrictType_CantFly",
		2: "TrainerStrictType_CantRide",
		3: "TrainerStrictType_CantCatch",
		4: "TrainerStrictType_CantBattle",
		5: "TrainerStrictType_CantMove",
		6: "TrainerStrictType_CantRun",
		7: "TrainerStrictType_CantUpdateAroundPokes",
		8: "TrainerStrictType_FirstPoke",
	}
	TrainerStrictType_value = map[string]int32{
		"TrainerStrictType_None":                  0,
		"TrainerStrictType_CantFly":               1,
		"TrainerStrictType_CantRide":              2,
		"TrainerStrictType_CantCatch":             3,
		"TrainerStrictType_CantBattle":            4,
		"TrainerStrictType_CantMove":              5,
		"TrainerStrictType_CantRun":               6,
		"TrainerStrictType_CantUpdateAroundPokes": 7,
		"TrainerStrictType_FirstPoke":             8,
	}
)

func (x TrainerStrictType) Enum() *TrainerStrictType {
	p := new(TrainerStrictType)
	*p = x
	return p
}

func (x TrainerStrictType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerStrictType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerStrict_proto_enumTypes[1].Descriptor()
}

func (TrainerStrictType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerStrict_proto_enumTypes[1]
}

func (x TrainerStrictType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerStrictType.Descriptor instead.
func (TrainerStrictType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{1}
}

type TrainerStrictInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// int64 source_id = 2;
	Stricts []*TrainerStrict `protobuf:"bytes,3,rep,name=stricts,proto3" json:"stricts,omitempty"`
}

func (x *TrainerStrictInfo) Reset() {
	*x = TrainerStrictInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerStrict_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerStrictInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerStrictInfo) ProtoMessage() {}

func (x *TrainerStrictInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerStrictInfo.ProtoReflect.Descriptor instead.
func (*TrainerStrictInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerStrictInfo) GetStricts() []*TrainerStrict {
	if x != nil {
		return x.Stricts
	}
	return nil
}

type TrainerStrict struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source         TrainerStrictSource `protobuf:"varint,1,opt,name=source,proto3,enum=MainServer.TrainerStrictSource" json:"source,omitempty"`
	StrictType     TrainerStrictType   `protobuf:"varint,2,opt,name=strict_type,json=strictType,proto3,enum=MainServer.TrainerStrictType" json:"strict_type,omitempty"`
	StrictIntValue int64               `protobuf:"varint,3,opt,name=strict_int_value,json=strictIntValue,proto3" json:"strict_int_value,omitempty"`
	StrictStrValue string              `protobuf:"bytes,4,opt,name=strict_str_value,json=strictStrValue,proto3" json:"strict_str_value,omitempty"`
}

func (x *TrainerStrict) Reset() {
	*x = TrainerStrict{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerStrict_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerStrict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerStrict) ProtoMessage() {}

func (x *TrainerStrict) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerStrict.ProtoReflect.Descriptor instead.
func (*TrainerStrict) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerStrict) GetSource() TrainerStrictSource {
	if x != nil {
		return x.Source
	}
	return TrainerStrictSource_TrainerStrictSource_None
}

func (x *TrainerStrict) GetStrictType() TrainerStrictType {
	if x != nil {
		return x.StrictType
	}
	return TrainerStrictType_TrainerStrictType_None
}

func (x *TrainerStrict) GetStrictIntValue() int64 {
	if x != nil {
		return x.StrictIntValue
	}
	return 0
}

func (x *TrainerStrict) GetStrictStrValue() string {
	if x != nil {
		return x.StrictStrValue
	}
	return ""
}

var File_MainServer_TrainerStrict_proto protoreflect.FileDescriptor

var file_MainServer_TrainerStrict_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x11,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x52, 0x07, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x73, 0x22, 0xdc, 0x01, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x49, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x53, 0x74, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x7f, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x51, 0x75, 0x65, 0x73, 0x74, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x10, 0x02, 0x2a, 0xbe, 0x02, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61,
	0x6e, 0x74, 0x46, 0x6c, 0x79, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x6e,
	0x74, 0x52, 0x69, 0x64, 0x65, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x72, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x6e,
	0x74, 0x43, 0x61, 0x74, 0x63, 0x68, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61,
	0x6e, 0x74, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x43, 0x61, 0x6e, 0x74, 0x4d, 0x6f, 0x76, 0x65, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x43, 0x61, 0x6e, 0x74, 0x52, 0x75, 0x6e, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43,
	0x61, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x50,
	0x6f, 0x6b, 0x65, 0x73, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x53, 0x74, 0x72, 0x69, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x69, 0x72, 0x73,
	0x74, 0x50, 0x6f, 0x6b, 0x65, 0x10, 0x08, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerStrict_proto_rawDescOnce sync.Once
	file_MainServer_TrainerStrict_proto_rawDescData = file_MainServer_TrainerStrict_proto_rawDesc
)

func file_MainServer_TrainerStrict_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerStrict_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerStrict_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerStrict_proto_rawDescData)
	})
	return file_MainServer_TrainerStrict_proto_rawDescData
}

var file_MainServer_TrainerStrict_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_TrainerStrict_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerStrict_proto_goTypes = []any{
	(TrainerStrictSource)(0),  // 0: MainServer.TrainerStrictSource
	(TrainerStrictType)(0),    // 1: MainServer.TrainerStrictType
	(*TrainerStrictInfo)(nil), // 2: MainServer.TrainerStrictInfo
	(*TrainerStrict)(nil),     // 3: MainServer.TrainerStrict
}
var file_MainServer_TrainerStrict_proto_depIdxs = []int32{
	3, // 0: MainServer.TrainerStrictInfo.stricts:type_name -> MainServer.TrainerStrict
	0, // 1: MainServer.TrainerStrict.source:type_name -> MainServer.TrainerStrictSource
	1, // 2: MainServer.TrainerStrict.strict_type:type_name -> MainServer.TrainerStrictType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerStrict_proto_init() }
func file_MainServer_TrainerStrict_proto_init() {
	if File_MainServer_TrainerStrict_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerStrict_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerStrictInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerStrict_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerStrict); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerStrict_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerStrict_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerStrict_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerStrict_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerStrict_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerStrict_proto = out.File
	file_MainServer_TrainerStrict_proto_rawDesc = nil
	file_MainServer_TrainerStrict_proto_goTypes = nil
	file_MainServer_TrainerStrict_proto_depIdxs = nil
}
