// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTeam int32

const (
	TrainerTeam_TRAINER_TEAM_NONE     TrainerTeam = 0
	TrainerTeam_TRAINER_TEAM_Rocket   TrainerTeam = 1  //火箭队
	TrainerTeam_TRAINER_TEAM_Magma    TrainerTeam = 2  //熔岩队
	TrainerTeam_TRAINER_TEAM_Aqua     TrainerTeam = 3  //海洋队
	TrainerTeam_TRAINER_TEAM_Galactic TrainerTeam = 5  //银河队
	TrainerTeam_TRAINER_TEAM_Plasma   TrainerTeam = 6  //等离子队
	TrainerTeam_TRAINER_TEAM_Flare    TrainerTeam = 7  //闪焰队
	TrainerTeam_TRAINER_TEAM_Skull    TrainerTeam = 8  //骷髅队
	TrainerTeam_TRAINER_TEAM_Yell     TrainerTeam = 9  //呐喊队
	TrainerTeam_TRAINER_TEAM_Star     TrainerTeam = 10 //天星队
)

// Enum value maps for TrainerTeam.
var (
	TrainerTeam_name = map[int32]string{
		0:  "TRAINER_TEAM_NONE",
		1:  "TRAINER_TEAM_Rocket",
		2:  "TRAINER_TEAM_Magma",
		3:  "TRAINER_TEAM_Aqua",
		5:  "TRAINER_TEAM_Galactic",
		6:  "TRAINER_TEAM_Plasma",
		7:  "TRAINER_TEAM_Flare",
		8:  "TRAINER_TEAM_Skull",
		9:  "TRAINER_TEAM_Yell",
		10: "TRAINER_TEAM_Star",
	}
	TrainerTeam_value = map[string]int32{
		"TRAINER_TEAM_NONE":     0,
		"TRAINER_TEAM_Rocket":   1,
		"TRAINER_TEAM_Magma":    2,
		"TRAINER_TEAM_Aqua":     3,
		"TRAINER_TEAM_Galactic": 5,
		"TRAINER_TEAM_Plasma":   6,
		"TRAINER_TEAM_Flare":    7,
		"TRAINER_TEAM_Skull":    8,
		"TRAINER_TEAM_Yell":     9,
		"TRAINER_TEAM_Star":     10,
	}
)

func (x TrainerTeam) Enum() *TrainerTeam {
	p := new(TrainerTeam)
	*p = x
	return p
}

func (x TrainerTeam) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTeam) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeam_proto_enumTypes[0].Descriptor()
}

func (TrainerTeam) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeam_proto_enumTypes[0]
}

func (x TrainerTeam) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTeam.Descriptor instead.
func (TrainerTeam) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

type TrainerOnTeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contribution int64 `protobuf:"varint,1,opt,name=contribution,proto3" json:"contribution,omitempty"` //阵营贡献
	Level        int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *TrainerOnTeamInfo) Reset() {
	*x = TrainerOnTeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerOnTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerOnTeamInfo) ProtoMessage() {}

func (x *TrainerOnTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerOnTeamInfo.ProtoReflect.Descriptor instead.
func (*TrainerOnTeamInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerOnTeamInfo) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *TrainerOnTeamInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type RpcAddTrainerTeamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type TrainerTeam `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerTeam" json:"type,omitempty"` //要加入的组织
}

func (x *RpcAddTrainerTeamRequest) Reset() {
	*x = RpcAddTrainerTeamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpcAddTrainerTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerTeamRequest) ProtoMessage() {}

func (x *RpcAddTrainerTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeam_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerTeamRequest.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerTeamRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeam_proto_rawDescGZIP(), []int{1}
}

func (x *RpcAddTrainerTeamRequest) GetType() TrainerTeam {
	if x != nil {
		return x.Type
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

var File_MainServer_TrainerTeam_proto protoreflect.FileDescriptor

var file_MainServer_TrainerTeam_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x4d, 0x0a, 0x11, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x4f, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x47, 0x0a, 0x18, 0x52, 0x70, 0x63,
	0x41, 0x64, 0x64, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x2a, 0xfe, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65,
	0x61, 0x6d, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45,
	0x41, 0x4d, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41,
	0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x52, 0x6f, 0x63, 0x6b, 0x65, 0x74,
	0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45,
	0x41, 0x4d, 0x5f, 0x4d, 0x61, 0x67, 0x6d, 0x61, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52,
	0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x41, 0x71, 0x75, 0x61, 0x10,
	0x03, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41,
	0x4d, 0x5f, 0x47, 0x61, 0x6c, 0x61, 0x63, 0x74, 0x69, 0x63, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13,
	0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x50, 0x6c, 0x61,
	0x73, 0x6d, 0x61, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52,
	0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x46, 0x6c, 0x61, 0x72, 0x65, 0x10, 0x07, 0x12, 0x16, 0x0a,
	0x12, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x6b,
	0x75, 0x6c, 0x6c, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52,
	0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x59, 0x65, 0x6c, 0x6c, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11,
	0x54, 0x52, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x74, 0x61,
	0x72, 0x10, 0x0a, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerTeam_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTeam_proto_rawDescData = file_MainServer_TrainerTeam_proto_rawDesc
)

func file_MainServer_TrainerTeam_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTeam_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerTeam_proto_rawDescData)
	})
	return file_MainServer_TrainerTeam_proto_rawDescData
}

var file_MainServer_TrainerTeam_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerTeam_proto_goTypes = []any{
	(TrainerTeam)(0),                 // 0: MainServer.TrainerTeam
	(*TrainerOnTeamInfo)(nil),        // 1: MainServer.TrainerOnTeamInfo
	(*RpcAddTrainerTeamRequest)(nil), // 2: MainServer.RpcAddTrainerTeamRequest
}
var file_MainServer_TrainerTeam_proto_depIdxs = []int32{
	0, // 0: MainServer.RpcAddTrainerTeamRequest.type:type_name -> MainServer.TrainerTeam
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTeam_proto_init() }
func file_MainServer_TrainerTeam_proto_init() {
	if File_MainServer_TrainerTeam_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerTeam_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerOnTeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_MainServer_TrainerTeam_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RpcAddTrainerTeamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerTeam_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTeam_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTeam_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTeam_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTeam_proto = out.File
	file_MainServer_TrainerTeam_proto_rawDesc = nil
	file_MainServer_TrainerTeam_proto_goTypes = nil
	file_MainServer_TrainerTeam_proto_depIdxs = nil
}
