// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: MainServer/TrainerTeamNPC.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTeamNpcId int32

const (
	TrainerTeamNpcId_TEAM_NPC_NONE           TrainerTeamNpcId = 0
	TrainerTeamNpcId_TEAM_NPC_Boss           TrainerTeamNpcId = 1
	TrainerTeamNpcId_TEAM_NPC_receptionist_1 TrainerTeamNpcId = 11 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_2 TrainerTeamNpcId = 12 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_3 TrainerTeamNpcId = 13 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_4 TrainerTeamNpcId = 14 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_5 TrainerTeamNpcId = 15 //接待员
	TrainerTeamNpcId_TEAM_NPC_1              TrainerTeamNpcId = 101
	TrainerTeamNpcId_TEAM_NPC_2              TrainerTeamNpcId = 102
	TrainerTeamNpcId_TEAM_NPC_3              TrainerTeamNpcId = 103
	TrainerTeamNpcId_TEAM_NPC_4              TrainerTeamNpcId = 104
	TrainerTeamNpcId_TEAM_NPC_5              TrainerTeamNpcId = 105
	TrainerTeamNpcId_TEAM_NPC_6              TrainerTeamNpcId = 106
	TrainerTeamNpcId_TEAM_NPC_7              TrainerTeamNpcId = 107
	TrainerTeamNpcId_TEAM_NPC_8              TrainerTeamNpcId = 108
	TrainerTeamNpcId_TEAM_NPC_9              TrainerTeamNpcId = 109
	TrainerTeamNpcId_TEAM_NPC_10             TrainerTeamNpcId = 110
)

// Enum value maps for TrainerTeamNpcId.
var (
	TrainerTeamNpcId_name = map[int32]string{
		0:   "TEAM_NPC_NONE",
		1:   "TEAM_NPC_Boss",
		11:  "TEAM_NPC_receptionist_1",
		12:  "TEAM_NPC_receptionist_2",
		13:  "TEAM_NPC_receptionist_3",
		14:  "TEAM_NPC_receptionist_4",
		15:  "TEAM_NPC_receptionist_5",
		101: "TEAM_NPC_1",
		102: "TEAM_NPC_2",
		103: "TEAM_NPC_3",
		104: "TEAM_NPC_4",
		105: "TEAM_NPC_5",
		106: "TEAM_NPC_6",
		107: "TEAM_NPC_7",
		108: "TEAM_NPC_8",
		109: "TEAM_NPC_9",
		110: "TEAM_NPC_10",
	}
	TrainerTeamNpcId_value = map[string]int32{
		"TEAM_NPC_NONE":           0,
		"TEAM_NPC_Boss":           1,
		"TEAM_NPC_receptionist_1": 11,
		"TEAM_NPC_receptionist_2": 12,
		"TEAM_NPC_receptionist_3": 13,
		"TEAM_NPC_receptionist_4": 14,
		"TEAM_NPC_receptionist_5": 15,
		"TEAM_NPC_1":              101,
		"TEAM_NPC_2":              102,
		"TEAM_NPC_3":              103,
		"TEAM_NPC_4":              104,
		"TEAM_NPC_5":              105,
		"TEAM_NPC_6":              106,
		"TEAM_NPC_7":              107,
		"TEAM_NPC_8":              108,
		"TEAM_NPC_9":              109,
		"TEAM_NPC_10":             110,
	}
)

func (x TrainerTeamNpcId) Enum() *TrainerTeamNpcId {
	p := new(TrainerTeamNpcId)
	*p = x
	return p
}

func (x TrainerTeamNpcId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTeamNpcId) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeamNPC_proto_enumTypes[0].Descriptor()
}

func (TrainerTeamNpcId) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeamNPC_proto_enumTypes[0]
}

func (x TrainerTeamNpcId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTeamNpcId.Descriptor instead.
func (TrainerTeamNpcId) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeamNPC_proto_rawDescGZIP(), []int{0}
}

type TrainerTeamNpcInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     TrainerTeamNpcId `protobuf:"varint,1,opt,name=id,proto3,enum=MainServer.TrainerTeamNpcId" json:"id,omitempty"`
	Config *NpcRoleConfig   `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *TrainerTeamNpcInfo) Reset() {
	*x = TrainerTeamNpcInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_MainServer_TrainerTeamNPC_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainerTeamNpcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTeamNpcInfo) ProtoMessage() {}

func (x *TrainerTeamNpcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeamNPC_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTeamNpcInfo.ProtoReflect.Descriptor instead.
func (*TrainerTeamNpcInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeamNPC_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerTeamNpcInfo) GetId() TrainerTeamNpcId {
	if x != nil {
		return x.Id
	}
	return TrainerTeamNpcId_TEAM_NPC_NONE
}

func (x *TrainerTeamNpcInfo) GetConfig() *NpcRoleConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_MainServer_TrainerTeamNPC_proto protoreflect.FileDescriptor

var file_MainServer_TrainerTeamNPC_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x4e, 0x50, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0a, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x15, 0x4d,
	0x61, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x52, 0x6f, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x75, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54,
	0x65, 0x61, 0x6d, 0x4e, 0x70, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x4e,
	0x70, 0x63, 0x49, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x70, 0x63, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2a, 0xea, 0x02, 0x0a, 0x10,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x4e, 0x70, 0x63, 0x49, 0x64,
	0x12, 0x11, 0x0a, 0x0d, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f,
	0x42, 0x6f, 0x73, 0x73, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e,
	0x50, 0x43, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x69, 0x73, 0x74, 0x5f,
	0x31, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f,
	0x72, 0x65, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x69, 0x73, 0x74, 0x5f, 0x32, 0x10, 0x0c,
	0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x69, 0x73, 0x74, 0x5f, 0x33, 0x10, 0x0d, 0x12, 0x1b, 0x0a,
	0x17, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x69, 0x73, 0x74, 0x5f, 0x34, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x45,
	0x41, 0x4d, 0x5f, 0x4e, 0x50, 0x43, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x69, 0x73, 0x74, 0x5f, 0x35, 0x10, 0x0f, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x31, 0x10, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x32, 0x10, 0x66, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x33, 0x10, 0x67, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x34, 0x10, 0x68, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x35, 0x10, 0x69, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x36, 0x10, 0x6a, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x37, 0x10, 0x6b, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x38, 0x10, 0x6c, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x39, 0x10, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x45, 0x41, 0x4d, 0x5f,
	0x4e, 0x50, 0x43, 0x5f, 0x31, 0x30, 0x10, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_MainServer_TrainerTeamNPC_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTeamNPC_proto_rawDescData = file_MainServer_TrainerTeamNPC_proto_rawDesc
)

func file_MainServer_TrainerTeamNPC_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTeamNPC_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTeamNPC_proto_rawDescData = protoimpl.X.CompressGZIP(file_MainServer_TrainerTeamNPC_proto_rawDescData)
	})
	return file_MainServer_TrainerTeamNPC_proto_rawDescData
}

var file_MainServer_TrainerTeamNPC_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerTeamNPC_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_TrainerTeamNPC_proto_goTypes = []any{
	(TrainerTeamNpcId)(0),      // 0: MainServer.TrainerTeamNpcId
	(*TrainerTeamNpcInfo)(nil), // 1: MainServer.TrainerTeamNpcInfo
	(*NpcRoleConfig)(nil),      // 2: MainServer.NpcRoleConfig
}
var file_MainServer_TrainerTeamNPC_proto_depIdxs = []int32{
	0, // 0: MainServer.TrainerTeamNpcInfo.id:type_name -> MainServer.TrainerTeamNpcId
	2, // 1: MainServer.TrainerTeamNpcInfo.config:type_name -> MainServer.NpcRoleConfig
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTeamNPC_proto_init() }
func file_MainServer_TrainerTeamNPC_proto_init() {
	if File_MainServer_TrainerTeamNPC_proto != nil {
		return
	}
	file_MainServer_Role_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_MainServer_TrainerTeamNPC_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TrainerTeamNpcInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_MainServer_TrainerTeamNPC_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTeamNPC_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTeamNPC_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTeamNPC_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTeamNPC_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTeamNPC_proto = out.File
	file_MainServer_TrainerTeamNPC_proto_rawDesc = nil
	file_MainServer_TrainerTeamNPC_proto_goTypes = nil
	file_MainServer_TrainerTeamNPC_proto_depIdxs = nil
}
