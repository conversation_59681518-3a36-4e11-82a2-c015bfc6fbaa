package quest

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableQuest = "quest"
const TableTrainerQuest = "trainer_quest"

// InitQuest 初始化任务系统
func InitQuest(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 创建任务表
	err := createQuestTable(ctx, logger, db)
	if err != nil {
		return err
	}

	// 创建训练师任务表
	err = createTrainerQuestTable(ctx, logger, db)
	if err != nil {
		return err
	}

	logger.Info("Quest system initialized successfully")
	return nil
}

// createQuestTable 创建任务表
func createQuestTable(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			quest_id SERIAL PRIMARY KEY,
			quest_type INTEGER NOT NULL DEFAULT 0,
			quest_level INTEGER NOT NULL DEFAULT 1,
			quest_status INTEGER NOT NULL DEFAULT 0,
			quest_unlock_id INTEGER NOT NULL DEFAULT 0,
			linear_quest_ids JSONB DEFAULT '[]',
			single_quest BOOLEAN NOT NULL DEFAULT true,
			current_quest_ids JSONB DEFAULT '[]',
			quest_reward_id INTEGER NOT NULL DEFAULT 0,
			quest_start_time BIGINT NOT NULL DEFAULT 0,
			quest_end_time BIGINT NOT NULL DEFAULT 0,
			quest_repeat_limit INTEGER NOT NULL DEFAULT 1,
			quest_repeat_interval INTEGER NOT NULL DEFAULT 0,
			quest_broadcast JSONB DEFAULT '{}',
			quest_complete_id INTEGER NOT NULL DEFAULT 0,
			quest_repeat_limit_time INTEGER NOT NULL DEFAULT 0,
			create_ts BIGINT NOT NULL DEFAULT 0,
			update_ts BIGINT NOT NULL DEFAULT 0
		)
	`, TableQuest)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("Failed to create quest table: %v", err)
		return err
	}

	// 创建索引
	indexQueries := []string{
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_type ON %s (quest_type)", TableQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_status ON %s (quest_status)", TableQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_quest_time ON %s (quest_start_time, quest_end_time)", TableQuest),
	}

	for _, indexQuery := range indexQueries {
		_, err = db.ExecContext(ctx, indexQuery)
		if err != nil {
			logger.Error("Failed to create quest index: %v", err)
			return err
		}
	}

	return nil
}

// createTrainerQuestTable 创建训练师任务表
func createTrainerQuestTable(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id BIGSERIAL PRIMARY KEY,
			tid BIGINT NOT NULL,
			quest_id BIGINT NOT NULL,
			quest_type INTEGER NOT NULL,
			quest_status INTEGER NOT NULL DEFAULT 1,
			quest_progress INTEGER NOT NULL DEFAULT 0,
			quest_start_time BIGINT NOT NULL DEFAULT 0,
			quest_end_time BIGINT NOT NULL DEFAULT 0,
			quest_repeat_limit_time INTEGER NOT NULL DEFAULT 0,
			create_ts BIGINT NOT NULL DEFAULT 0,
			update_ts BIGINT NOT NULL DEFAULT 0,
			UNIQUE(tid, quest_id)
		)
	`, TableTrainerQuest)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("Failed to create trainer_quest table: %v", err)
		return err
	}

	// 创建索引
	indexQueries := []string{
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid ON %s (tid)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_status ON %s (quest_status)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_time ON %s (accept_time, complete_time)", TableTrainerQuest),
	}

	for _, indexQuery := range indexQueries {
		_, err = db.ExecContext(ctx, indexQuery)
		if err != nil {
			logger.Error("Failed to create trainer_quest index: %v", err)
			return err
		}
	}

	return nil
}

// AddQuest 添加任务到数据库
func AddQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, quest *MainServer.QuestInfo) error {
	nowTs := time.Now().Unix()

	// 序列化JSONB字段
	linearQuestIds, _ := json.Marshal(quest.LinearQuestIds)
	currentQuestIds, _ := json.Marshal(quest.CurrentQuestIds)
	questBroadcast, _ := json.Marshal(quest.QuestBroadcast)

	query := fmt.Sprintf(`
		INSERT INTO %s (
			quest_id, quest_type, quest_level, quest_status, quest_unlock_id,
			linear_quest_ids, single_quest, current_quest_ids, quest_reward_id,
			quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			quest_broadcast, quest_complete_id, quest_repeat_limit_time,
			create_ts, update_ts
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
		ON CONFLICT (quest_id) DO UPDATE SET
			quest_type = EXCLUDED.quest_type,
			quest_level = EXCLUDED.quest_level,
			quest_status = EXCLUDED.quest_status,
			quest_unlock_id = EXCLUDED.quest_unlock_id,
			linear_quest_ids = EXCLUDED.linear_quest_ids,
			single_quest = EXCLUDED.single_quest,
			current_quest_ids = EXCLUDED.current_quest_ids,
			quest_reward_id = EXCLUDED.quest_reward_id,
			quest_start_time = EXCLUDED.quest_start_time,
			quest_end_time = EXCLUDED.quest_end_time,
			quest_repeat_limit = EXCLUDED.quest_repeat_limit,
			quest_repeat_interval = EXCLUDED.quest_repeat_interval,
			quest_broadcast = EXCLUDED.quest_broadcast,
			quest_complete_id = EXCLUDED.quest_complete_id,
			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
			update_ts = $18
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query,
		quest.QuestId, quest.QuestType, quest.QuestLevel, quest.QuestStatus, quest.QuestUnlockId,
		linearQuestIds, quest.SingleQuest, currentQuestIds, quest.QuestRewardId,
		quest.QuestStartTime, quest.QuestEndTime, quest.QuestRepeatLimit, quest.QuestRepeatInterval,
		questBroadcast, quest.QuestCompleteId, quest.QuestRepeatLimitTime,
		nowTs, nowTs,
	)

	if err != nil {
		logger.Error("Failed to add quest: %v", err)
		return err
	}

	return nil
}

// GetQuestById 根据ID获取任务信息
func GetQuestById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32) (*MainServer.QuestInfo, error) {
	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_id,
			   linear_quest_ids, single_quest, current_quest_ids, quest_reward_id,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_id, quest_repeat_limit_time
		FROM %s WHERE quest_id = $1
	`, TableQuest)

	quest := &MainServer.QuestInfo{}
	var linearQuestIdsJSON, currentQuestIdsJSON, questBroadcastJSON []byte
	var questType, questLevel, questStatus int

	err := tx.QueryRowContext(ctx, query, questId).Scan(
		&quest.QuestId, &questType, &questLevel, &questStatus, &quest.QuestUnlockId,
		&linearQuestIdsJSON, &quest.SingleQuest, &currentQuestIdsJSON, &quest.QuestRewardId,
		&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
		&questBroadcastJSON, &quest.QuestCompleteId, &quest.QuestRepeatLimitTime,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("quest not found")
		}
		logger.Error("Failed to get quest: %v", err)
		return nil, err
	}

	// 转换枚举类型
	quest.QuestType = MainServer.QuestType(questType)
	quest.QuestLevel = int32(questLevel)
	quest.QuestStatus = MainServer.QuestStatus(questStatus)

	// 反序列化JSONB字段
	json.Unmarshal(linearQuestIdsJSON, &quest.LinearQuestIds)
	json.Unmarshal(currentQuestIdsJSON, &quest.CurrentQuestIds)
	json.Unmarshal(questBroadcastJSON, &quest.QuestBroadcast)

	return quest, nil
}

// UpdateQuestStatus 更新任务状态
func UpdateQuestStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32, status MainServer.QuestStatus) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_status = $1, update_ts = $2 WHERE quest_id = $3
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query, int(status), nowTs, questId)
	if err != nil {
		logger.Error("Failed to update quest status: %v", err)
		return err
	}

	return nil
}

// GetAvailableQuests 获取可用的任务列表
func GetAvailableQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questType MainServer.QuestType) ([]*MainServer.QuestInfo, error) {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_id,
			   linear_quest_ids, single_quest, current_quest_ids, quest_reward_id,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_id, quest_repeat_limit_time
		FROM %s 
		WHERE quest_status = $1 
		AND (quest_start_time = 0 OR quest_start_time <= $2)
		AND (quest_end_time = 0 OR quest_end_time >= $2)
		AND ($3 = -1 OR quest_type = $3)
		ORDER BY quest_level, quest_id
	`, TableQuest)

	var questTypeFilter int = -1
	if questType != MainServer.QuestType_QuestType_once {
		questTypeFilter = int(questType)
	}

	rows, err := tx.QueryContext(ctx, query, int(MainServer.QuestStatus_QuestStatus_open), nowTs, questTypeFilter)
	if err != nil {
		logger.Error("Failed to get available quests: %v", err)
		return nil, err
	}
	defer rows.Close()

	var quests []*MainServer.QuestInfo
	for rows.Next() {
		quest := &MainServer.QuestInfo{}
		var linearQuestIdsJSON, currentQuestIdsJSON, questBroadcastJSON []byte
		var questTypeInt, questLevelInt, questStatusInt int

		err := rows.Scan(
			&quest.QuestId, &questTypeInt, &questLevelInt, &questStatusInt, &quest.QuestUnlockId,
			&linearQuestIdsJSON, &quest.SingleQuest, &currentQuestIdsJSON, &quest.QuestRewardId,
			&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
			&questBroadcastJSON, &quest.QuestCompleteId, &quest.QuestRepeatLimitTime,
		)
		if err != nil {
			logger.Error("Failed to scan quest: %v", err)
			continue
		}

		// 转换枚举类型
		quest.QuestType = MainServer.QuestType(questTypeInt)
		quest.QuestLevel = int32(questLevelInt)
		quest.QuestStatus = MainServer.QuestStatus(questStatusInt)

		// 反序列化JSONB字段
		json.Unmarshal(linearQuestIdsJSON, &quest.LinearQuestIds)
		json.Unmarshal(currentQuestIdsJSON, &quest.CurrentQuestIds)
		json.Unmarshal(questBroadcastJSON, &quest.QuestBroadcast)

		quests = append(quests, quest)
	}

	return quests, nil
}

// DeleteQuest 删除任务
func DeleteQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32) error {
	query := fmt.Sprintf(`DELETE FROM %s WHERE quest_id = $1`, TableQuest)

	_, err := tx.ExecContext(ctx, query, questId)
	if err != nil {
		logger.Error("Failed to delete quest: %v", err)
		return err
	}

	return nil
}

// UpdateQuestUnlockId 更新任务解锁条件ID
func UpdateQuestUnlockId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32, unlockId int32) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_unlock_id = $1, update_ts = $2 WHERE quest_id = $3
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query, unlockId, nowTs, questId)
	if err != nil {
		logger.Error("Failed to update quest unlock id: %v", err)
		return err
	}

	return nil
}

// UpdateQuestCompleteId 更新任务完成条件ID
func UpdateQuestCompleteId(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32, completeId int32) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_complete_id = $1, update_ts = $2 WHERE quest_id = $3
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query, completeId, nowTs, questId)
	if err != nil {
		logger.Error("Failed to update quest complete id: %v", err)
		return err
	}

	return nil
}

// UpdateQuestReward 更新任务奖励ID
func UpdateQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, questId int32, rewardId int32) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_reward_id = $1, update_ts = $2 WHERE quest_id = $3
	`, TableQuest)

	_, err := tx.ExecContext(ctx, query, rewardId, nowTs, questId)
	if err != nil {
		logger.Error("Failed to update quest reward: %v", err)
		return err
	}

	return nil
}
