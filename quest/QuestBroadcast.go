package quest

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// QuestBroadcastManager 任务广播管理器
type QuestBroadcastManager struct {
	logger runtime.Logger
	nk     runtime.NakamaModule
}

// NewQuestBroadcastManager 创建任务广播管理器
func NewQuestBroadcastManager(logger runtime.Logger, nk runtime.NakamaModule) *QuestBroadcastManager {
	return &QuestBroadcastManager{
		logger: logger,
		nk:     nk,
	}
}

// CheckAndBroadcastQuests 检查并广播任务开始/结束
func (qbm *QuestBroadcastManager) CheckAndBroadcastQuests(ctx context.Context, tx *sql.Tx) error {
	nowTs := time.Now().Unix()

	// 查询需要广播的任务
	quests, err := qbm.getQuestsForBroadcast(ctx, tx, nowTs)
	if err != nil {
		return err
	}

	for _, quest := range quests {
		err = qbm.broadcastQuest(ctx, quest, nowTs)
		if err != nil {
			qbm.logger.Error("Failed to broadcast quest %d: %v", quest.QuestId, err)
			continue
		}

		// 更新任务状态
		if nowTs >= quest.QuestStartTime && quest.QuestStatus == MainServer.QuestStatus_QuestStatus_wait {
			err = UpdateQuestStatus(ctx, qbm.logger, tx, quest.QuestId, MainServer.QuestStatus_QuestStatus_open)
			if err != nil {
				qbm.logger.Error("Failed to update quest status: %v", err)
			}
		}
	}

	return nil
}

// getQuestsForBroadcast 获取需要广播的任务
func (qbm *QuestBroadcastManager) getQuestsForBroadcast(ctx context.Context, tx *sql.Tx, nowTs int64) ([]*MainServer.QuestInfo, error) {
	// 查询即将开始或结束的任务（在5分钟内）
	broadcastWindow := int64(300) // 5分钟

	query := fmt.Sprintf(`
		SELECT quest_id, quest_type, quest_level, quest_status, quest_unlock_id,
			   linear_quest_ids, single_quest, current_quest_ids, quest_reward,
			   quest_start_time, quest_end_time, quest_repeat_limit, quest_repeat_interval,
			   quest_broadcast, quest_complete_id, quest_repeat_limit_time
		FROM %s 
		WHERE (
			(quest_start_time > 0 AND quest_start_time <= $1 AND quest_start_time > $2 AND quest_status = $3)
			OR 
			(quest_end_time > 0 AND quest_end_time <= $1 AND quest_end_time > $2 AND quest_status = $4)
		)
		AND quest_broadcast IS NOT NULL
		AND quest_broadcast != '{}'
	`, TableQuest)

	rows, err := tx.QueryContext(ctx, query,
		nowTs+broadcastWindow,
		nowTs-broadcastWindow,
		int(MainServer.QuestStatus_QuestStatus_wait),
		int(MainServer.QuestStatus_QuestStatus_open),
	)
	if err != nil {
		qbm.logger.Error("Failed to get quests for broadcast: %v", err)
		return nil, err
	}
	defer rows.Close()

	var quests []*MainServer.QuestInfo
	for rows.Next() {
		quest, err := qbm.scanQuestFromRow(rows)
		if err != nil {
			qbm.logger.Error("Failed to scan quest: %v", err)
			continue
		}
		quests = append(quests, quest)
	}

	return quests, nil
}

// scanQuestFromRow 从数据库行扫描任务信息
func (qbm *QuestBroadcastManager) scanQuestFromRow(rows *sql.Rows) (*MainServer.QuestInfo, error) {
	// 这里复用Quest.go中的扫描逻辑
	// 为了简化，这里只实现基本的扫描
	quest := &MainServer.QuestInfo{}
	var questType, questLevel, questStatus int
	var linearQuestIdsJSON, currentQuestIdsJSON, questBroadcastJSON []byte

	err := rows.Scan(
		&quest.QuestId, &questType, &questLevel, &questStatus, &quest.QuestUnlockId,
		&linearQuestIdsJSON, &quest.SingleQuest, &currentQuestIdsJSON, &quest.QuestReward,
		&quest.QuestStartTime, &quest.QuestEndTime, &quest.QuestRepeatLimit, &quest.QuestRepeatInterval,
		&questBroadcastJSON, &quest.QuestCompleteId, &quest.QuestRepeatLimitTime,
	)
	if err != nil {
		return nil, err
	}

	quest.QuestType = MainServer.QuestType(questType)
	quest.QuestLevel = int32(questLevel)
	quest.QuestStatus = MainServer.QuestStatus(questStatus)

	// 反序列化广播信息
	if len(questBroadcastJSON) > 0 {
		// 这里需要反序列化JSON到QuestBroadcast
		// 简化处理，实际使用时需要完整实现
		quest.QuestBroadcast = &MainServer.QuestBroadcastInfo{}
	}

	return quest, nil
}

// broadcastQuest 广播任务信息
func (qbm *QuestBroadcastManager) broadcastQuest(ctx context.Context, quest *MainServer.QuestInfo, nowTs int64) error {
	if quest.QuestBroadcast == nil {
		return nil
	}

	// 确定广播类型和内容
	var broadcastMessage string

	if nowTs >= quest.QuestStartTime && quest.QuestStatus == MainServer.QuestStatus_QuestStatus_wait {
		broadcastMessage = qbm.formatQuestStartMessage(quest)
	} else if nowTs >= quest.QuestEndTime && quest.QuestStatus == MainServer.QuestStatus_QuestStatus_open {
		broadcastMessage = qbm.formatQuestEndMessage(quest)
	} else {
		return nil // 不需要广播
	}

	// 根据广播类型发送消息
	// TODO: 需要确认QuestBroadcastType枚举的正确命名
	qbm.logger.Info("Broadcasting quest message: %s", broadcastMessage)
	return nil
}

// formatQuestStartMessage 格式化任务开始消息
func (qbm *QuestBroadcastManager) formatQuestStartMessage(quest *MainServer.QuestInfo) string {
	questTypeName := qbm.getQuestTypeName(quest.QuestType)
	return fmt.Sprintf("【任务开始】%s任务已开启！任务ID: %d", questTypeName, quest.QuestId)
}

// formatQuestEndMessage 格式化任务结束消息
func (qbm *QuestBroadcastManager) formatQuestEndMessage(quest *MainServer.QuestInfo) string {
	questTypeName := qbm.getQuestTypeName(quest.QuestType)
	return fmt.Sprintf("【任务结束】%s任务即将结束！任务ID: %d", questTypeName, quest.QuestId)
}

// getQuestTypeName 获取任务类型名称
func (qbm *QuestBroadcastManager) getQuestTypeName(questType MainServer.QuestType) string {
	switch questType {
	case MainServer.QuestType_QuestType_once:
		return "一次性"
	case MainServer.QuestType_QuestType_daily:
		return "每日"
	case MainServer.QuestType_QuestType_weekly:
		return "每周"
	case MainServer.QuestType_QuestType_monthly:
		return "每月"
	case MainServer.QuestType_QuestType_yearly:
		return "每年"
	case MainServer.QuestType_QuestType_repeat:
		return "重复"
	case MainServer.QuestType_QuestType_custom:
		return "自定义"
	case MainServer.QuestType_QuestType_activity:
		return "活动"
	case MainServer.QuestType_QuestType_activity_original_first:
		return "原初活动"
	default:
		return "未知"
	}
}

// sendSystemBroadcast 发送系统广播
func (qbm *QuestBroadcastManager) sendSystemBroadcast(message string) error {
	// 发送系统级广播给所有在线用户
	qbm.logger.Info("System broadcast: %s", message)

	// 这里可以调用Nakama的广播API
	// 示例：
	// err := qbm.nk.NotificationSend(ctx, "", message, map[string]interface{}{
	//     "type": "quest_broadcast",
	//     "broadcast_type": "system",
	// }, "", "", true)

	return nil
}

// sendWorldBroadcast 发送世界广播
func (qbm *QuestBroadcastManager) sendWorldBroadcast(message string) error {
	// 发送世界级广播给所有在线用户
	qbm.logger.Info("World broadcast: %s", message)

	// 这里可以调用Nakama的广播API
	// 示例：
	// err := qbm.nk.NotificationSend(ctx, "", message, map[string]interface{}{
	//     "type": "quest_broadcast",
	//     "broadcast_type": "world",
	// }, "", "", true)

	return nil
}

// sendAreaBroadcast 发送区域广播
func (qbm *QuestBroadcastManager) sendAreaBroadcast(message string, areaValue string) error {
	// 发送区域级广播给指定区域的用户
	qbm.logger.Info("Area broadcast to %s: %s", areaValue, message)

	// 这里需要根据区域值获取该区域的用户列表，然后发送广播
	// 示例：
	// users := getUsersInArea(areaValue)
	// for _, userID := range users {
	//     err := qbm.nk.NotificationSend(ctx, userID, message, map[string]interface{}{
	//         "type": "quest_broadcast",
	//         "broadcast_type": "area",
	//         "area": areaValue,
	//     }, "", "", false)
	// }

	return nil
}

// StartQuestBroadcastScheduler 启动任务广播调度器
func (qbm *QuestBroadcastManager) StartQuestBroadcastScheduler(ctx context.Context, db *sql.DB) {
	// 每分钟检查一次需要广播的任务
	ticker := time.NewTicker(1 * time.Minute)

	go func() {
		for {
			select {
			case <-ticker.C:
				tx, err := db.Begin()
				if err != nil {
					qbm.logger.Error("Failed to begin transaction for quest broadcast: %v", err)
					continue
				}

				err = qbm.CheckAndBroadcastQuests(ctx, tx)
				if err != nil {
					qbm.logger.Error("Failed to check and broadcast quests: %v", err)
					tx.Rollback()
					continue
				}

				err = tx.Commit()
				if err != nil {
					qbm.logger.Error("Failed to commit quest broadcast transaction: %v", err)
				}

			case <-ctx.Done():
				ticker.Stop()
				return
			}
		}
	}()

	qbm.logger.Info("Quest broadcast scheduler started")
}
