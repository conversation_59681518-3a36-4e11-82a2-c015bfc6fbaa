package quest

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"

	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// QuestConditionManager 任务条件管理器（从内存中读取配置）
var QuestUnlockConditions map[int32]*MainServer.QuestUnlockInfo
var QuestCompleteConditions map[int32]*MainServer.QuestCompleteInfo

// InitQuestConditions 初始化任务条件（从JSON配置文件加载到内存）
func InitQuestConditions() {
	// 这里应该从JSON配置文件加载条件数据到内存
	// 目前使用空的map，实际使用时需要加载配置
	QuestUnlockConditions = make(map[int32]*MainServer.QuestUnlockInfo)
	QuestCompleteConditions = make(map[int32]*MainServer.QuestCompleteInfo)
}

// CheckQuestUnlockConditions 检查任务解锁条件
func CheckQuestUnlockConditions(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, unlockId int32) (bool, error) {
	if unlockId == 0 {
		return true, nil // 无解锁条件
	}

	unlockInfo, exists := QuestUnlockConditions[unlockId]
	if !exists {
		logger.Error("Quest unlock condition not found: %d", unlockId)
		return false, fmt.Errorf("unlock condition not found")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByTid(tid)
	if trainer == nil {
		return false, fmt.Errorf("trainer not found")
	}

	// 检查每个解锁条件
	for _, condition := range unlockInfo.QuestUnlockConditions {
		passed, err := checkSingleUnlockCondition(ctx, logger, tx, trainer, unlockInfo.QuestUnlockType, condition)
		if err != nil {
			return false, err
		}
		if !passed {
			return false, nil // 任何一个条件不满足都返回false
		}
	}

	return true, nil
}

// checkSingleUnlockCondition 检查单个解锁条件
func checkSingleUnlockCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, unlockType MainServer.QuestUnlockType, condition *MainServer.QuestConditionInfo) (bool, error) {
	switch unlockType {
	case MainServer.QuestUnlockType_QuestUnlockType_level:
		// 检查等级
		// TODO: 需要确认Trainer结构中等级字段的正确名称
		logger.Info("Level unlock condition check not implemented yet")
		return true, nil

	case MainServer.QuestUnlockType_QuestUnlockType_quest:
		// 检查前置任务是否完成
		questId, err := strconv.ParseInt(condition.ConditionNameId, 10, 32)
		if err != nil {
			return false, fmt.Errorf("invalid quest id: %s", condition.ConditionNameId)
		}
		return IsQuestCompleted(ctx, logger, tx, trainer.Id, int32(questId))

	case MainServer.QuestUnlockType_QuestUnlockType_item:
		// 检查道具数量
		// 这里需要调用道具系统的接口检查道具数量
		// return inventory.CheckItemCount(ctx, logger, tx, trainer.Id, condition.ConditionNameId, condition.ConditionCount)
		logger.Info("Item unlock condition check not implemented yet")
		return true, nil

	case MainServer.QuestUnlockType_QuestUnlockType_money:
		// 检查金钱
		// TODO: 需要确认Trainer结构中金钱字段的正确名称
		logger.Info("Money unlock condition check not implemented yet")
		return true, nil

	case MainServer.QuestUnlockType_QuestUnlockType_poke:
		// 检查是否拥有指定宝可梦
		return checkTrainerHasPoke(trainer, condition.ConditionNameId), nil

	case MainServer.QuestUnlockType_QuestUnlockType_title:
		// 检查是否拥有指定称号
		return checkTrainerHasTitle(trainer, condition.ConditionNameId), nil

	case MainServer.QuestUnlockType_QuestUnlockType_time:
		// 检查时间条件
		// 可以通过json_value解析具体的时间条件
		logger.Info("Time unlock condition check not implemented yet")
		return true, nil

	default:
		logger.Error("Unknown unlock type: %v", unlockType)
		return false, fmt.Errorf("unknown unlock type")
	}
}

// CheckQuestCompleteConditions 检查任务完成条件
func CheckQuestCompleteConditions(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, completeId int32, progress map[string]interface{}) (bool, error) {
	if completeId == 0 {
		return true, nil // 无完成条件
	}

	completeInfo, exists := QuestCompleteConditions[completeId]
	if !exists {
		logger.Error("Quest complete condition not found: %d", completeId)
		return false, fmt.Errorf("complete condition not found")
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByTid(tid)
	if trainer == nil {
		return false, fmt.Errorf("trainer not found")
	}

	// 检查每个完成条件
	for _, condition := range completeInfo.QuestCompleteConditions {
		passed, err := checkSingleCompleteCondition(ctx, logger, tx, trainer, completeInfo.QuestCompleteType, condition, progress)
		if err != nil {
			return false, err
		}
		if !passed {
			return false, nil // 任何一个条件不满足都返回false
		}
	}

	return true, nil
}

// checkSingleCompleteCondition 检查单个完成条件
func checkSingleCompleteCondition(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, completeType MainServer.QuestCompleteType, condition *MainServer.QuestConditionInfo, progress map[string]interface{}) (bool, error) {
	switch completeType {
	case MainServer.QuestCompleteType_QuestCompleteType_battle_poke:
		// 检查击败指定宝可梦的数量
		key := fmt.Sprintf("battle_poke_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_battle_npc:
		// 检查击败指定NPC的数量
		key := fmt.Sprintf("battle_npc_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_collect_item:
		// 检查收集道具数量
		// 这里需要调用道具系统的接口检查道具数量
		// return inventory.CheckItemCount(ctx, logger, tx, trainer.Id, condition.ConditionNameId, condition.ConditionCount)
		logger.Info("Collect item condition check not implemented yet")
		return true, nil

	case MainServer.QuestCompleteType_QuestCompleteType_collect_poke:
		// 检查收集宝可梦数量
		key := fmt.Sprintf("collect_poke_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_talk:
		// 检查与NPC对话
		key := fmt.Sprintf("talk_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_deliver_item:
		// 检查交付道具
		key := fmt.Sprintf("deliver_item_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_deliver_poke:
		// 检查交付宝可梦
		key := fmt.Sprintf("deliver_poke_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_arrive_area:
		// 检查到达指定区域
		key := fmt.Sprintf("arrive_area_%s", condition.ConditionNameId)
		count := getProgressCount(progress, key)
		return count >= condition.ConditionCount, nil

	case MainServer.QuestCompleteType_QuestCompleteType_custom:
		// 自定义条件，通过json_value解析
		return checkCustomCondition(condition.JsonValue, progress), nil

	default:
		logger.Error("Unknown complete type: %v", completeType)
		return false, fmt.Errorf("unknown complete type")
	}
}

// 注意：UpdateQuestProgress函数已移动到TrainerQuest.go中，使用简化的进度系统

// 辅助函数

// getProgressCount 获取进度计数
func getProgressCount(progress map[string]interface{}, key string) int32 {
	if progress == nil {
		return 0
	}
	if count, exists := progress[key]; exists {
		if countFloat, ok := count.(float64); ok {
			return int32(countFloat)
		}
		if countInt, ok := count.(int32); ok {
			return countInt
		}
	}
	return 0
}

// checkTrainerHasPoke 检查训练师是否拥有指定宝可梦
func checkTrainerHasPoke(trainer *MainServer.Trainer, pokeName string) bool {
	for _, pokeId := range trainer.PokeIds {
		if pokeId == pokeName {
			return true
		}
	}
	return false
}

// checkTrainerHasTitle 检查训练师是否拥有指定称号
func checkTrainerHasTitle(trainer *MainServer.Trainer, titleName string) bool {
	if trainer.Decoration == nil || trainer.Decoration.Titles == nil {
		return false
	}

	for _, title := range trainer.Decoration.Titles {
		if title.Type.String() == titleName {
			return true
		}
	}
	return false
}

// IsQuestCompleted 检查任务是否已完成
func IsQuestCompleted(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (bool, error) {
	query := fmt.Sprintf(`
		SELECT quest_status FROM %s WHERE tid = $1 AND quest_id = $2
	`, TableTrainerQuest)

	var status int32
	err := tx.QueryRowContext(ctx, query, tid, questId).Scan(&status)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // 任务未接受，视为未完成
		}
		logger.Error("Failed to check quest completion: %v", err)
		return false, err
	}

	return status == int32(MainServer.TrainerQuestStatus_TrainerQuestStatus_finish) || status == int32(MainServer.TrainerQuestStatus_TrainerQuestStatus_reward), nil
}

// checkCustomCondition 检查自定义条件
func checkCustomCondition(jsonValue string, progress map[string]interface{}) bool {
	// 解析自定义条件的JSON配置
	// 这里可以根据具体需求实现复杂的自定义逻辑
	if jsonValue == "" {
		return true
	}

	// 示例：解析简单的键值对条件
	var customCondition map[string]interface{}
	err := json.Unmarshal([]byte(jsonValue), &customCondition)
	if err != nil {
		return false
	}

	// 检查自定义条件
	for key, expectedValue := range customCondition {
		actualValue := getProgressCount(progress, key)
		if expectedFloat, ok := expectedValue.(float64); ok {
			if actualValue < int32(expectedFloat) {
				return false
			}
		}
	}

	return true
}
