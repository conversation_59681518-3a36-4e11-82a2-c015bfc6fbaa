package quest

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// InitQuestSystem 初始化任务系统
func InitQuestSystem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule) error {
	// 创建数据库表
	err := InitQuest(ctx, logger, db)
	if err != nil {
		logger.Error("Failed to initialize quest tables: %v", err)
		return err
	}

	// 初始化任务条件配置
	InitQuestConditions()

	// 初始化任务奖励配置
	InitQuestRewards()

	// 启动任务广播调度器
	broadcastManager := NewQuestBroadcastManager(logger, nk)
	broadcastManager.StartQuestBroadcastScheduler(ctx, db)

	logger.Info("Quest system initialized successfully")
	return nil
}

// RegisterQuestRPCs 注册任务相关的RPC函数
func RegisterQuestRPCs(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	// 注册接受任务RPC
	err := initializer.RegisterRpc("accept_quest", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcAcceptQuest(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	// 注册完成任务RPC
	err = initializer.RegisterRpc("complete_quest", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcCompleteQuest(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	// 注册领取奖励RPC
	err = initializer.RegisterRpc("claim_quest_reward", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcClaimQuestReward(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	// 注册获取可用任务RPC
	err = initializer.RegisterRpc("get_available_quests", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcGetAvailableQuests(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	// 注册获取训练师任务RPC
	err = initializer.RegisterRpc("get_trainer_quests", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcGetTrainerQuests(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	// 注册更新任务进度RPC
	err = initializer.RegisterRpc("update_quest_progress", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
		return RpcUpdateQuestProgress(ctx, logger, db, nk, payload)
	})
	if err != nil {
		return err
	}

	logger.Info("Quest RPCs registered successfully")
	return nil
}

// QuestSystemConfig 任务系统配置
type QuestSystemConfig struct {
	EnableBroadcast     bool `json:"enable_broadcast"`       // 是否启用广播
	BroadcastInterval   int  `json:"broadcast_interval"`     // 广播检查间隔（秒）
	MaxQuestsPerTrainer int  `json:"max_quests_per_trainer"` // 每个训练师最大任务数
	QuestTimeoutSeconds int  `json:"quest_timeout_seconds"`  // 任务超时时间（秒）
	EnableQuestLog      bool `json:"enable_quest_log"`       // 是否启用任务日志
	LogRetentionDays    int  `json:"log_retention_days"`     // 日志保留天数
}

// DefaultQuestSystemConfig 默认任务系统配置
var DefaultQuestSystemConfig = QuestSystemConfig{
	EnableBroadcast:     true,
	BroadcastInterval:   60,    // 1分钟
	MaxQuestsPerTrainer: 50,    // 最多50个任务
	QuestTimeoutSeconds: 86400, // 24小时
	EnableQuestLog:      true,
	LogRetentionDays:    30, // 保留30天
}

// LoadQuestSystemConfig 加载任务系统配置
func LoadQuestSystemConfig() QuestSystemConfig {
	// 这里可以从配置文件或环境变量加载配置
	// 目前返回默认配置
	return DefaultQuestSystemConfig
}

// ValidateQuestSystemConfig 验证任务系统配置
func ValidateQuestSystemConfig(config QuestSystemConfig) error {
	if config.BroadcastInterval <= 0 {
		config.BroadcastInterval = 60
	}
	if config.MaxQuestsPerTrainer <= 0 {
		config.MaxQuestsPerTrainer = 50
	}
	if config.QuestTimeoutSeconds <= 0 {
		config.QuestTimeoutSeconds = 86400
	}
	if config.LogRetentionDays <= 0 {
		config.LogRetentionDays = 30
	}
	return nil
}

// CleanupExpiredQuests 清理过期任务
func CleanupExpiredQuests(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 清理过期的任务记录
	config := LoadQuestSystemConfig()
	if config.EnableQuestLog && config.LogRetentionDays > 0 {
		retentionTimestamp := time.Now().AddDate(0, 0, -config.LogRetentionDays).Unix()

		query := `
			DELETE FROM trainer_quest 
			WHERE quest_status IN (2, 3, 4) 
			AND update_ts < $1
		`

		result, err := tx.ExecContext(ctx, query, retentionTimestamp)
		if err != nil {
			logger.Error("Failed to cleanup expired quests: %v", err)
			return err
		}

		rowsAffected, _ := result.RowsAffected()
		logger.Info("Cleaned up %d expired quest records", rowsAffected)
	}

	return tx.Commit()
}

// GetQuestSystemStats 获取任务系统统计信息
func GetQuestSystemStats(ctx context.Context, logger runtime.Logger, db *sql.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计总任务数
	var totalQuests int
	err := db.QueryRowContext(ctx, "SELECT COUNT(*) FROM quest").Scan(&totalQuests)
	if err != nil {
		return nil, err
	}
	stats["total_quests"] = totalQuests

	// 统计活跃任务数
	var activeQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM quest WHERE quest_status = 1").Scan(&activeQuests)
	if err != nil {
		return nil, err
	}
	stats["active_quests"] = activeQuests

	// 统计训练师任务总数
	var totalTrainerQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest").Scan(&totalTrainerQuests)
	if err != nil {
		return nil, err
	}
	stats["total_trainer_quests"] = totalTrainerQuests

	// 统计进行中的训练师任务数
	var inProgressQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest WHERE quest_status = 1").Scan(&inProgressQuests)
	if err != nil {
		return nil, err
	}
	stats["in_progress_quests"] = inProgressQuests

	// 统计已完成的训练师任务数
	var completedQuests int
	err = db.QueryRowContext(ctx, "SELECT COUNT(*) FROM trainer_quest WHERE quest_status IN (2, 3)").Scan(&completedQuests)
	if err != nil {
		return nil, err
	}
	stats["completed_quests"] = completedQuests

	return stats, nil
}

// HealthCheckQuestSystem 任务系统健康检查
func HealthCheckQuestSystem(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 检查数据库连接
	err := db.PingContext(ctx)
	if err != nil {
		return fmt.Errorf("database connection failed: %v", err)
	}

	// 检查必要的表是否存在
	tables := []string{"quest", "trainer_quest"}
	for _, table := range tables {
		var exists bool
		query := `
			SELECT EXISTS (
				SELECT FROM information_schema.tables 
				WHERE table_schema = 'public' 
				AND table_name = $1
			)
		`
		err := db.QueryRowContext(ctx, query, table).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check table %s: %v", table, err)
		}
		if !exists {
			return fmt.Errorf("table %s does not exist", table)
		}
	}

	logger.Info("Quest system health check passed")
	return nil
}
