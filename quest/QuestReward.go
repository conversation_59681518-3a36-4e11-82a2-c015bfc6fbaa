package quest

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"

	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// QuestRewardManager 任务奖励管理器（从内存中读取配置）
var QuestRewardConfigs map[int32]*MainServer.QuestRewardInfo

// InitQuestRewards 初始化任务奖励配置（从JSON配置文件加载到内存）
func InitQuestRewards() {
	// 这里应该从JSON配置文件加载奖励数据到内存
	// 目前使用空的map，实际使用时需要加载配置
	QuestRewardConfigs = make(map[int32]*MainServer.QuestRewardInfo)
}

// GrantQuestReward 发放任务奖励
func GrantQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, rewardInfo *MainServer.QuestRewardInfo) error {
	if rewardInfo == nil {
		return nil // 无奖励
	}

	// 处理每个奖励项
	for _, rewardValue := range rewardInfo.QuestRewards {
		// 检查奖励概率
		if !checkRewardProbability(rewardValue.QuestRewardRate) {
			continue // 概率未命中，跳过此奖励
		}

		// 计算奖励数量
		rewardCount := calculateRewardCount(rewardValue)

		// 根据奖励类型发放奖励
		err := grantSingleReward(ctx, logger, tx, tid, rewardValue.QuestRewardType, rewardCount)
		if err != nil {
			logger.Error("Failed to grant reward: %v", err)
			return err
		}
	}

	return nil
}

// checkRewardProbability 检查奖励概率
func checkRewardProbability(rate float32) bool {
	if rate <= 0 {
		return false
	}
	if rate >= 1.0 {
		return true
	}

	// 生成0-1之间的随机数
	randomValue := rand.Float32()
	return randomValue <= rate
}

// calculateRewardCount 计算奖励数量
func calculateRewardCount(rewardValue *MainServer.QuestRewardValue) int32 {
	baseCount := rewardValue.QuestRewardCount
	if baseCount <= 0 {
		return 0
	}

	switch rewardValue.QuestRewardCountRate {
	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_None:
		// 固定数量
		return baseCount

	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Average:
		// 从1到最大值，每个数量概率相同
		return int32(rand.Intn(int(baseCount))) + 1

	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Stairs:
		// 阶梯概率，数值越小概率越大
		return calculateStairsCount(baseCount)

	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Mid_Max_Lower:
		// 最大值概率最低，中间值概率较高
		return calculateMidMaxLowerCount(baseCount)

	default:
		return baseCount
	}
}

// calculateStairsCount 计算阶梯概率的数量
func calculateStairsCount(maxCount int32) int32 {
	if maxCount <= 1 {
		return maxCount
	}

	// 创建权重数组，数值越小权重越大
	weights := make([]float32, maxCount)
	totalWeight := float32(0)

	for i := int32(0); i < maxCount; i++ {
		// 权重递减：第1个数量权重最大，最后一个权重最小
		weights[i] = float32(maxCount - i)
		totalWeight += weights[i]
	}

	// 生成随机数选择
	randomValue := rand.Float32() * totalWeight
	currentWeight := float32(0)

	for i := int32(0); i < maxCount; i++ {
		currentWeight += weights[i]
		if randomValue <= currentWeight {
			return i + 1 // 返回1-based的数量
		}
	}

	return maxCount
}

// calculateMidMaxLowerCount 计算中间值概率高、最大值概率低的数量
func calculateMidMaxLowerCount(maxCount int32) int32 {
	if maxCount <= 1 {
		return maxCount
	}

	midPoint := maxCount / 2

	// 前70%概率分配给前半部分（平均分配）
	if rand.Float32() < 0.7 {
		return int32(rand.Intn(int(midPoint))) + 1
	}

	// 后30%概率分配给后半部分（阶梯递减）
	remaining := maxCount - midPoint
	weights := make([]float32, remaining)
	totalWeight := float32(0)

	for i := int32(0); i < remaining; i++ {
		weights[i] = float32(remaining - i)
		totalWeight += weights[i]
	}

	randomValue := rand.Float32() * totalWeight
	currentWeight := float32(0)

	for i := int32(0); i < remaining; i++ {
		currentWeight += weights[i]
		if randomValue <= currentWeight {
			return midPoint + i + 1
		}
	}

	return maxCount
}

// grantSingleReward 发放单个奖励
func grantSingleReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, rewardType MainServer.QuestRewardType, count int32) error {
	switch rewardType {
	case MainServer.QuestRewardType_QuestRewardType_Pokemon:
		// 发放宝可梦奖励
		return grantPokemonReward(ctx, logger, tx, tid, count)

	case MainServer.QuestRewardType_QuestRewardType_Item:
		// 发放道具奖励
		return grantItemReward(ctx, logger, tx, tid, count)

	case MainServer.QuestRewardType_QuestRewardType_Cloth:
		// 发放服装奖励
		return grantClothReward(ctx, logger, tx, tid, count)

	default:
		logger.Error("Unknown reward type: %v", rewardType)
		return fmt.Errorf("unknown reward type")
	}
}

// grantPokemonReward 发放宝可梦奖励
func grantPokemonReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, count int32) error {
	// 这里需要调用宝可梦系统的接口来发放宝可梦
	// 示例：
	// for i := int32(0); i < count; i++ {
	//     err := pokemon.AddPokemonToTrainer(ctx, logger, tx, tid, pokemonId)
	//     if err != nil {
	//         return err
	//     }
	// }

	logger.Info("Pokemon reward granted to trainer %d, count: %d", tid, count)
	return nil
}

// grantItemReward 发放道具奖励
func grantItemReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, count int32) error {
	// 这里需要调用道具系统的接口来发放道具
	// 示例：
	// err := inventory.AddItem(ctx, logger, tx, tid, itemId, count)
	// if err != nil {
	//     return err
	// }

	logger.Info("Item reward granted to trainer %d, count: %d", tid, count)
	return nil
}

// grantClothReward 发放服装奖励
func grantClothReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, count int32) error {
	// 这里需要调用服装系统的接口来发放服装
	// 示例：
	// for i := int32(0); i < count; i++ {
	//     err := cloth.AddClothToTrainer(ctx, logger, tx, tid, clothId)
	//     if err != nil {
	//         return err
	//     }
	// }

	logger.Info("Cloth reward granted to trainer %d, count: %d", tid, count)
	return nil
}

// GetRewardPreview 获取奖励预览（不实际发放）
func GetRewardPreview(rewardId int32) (*MainServer.QuestRewardInfo, error) {
	if rewardId == 0 {
		return nil, nil
	}

	rewardInfo, exists := QuestRewardConfigs[rewardId]
	if !exists {
		return nil, fmt.Errorf("reward config not found")
	}

	return rewardInfo, nil
}

// CalculateRewardPreview 计算奖励预览（显示可能获得的奖励范围）
func CalculateRewardPreview(rewardId int32) (map[MainServer.QuestRewardType]RewardRange, error) {
	rewardInfo, err := GetRewardPreview(rewardId)
	if err != nil {
		return nil, err
	}
	if rewardInfo == nil {
		return nil, nil
	}

	preview := make(map[MainServer.QuestRewardType]RewardRange)

	for _, rewardValue := range rewardInfo.QuestRewards {
		rewardType := rewardValue.QuestRewardType

		// 计算数量范围
		minCount, maxCount := calculateCountRange(rewardValue)

		if existing, exists := preview[rewardType]; exists {
			// 如果已存在该类型奖励，累加范围
			existing.MinCount += minCount
			existing.MaxCount += maxCount
			existing.Probability = 1.0 - (1.0-existing.Probability)*(1.0-rewardValue.QuestRewardRate)
			preview[rewardType] = existing
		} else {
			preview[rewardType] = RewardRange{
				MinCount:    minCount,
				MaxCount:    maxCount,
				Probability: rewardValue.QuestRewardRate,
			}
		}
	}

	return preview, nil
}

// RewardRange 奖励范围
type RewardRange struct {
	MinCount    int32   `json:"min_count"`
	MaxCount    int32   `json:"max_count"`
	Probability float32 `json:"probability"`
}

// calculateCountRange 计算数量范围
func calculateCountRange(rewardValue *MainServer.QuestRewardValue) (int32, int32) {
	baseCount := rewardValue.QuestRewardCount
	if baseCount <= 0 {
		return 0, 0
	}

	switch rewardValue.QuestRewardCountRate {
	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_None:
		return baseCount, baseCount

	case MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Average,
		MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Stairs,
		MainServer.QuestRewardValueCountRateType_QuestRewardValueCountRateType_Mid_Max_Lower:
		return 1, baseCount

	default:
		return baseCount, baseCount
	}
}

// init 初始化随机数种子
// 注意：Go 1.20+ 不再需要手动设置随机数种子
func init() {
	// rand.Seed(time.Now().UnixNano()) // 已弃用
}
