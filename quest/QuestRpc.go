package quest

import (
	"context"
	"database/sql"

	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RpcAcceptQuest RPC函数：接受任务
func RpcAcceptQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcAcceptQuestRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.<PERSON>gin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 接受任务
	err = AcceptQuest(ctx, logger, tx, trainer.Id, request.QuestId)
	if err != nil {
		return "", runtime.NewError("接受任务失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcAcceptQuestResponse{
		Success: true,
		Message: "任务接受成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcCompleteQuest RPC函数：完成任务
func RpcCompleteQuest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcCompleteQuestRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 完成任务
	err = CompleteQuest(ctx, logger, tx, trainer.Id, request.QuestId)
	if err != nil {
		return "", runtime.NewError("完成任务失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcCompleteQuestResponse{
		Success: true,
		Message: "任务完成成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcClaimQuestReward RPC函数：领取任务奖励
func RpcClaimQuestReward(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcClaimQuestRewardRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 领取任务奖励
	err = ClaimQuestReward(ctx, logger, tx, trainer.Id, request.QuestId)
	if err != nil {
		return "", runtime.NewError("领取任务奖励失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcClaimQuestRewardResponse{
		Success: true,
		Message: "任务奖励领取成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcGetAvailableQuests RPC函数：获取可用任务列表
func RpcGetAvailableQuests(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcGetAvailableQuestsRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取可用任务列表
	quests, err := GetAvailableQuests(ctx, logger, tx, request.QuestType)
	if err != nil {
		return "", runtime.NewError("获取可用任务列表失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetAvailableQuestsResponse{
		Success: true,
		Message: "获取可用任务列表成功",
		Quests:  quests,
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcGetTrainerQuests RPC函数：获取训练师任务列表
func RpcGetTrainerQuests(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取训练师任务列表
	trainerQuests, err := GetTrainerQuestList(ctx, logger, tx, trainer.Id)
	if err != nil {
		return "", runtime.NewError("获取训练师任务列表失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetTrainerQuestsResponse{
		Success:       true,
		Message:       "获取训练师任务列表成功",
		TrainerQuests: trainerQuests,
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}

// RpcUpdateQuestProgress RPC函数：更新任务进度
func RpcUpdateQuestProgress(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcUpdateQuestProgressRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 更新任务进度
	err = UpdateQuestProgress(ctx, logger, tx, trainer.Id, request.QuestId, request.Progress)
	if err != nil {
		return "", runtime.NewError("更新任务进度失败: "+err.Error(), 400)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcUpdateQuestProgressResponse{
		Success: true,
		Message: "任务进度更新成功",
	}

	resultStr, err := tool.ProtoToBase64(result)
	if err != nil {
		return "", runtime.NewError("Failed to encode response", 500)
	}
	return resultStr, nil
}
