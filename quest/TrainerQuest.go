package quest

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 使用proto定义的TrainerQuestStatus枚举

// AcceptQuest 接受任务
func AcceptQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) error {
	// 检查任务是否存在且可接受
	quest, err := GetQuestById(ctx, logger, tx, questId)
	if err != nil {
		return fmt.Errorf("quest not found: %v", err)
	}

	// 检查任务时间是否有效
	nowTs := time.Now().Unix()
	if quest.QuestStartTime > 0 && nowTs < quest.QuestStartTime {
		return fmt.Errorf("quest not started yet")
	}
	if quest.QuestEndTime > 0 && nowTs > quest.QuestEndTime {
		return fmt.Errorf("quest has ended")
	}

	// 检查是否已经接受过该任务
	exists, err := IsQuestAccepted(ctx, logger, tx, tid, questId)
	if err != nil {
		return err
	}
	if exists {
		return fmt.Errorf("quest already accepted")
	}

	// 检查解锁条件
	unlocked, err := CheckQuestUnlockConditions(ctx, logger, tx, tid, quest.QuestUnlockId)
	if err != nil {
		return err
	}
	if !unlocked {
		return fmt.Errorf("quest unlock conditions not met")
	}

	// 检查重复限制
	if quest.QuestRepeatLimit > 0 {
		count, err := GetQuestRepeatCount(ctx, logger, tx, tid, questId)
		if err != nil {
			return err
		}
		if count >= quest.QuestRepeatLimit {
			return fmt.Errorf("quest repeat limit exceeded")
		}
	}

	// 插入训练师任务记录
	query := fmt.Sprintf(`
		INSERT INTO %s (tid, quest_id, quest_type, quest_status, quest_progress, quest_start_time, quest_repeat_limit_time, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`, TableTrainerQuest)

	_, err = tx.ExecContext(ctx, query,
		tid, questId, int(quest.QuestType), int(MainServer.TrainerQuestStatus_TrainerQuestStatus_accept), 0, nowTs, quest.QuestRepeatLimitTime, nowTs, nowTs,
	)

	if err != nil {
		logger.Error("Failed to accept quest: %v", err)
		return err
	}

	return nil
}

// CompleteQuest 完成任务
func CompleteQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) error {
	// 检查任务是否已接受
	trainerQuest, err := GetTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("quest not accepted: %v", err)
	}

	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return fmt.Errorf("quest is not in progress")
	}

	// 获取任务信息
	quest, err := GetQuestById(ctx, logger, tx, questId)
	if err != nil {
		return err
	}

	// 检查完成条件（简化版本，基于进度值）
	if trainerQuest.QuestProgress < 1 {
		return fmt.Errorf("quest complete conditions not met")
	}

	// 检查任务时间限制
	if quest.QuestRepeatLimitTime > 0 {
		nowTs := time.Now().Unix()
		if nowTs-trainerQuest.QuestStartTime > int64(quest.QuestRepeatLimitTime) {
			// 任务超时，标记为失败
			err = UpdateTrainerQuestStatus(ctx, logger, tx, tid, questId, MainServer.TrainerQuestStatus_TrainerQuestStatus_failed)
			if err != nil {
				return err
			}
			return fmt.Errorf("quest time limit exceeded")
		}
	}

	// 更新任务状态为已完成
	nowTs := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s SET quest_status = $1, quest_end_time = $2, update_ts = $3
		WHERE tid = $4 AND quest_id = $5
	`, TableTrainerQuest)

	_, err = tx.ExecContext(ctx, query,
		int(MainServer.TrainerQuestStatus_TrainerQuestStatus_finish), nowTs, nowTs, tid, questId,
	)

	if err != nil {
		logger.Error("Failed to complete quest: %v", err)
		return err
	}

	return nil
}

// ClaimQuestReward 领取任务奖励
func ClaimQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) error {
	// 检查任务是否已完成
	trainerQuest, err := GetTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("quest not found: %v", err)
	}

	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_finish {
		return fmt.Errorf("quest is not completed")
	}

	// 获取任务信息
	quest, err := GetQuestById(ctx, logger, tx, questId)
	if err != nil {
		return err
	}

	// 发放奖励
	err = GrantQuestReward(ctx, logger, tx, tid, quest.QuestReward)
	if err != nil {
		return err
	}

	// 更新奖励领取状态
	nowTs := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s SET quest_status = $1, update_ts = $2
		WHERE tid = $3 AND quest_id = $4
	`, TableTrainerQuest)

	_, err = tx.ExecContext(ctx, query,
		int(MainServer.TrainerQuestStatus_TrainerQuestStatus_reward), nowTs, tid, questId,
	)

	if err != nil {
		logger.Error("Failed to claim quest reward: %v", err)
		return err
	}

	return nil
}

// IsQuestAccepted 检查任务是否已被接受
func IsQuestAccepted(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (bool, error) {
	query := fmt.Sprintf(`
		SELECT COUNT(*) FROM %s WHERE tid = $1 AND quest_id = $2
	`, TableTrainerQuest)

	var count int
	err := tx.QueryRowContext(ctx, query, tid, questId).Scan(&count)
	if err != nil {
		logger.Error("Failed to check quest acceptance: %v", err)
		return false, err
	}

	return count > 0, nil
}

// GetTrainerQuest 获取训练师任务信息
func GetTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (*MainServer.TrainerQuest, error) {
	query := fmt.Sprintf(`
		SELECT id, quest_id, quest_type, quest_status, quest_progress, quest_start_time, quest_end_time, quest_repeat_limit_time
		FROM %s WHERE tid = $1 AND quest_id = $2
	`, TableTrainerQuest)

	trainerQuest := &MainServer.TrainerQuest{}

	var questStatus int32
	err := tx.QueryRowContext(ctx, query, tid, questId).Scan(
		&trainerQuest.Id, &trainerQuest.QuestId, &trainerQuest.QuestType,
		&questStatus, &trainerQuest.QuestProgress, &trainerQuest.QuestStartTime,
		&trainerQuest.QuestEndTime, &trainerQuest.QuestRepeatLimitTime,
	)
	trainerQuest.QuestStatus = MainServer.TrainerQuestStatus(questStatus)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trainer quest not found")
		}
		logger.Error("Failed to get trainer quest: %v", err)
		return nil, err
	}

	// 获取任务详细信息
	questInfo, err := GetQuestById(ctx, logger, tx, int32(trainerQuest.QuestId))
	if err != nil {
		logger.Error("Failed to get quest info: %v", err)
		// 不返回错误，继续处理
	} else {
		trainerQuest.QuestInfo = questInfo
	}

	return trainerQuest, nil
}

// 直接使用MainServer.TrainerQuest proto结构

// UpdateTrainerQuestStatus 更新训练师任务状态
func UpdateTrainerQuestStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, status MainServer.TrainerQuestStatus) error {
	nowTs := time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET quest_status = $1, update_ts = $2 WHERE tid = $3 AND quest_id = $4
	`, TableTrainerQuest)

	_, err := tx.ExecContext(ctx, query, int(status), nowTs, tid, questId)
	if err != nil {
		logger.Error("Failed to update trainer quest status: %v", err)
		return err
	}

	return nil
}

// GetQuestRepeatCount 获取任务重复次数
func GetQuestRepeatCount(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (int32, error) {
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(repeat_count), 0) FROM %s WHERE tid = $1 AND quest_id = $2
	`, TableTrainerQuest)

	var count int32
	err := tx.QueryRowContext(ctx, query, tid, questId).Scan(&count)
	if err != nil {
		logger.Error("Failed to get quest repeat count: %v", err)
		return 0, err
	}

	return count, nil
}

// UpdateQuestProgress 更新任务进度
func UpdateQuestProgress(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, progress int32) error {
	query := fmt.Sprintf(`
		UPDATE %s SET quest_progress = $1, update_ts = $2
		WHERE tid = $3 AND quest_id = $4
	`, TableTrainerQuest)

	nowTs := time.Now().Unix()
	_, err := tx.ExecContext(ctx, query, progress, nowTs, tid, questId)
	if err != nil {
		logger.Error("Failed to update quest progress: %v", err)
		return err
	}

	return nil
}

// GetTrainerQuestList 获取训练师任务列表
func GetTrainerQuestList(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerQuest, error) {
	query := fmt.Sprintf(`
		SELECT id, quest_id, quest_type, quest_status, quest_progress, quest_start_time, quest_end_time, quest_repeat_limit_time
		FROM %s WHERE tid = $1 ORDER BY quest_start_time DESC
	`, TableTrainerQuest)

	rows, err := tx.QueryContext(ctx, query, tid)
	if err != nil {
		logger.Error("Failed to get trainer quest list: %v", err)
		return nil, err
	}
	defer rows.Close()

	var trainerQuests []*MainServer.TrainerQuest
	for rows.Next() {
		trainerQuest := &MainServer.TrainerQuest{}
		var questStatus int32

		err := rows.Scan(
			&trainerQuest.Id, &trainerQuest.QuestId, &trainerQuest.QuestType,
			&questStatus, &trainerQuest.QuestProgress, &trainerQuest.QuestStartTime,
			&trainerQuest.QuestEndTime, &trainerQuest.QuestRepeatLimitTime,
		)
		trainerQuest.QuestStatus = MainServer.TrainerQuestStatus(questStatus)
		if err != nil {
			logger.Error("Failed to scan trainer quest: %v", err)
			continue
		}

		// 获取任务详细信息
		questInfo, err := GetQuestById(ctx, logger, tx, int32(trainerQuest.QuestId))
		if err != nil {
			logger.Error("Failed to get quest info for quest %d: %v", trainerQuest.QuestId, err)
			// 继续处理，不中断列表获取
		} else {
			trainerQuest.QuestInfo = questInfo
		}

		trainerQuests = append(trainerQuests, trainerQuest)
	}

	return trainerQuests, nil
}
