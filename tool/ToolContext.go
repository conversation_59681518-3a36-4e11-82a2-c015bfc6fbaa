package tool

import (
	"context"
	"go-nakama-poke/proto/MainServer"
	"strconv"
	"sync"

	"github.com/heroiclabs/nakama-common/runtime"
)

var (
	_userActiveMap = make(map[string]*MainServer.Trainer) // 以用户ID为键
	_trainerIdMap  = make(map[string]*MainServer.Trainer) // 以Trainer.Id为键
	mapMutex       sync.RWMutex                           // 读写锁，确保并发安全
)
var (
	_trainerAroundPokesMap = make(map[string][]*MainServer.Poke)
	mu                     sync.RWMutex
)

// 添加或更新 trainer 的 Pokemon
func AddOrUpdateTrainerAroundPokes(tid int64, pokes []*MainServer.Poke) {
	idstr := strconv.FormatInt(tid, 10)
	mu.Lock()
	defer mu.Unlock()
	_trainerAroundPokesMap[idstr] = pokes
}

// 删除 trainer 的 Pokemon
func RemoveTrainerAroundPokes(tid int64) {
	idstr := strconv.FormatInt(tid, 10)
	mu.Lock()
	defer mu.Unlock()
	delete(_trainerAroundPokesMap, idstr)
}
func RemoveTrainerAroundPokesBy(tid string) {
	mu.Lock()
	defer mu.Unlock()
	delete(_trainerAroundPokesMap, tid)
}

// 获取 trainer 的 Pokemon
func GetTrainerAroundPokes(tid int64) ([]*MainServer.Poke, bool) {
	idstr := strconv.FormatInt(tid, 10)
	mu.RLock()
	defer mu.RUnlock()
	pokes, exists := _trainerAroundPokesMap[idstr]
	return pokes, exists
}

// PokeIds:     append([]string{}, trainer.PokeIds...), // 深拷贝切片
// Badges:      append([]string{}, trainer.Badges...), // 深拷贝切片
func GetDesensitizeActiveTrainersByIds(ids []string) []*MainServer.Trainer {
	mapMutex.RLock()         // 加读锁
	defer mapMutex.RUnlock() // 解锁

	var trainers []*MainServer.Trainer
	for _, id := range ids {
		trainer, exists := _trainerIdMap[id]
		if exists {
			// 深拷贝 trainer 对象
			desensitizedTrainer := CopySafeTrainer(trainer)
			// desensitizedTrainer := &MainServer.Trainer{
			// 	Id:           trainer.Id,
			// 	Uid:          trainer.Uid,
			// 	Name:         trainer.Name,
			// 	Gender:       trainer.Gender,
			// 	Loc:          trainer.Loc,
			// 	PokeIds:      make([]string, len(trainer.PokeIds)), // 初始化切片
			// 	Action:       trainer.Action,
			// 	Items:        make(map[string]string),             // 初始化映射
			// 	Badges:       make([]string, len(trainer.Badges)), // 初始化切片
			// 	Belong:       trainer.Belong,
			// 	GroupId:      trainer.GroupId,
			// 	Cloth:        trainer.Cloth,      // 假设 TrainerCloth 是简单结构体
			// 	Coin:         0,                  // 重置为 0
			// 	Contribution: 0,                  // 重置为 0
			// 	SpecialCoin:  0,                  // 重置为 0
			// 	FollowPoke:   trainer.FollowPoke, // 假设 TrainerFollowPoke 是简单结构体
			// 	CreateTs:     0,
			// 	UpdateTs:     0,
			// }

			// // 深拷贝切片字段
			// copy(desensitizedTrainer.PokeIds, trainer.PokeIds)
			// copy(desensitizedTrainer.Badges, trainer.Badges)

			// // 深拷贝映射字段
			// for key, value := range trainer.Items {
			// 	desensitizedTrainer.Items[key] = value
			// }

			trainers = append(trainers, desensitizedTrainer)
		} else {
			RemoveActiveTrainer(id)
		}
	}
	return trainers
}

// GetActiveTrainersByIds 根据 Trainer.Id 获取所有 Trainer
func GetActiveTrainersByIds(ids []int64) []*MainServer.Trainer {
	mapMutex.RLock()         // 加读锁
	defer mapMutex.RUnlock() // 解锁

	var trainers []*MainServer.Trainer
	for _, id := range ids {
		if id < 0 {
			continue
		}
		idstr := strconv.FormatInt(id, 10)
		trainer, exists := _trainerIdMap[idstr]
		if exists {
			trainers = append(trainers, trainer)
		} else {
			RemoveActiveTrainer(idstr)
		}
	}
	return trainers
}
func GetActiveTrainersBase64ByIds(ids []string) []string {
	mapMutex.RLock()         // 加读锁
	defer mapMutex.RUnlock() // 解锁

	var trainers []string
	for _, id := range ids {
		if trainer, exists := _trainerIdMap[id]; exists {
			baseStr, err := ProtoToBase64(trainer)
			if err == nil {
				trainers = append(trainers, baseStr)
			}
		}
	}
	return trainers
}

// GetActiveTrainerByCtx 根据上下文获取用户的活跃 Trainer
func GetActiveTrainerByCtx(ctx context.Context) *MainServer.Trainer {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	return GetActiveTrainerByUid(userID)
}
func GetActiveTrainerByTid(tid int64) *MainServer.Trainer {
	mapMutex.RLock()         // 加读锁
	defer mapMutex.RUnlock() // 解锁
	idstr := strconv.FormatInt(tid, 10)
	return _trainerIdMap[idstr]
}

// GetActiveTrainerByUid 获取用户的活跃 Trainer
func GetActiveTrainerByUid(userID string) *MainServer.Trainer {
	mapMutex.RLock()         // 加读锁
	defer mapMutex.RUnlock() // 解锁

	return _userActiveMap[userID]
}
func RemoveActiveTrainer(tid string) bool {
	mapMutex.Lock()         // 加写锁
	defer mapMutex.Unlock() // 解锁

	// 检查用户是否存在
	if trainer, exists := _trainerIdMap[tid]; exists {
		delete(_userActiveMap, trainer.Uid)
		delete(_trainerIdMap, tid) // 同时从 _trainerIdMap 中删除
		RemoveTrainerAroundPokesBy(tid)
		return true
	}
	return false
}

// RemoveUserActiveTrainer 删除用户的活跃 Trainer
func RemoveUserActiveTrainer(userID string) bool {
	mapMutex.Lock()         // 加写锁
	defer mapMutex.Unlock() // 解锁

	// 检查用户是否存在
	if trainer, exists := _userActiveMap[userID]; exists {
		delete(_userActiveMap, userID)
		tidstr := strconv.FormatInt(trainer.Id, 10)
		delete(_trainerIdMap, tidstr) // 同时从 _trainerIdMap 中删除
		RemoveTrainerAroundPokesBy(tidstr)
		return true
	}
	return false
}

// SetUserActiveTrainer 设置用户的活跃 Trainer，并维护 _trainerIdMap
func SetUserActiveTrainer(userID string, trainer *MainServer.Trainer) {
	mapMutex.Lock()         // 加写锁
	defer mapMutex.Unlock() // 解锁

	// 如果用户已存在，需更新 _trainerIdMap
	if existingTrainer, exists := _userActiveMap[userID]; exists {
		delete(_trainerIdMap, strconv.FormatInt(existingTrainer.Id, 10)) // 删除旧的 Trainer.Id
	}

	_userActiveMap[userID] = trainer
	_trainerIdMap[strconv.FormatInt(trainer.Id, 10)] = trainer // 添加新的 Trainer.Id 映射
}
