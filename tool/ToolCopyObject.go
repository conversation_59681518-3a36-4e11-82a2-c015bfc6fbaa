package tool

import (
	"go-nakama-poke/proto/MainServer"

	"google.golang.org/protobuf/proto"
)

func CopySafeTrainer(trainer *MainServer.Trainer) *MainServer.Trainer {
	// 深拷贝 trainer 对象
	desensitizedTrainer := &MainServer.Trainer{
		Id:           trainer.Id,
		Uid:          trainer.Uid,
		Name:         trainer.Name,
		Gender:       trainer.Gender,
		Loc:          trainer.Loc,
		PokeIds:      make([]string, len(trainer.PokeIds)), // 初始化切片
		Action:       trainer.Action,
		Items:        make(map[string]*MainServer.TrainerItemInfo), // 初始化映射
		Badges:       make([]string, len(trainer.Badges)),          // 初始化切片
		Belong:       trainer.Belong,
		GroupId:      trainer.GroupId,
		Cloth:        trainer.Cloth,                   // 假设 TrainerCloth 是简单结构体
		Coin:         0,                               // 重置为 0
		BelongInfo:   &MainServer.TrainerBelongInfo{}, // 重置为 0
		SpecialCoin:  0,                               // 重置为 0
		FollowPoke:   trainer.FollowPoke,              // 假设 TrainerFollowPoke 是简单结构体
		CreateTs:     0,
		UpdateTs:     0,
		SpecialRight: MainServer.TrainerSpecialRight_TrainerNone,
		BoxStatus:    &MainServer.TrainerBoxStatus{},
		SessionInfo: &MainServer.TrainerSessionInfo{
			SessionEndTs: trainer.SessionInfo.SessionEndTs,
			LocInfo:      proto.Clone(trainer.SessionInfo.LocInfo).(*MainServer.TrainerLocInfo),
			MatchId:      trainer.SessionInfo.MatchId,
		},
	}

	// 深拷贝切片字段
	copy(desensitizedTrainer.PokeIds, trainer.PokeIds)
	copy(desensitizedTrainer.Badges, trainer.Badges)

	// 深拷贝映射字段
	for key, value := range trainer.Items {
		desensitizedTrainer.Items[key] = value
	}
	return desensitizedTrainer
}
