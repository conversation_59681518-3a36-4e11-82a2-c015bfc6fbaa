package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableTrainerName = "trainer"

func initTrainers(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTrainerTableIfNotExists(ctx, logger, db)
}

// 初始宝可梦名称与对应世代的字典
var starters = map[string]int{
	// 第一代
	"bulbasaur": 1, "charmander": 1, "squirtle": 1,
	// 第二代
	"chikorita": 2, "cyndaquil": 2, "totodile": 2,
	// 第三代
	"treecko": 3, "torchic": 3, "mudkip": 3,
	// 第四代
	"turtwig": 4, "chimchar": 4, "piplup": 4,
	// 第五代
	"snivy": 5, "tepig": 5, "oshawott": 5,
	// 第六代
	"chespin": 6, "fennekin": 6, "froakie": 6,
	// 第七代
	"rowlet": 7, "litten": 7, "popplio": 7,
	// 第八代
	"grookey": 8, "scorbunny": 8, "sobble": 8,
	// 第九代
	"sprigatito": 9, "fuecoco": 9, "quaxly": 9,
}

func createTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, trainer *MainServer.Trainer) (string, error) {

	trainer.CreateTs = time.Now().UnixMilli()
	// trainer.Uid = trainer.Uid
	trainer.Id = -1
	trainer.Action = MainServer.TrainerActionType_idle
	if len(trainer.PokeIds) != 1 {
		return "", fmt.Errorf("Trainer poke count err %d", len(trainer.PokeIds))
	}
	pokeName := trainer.PokeIds[0]
	has, _ := isStarter(pokeName)
	if !has {
		return "", fmt.Errorf("Trainer poke name err")
	}
	unique, err := isNameValid(ctx, db, trainer.Name)
	if err != nil {
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	if !unique {
		return "", fmt.Errorf("trainer Name err")
	}
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}

	poke_new, err := poke.CreatePoke(ctx, tx, pokeName, "", 5)
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	idStr := strconv.FormatInt(int64(poke_new.Id), 10)
	trainer.PokeIds = []string{idStr}

	tid, err := UpsertTrainer(ctx, logger, tx, trainer)
	// 插入 Trainer 数据
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to upsert trainer: %v", err)
	}
	poke_new.Tid = trainer.Id
	err = poke.UpdatePokeData(ctx, tx, poke_new)
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to upsert trainer: %v", err)
	}
	// 初始化 Trainer 的 PokeBox
	if err := initializeTrainerBoxes(ctx, logger, tx, trainer, false); err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to initialize trainer box: %v", err)
	}
	// 训练师库存
	// if err := inventory.InitTrainerInventory(ctx, logger, tid, tx); err != nil {
	// 	tx.Rollback()
	// 	return "", fmt.Errorf("failed to Init TrainerInventory : %v", err)
	// }

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return strconv.FormatInt(tid, 10), nil
}
func UpdateTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, trainer *MainServer.Trainer) (*MainServer.Trainer, error) {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	//更新时间
	UpsertTrainer(ctx, logger, tx, trainer)
	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}
	return trainer, nil
}
func UserHasTrainer(db *sql.DB, userId string, tid int64) bool {
	count := userTrainerCount(db, userId, tid, false)
	// 如果 count > 0，表示有匹配的记录
	return count > 0
}

// Check if a given name (with whitespace removed) is unique in the database
// isNamePass 检查 name 是否合格（去除空格后唯一且符合其他合格性规则）
func isNameValid(ctx context.Context, db *sql.DB, name string) (bool, error) {
	// 去除 name 中的所有空格
	cleanedName := strings.ReplaceAll(name, " ", "")
	// 基础检查：name 去掉空格后是否为空
	if cleanedName == "" {
		return false, fmt.Errorf("name cannot be empty after removing spaces")
	}

	// 检查长度（假设合格名称长度范围为 3 到 20 个字符）
	if len(cleanedName) < 3 || len(cleanedName) > 20 {
		return false, fmt.Errorf("name length must be between 3 and 20 characters after removing spaces")
	}

	// 检查是否包含特殊字符
	// for _, char := range cleanedName {
	// 	if !unicode.IsLetter(char) && !unicode.IsDigit(char) {
	// 		return false, fmt.Errorf("name can only contain letters and numbers")
	// 	}
	// }

	// 查询数据库中是否已经存在去除空格后相同的名称
	query := fmt.Sprintf(`
		SELECT COUNT(1)
		FROM %s
		WHERE name = $1;
	`, TableTrainerName)

	var count int
	err := db.QueryRowContext(ctx, query, cleanedName).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check name uniqueness: %v", err)
	}

	// 如果 count 不为 0，说明名称已存在
	if count > 0 {
		return false, fmt.Errorf("name already exists")
	}

	// 名称符合所有规则
	return true, nil
}

func isStarter(name string) (bool, int) {
	generation, exists := starters[name]
	return exists, generation
}
func userTrainerCount(db *sql.DB, userId string, tid int64, onlyUid bool) int {
	query := fmt.Sprintf("SELECT COUNT(1) FROM %s WHERE id = ? AND uid = ?", TableTrainerName)
	if onlyUid {
		query = fmt.Sprintf("SELECT COUNT(1) FROM %s WHERE uid = ?", TableTrainerName)
	}

	// 使用参数化查询，防止 SQL 注入
	var count int
	var err error
	if onlyUid {
		err = db.QueryRow(query, tid, userId).Scan(&count)
	} else {
		err = db.QueryRow(query, userId).Scan(&count)
	}
	if err != nil {
		log.Printf("没有查询出结果，不存在Trainer: %v", err)
		return 0
	}
	// 如果 count > 0，表示有匹配的记录
	return count
}
func UpdateTrainerPokeIds(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, newPokeIds []string) error {
	// trainer.Lock() // 或全局锁
	// defer trainer.Unlock()
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("UpdateTrainerPokeIds failed to upsert trainer: %v", err)
		return err
	}
	trainer.PokeIds = newPokeIds
	// 标记dirty，后续批量UpsertTrainer
	return nil
}

// 将 Trainer 数据插入或更新到数据库中，支持事务
func UpsertTrainer(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) (int64, error) {
	trainer.UpdateTs = time.Now().UnixMilli()
	tableName := TableTrainerName

	if trainer.Id < 0 {
		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", tableName)).Scan(&trainer.Id)
		if err != nil {
			return 0, fmt.Errorf("failed to generate new id: %v", err)
		}
	}

	updateSQL := fmt.Sprintf(`
        INSERT INTO %s (
            id, uid, name, gender, loc, poke_ids, action, items, badges, belong, 
            group_id, cloth, coin, belong_info, special_coin, follow_poke, 
            create_ts, update_ts, online_time, special_right, box_status
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 
            $11, $12, $13, $14, $15, $16, 
            $17, $18, $19, $20, $21
        )
        ON CONFLICT (id) DO UPDATE SET
            uid = EXCLUDED.uid,
            name = EXCLUDED.name,
            gender = EXCLUDED.gender,
            loc = EXCLUDED.loc,
            poke_ids = EXCLUDED.poke_ids,
            action = EXCLUDED.action,
            items = EXCLUDED.items,
            badges = EXCLUDED.badges,
            belong = EXCLUDED.belong,
            group_id = EXCLUDED.group_id,
            cloth = EXCLUDED.cloth,
            coin = EXCLUDED.coin,
            belong_info = EXCLUDED.belong_info,
            special_coin = EXCLUDED.special_coin,
            follow_poke = EXCLUDED.follow_poke,
            create_ts = EXCLUDED.create_ts,
            update_ts = EXCLUDED.update_ts,
            online_time = EXCLUDED.online_time,
            special_right = EXCLUDED.special_right,
            box_status = EXCLUDED.box_status
        RETURNING id;
    `, tableName)

	// 序列化字段
	itemsJSON, err := json.Marshal(trainer.Items)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize items: %v", err)
	}

	clothJSON, err := json.Marshal(trainer.Cloth)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize cloth: %v", err)
	}

	belongInfoJSON, err := json.Marshal(trainer.BelongInfo)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize belong_info: %v", err)
	}

	followPokeJSON, err := json.Marshal(trainer.FollowPoke)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize follow_poke: %v", err)
	}

	specialRightJSON, err := json.Marshal(trainer.SpecialRight)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize special_right: %v", err)
	}

	boxStatusJSON, err := json.Marshal(trainer.BoxStatus)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize box_status: %v", err)
	}

	// logger.Info("UpsertTrainer - Preparing SQL with parameters:",
	// 	"id:", trainer.Id, "uid:", trainer.Uid, "name:", trainer.Name, "gender:", trainer.Gender,
	// 	"loc:", trainer.Loc, "poke_ids:", strings.Join(trainer.PokeIds, ","),
	// 	"action:", trainer.Action, "items:", string(itemsJSON),
	// 	"badges:", strings.Join(trainer.Badges, ","), "belong:", trainer.Belong,
	// 	"group_id:", trainer.GroupId, "cloth:", string(clothJSON), "coin:", trainer.Coin,
	// 	"belongInfoJSON:", string(belongInfoJSON), "special_coin:", trainer.SpecialCoin,
	// 	"follow_poke:", string(followPokeJSON), "create_ts:", trainer.CreateTs, "update_ts:", trainer.UpdateTs,
	// 	"online_time", trainer.OnlineTime,
	// )

	var id int64
	err = tx.QueryRowContext(ctx, updateSQL,
		trainer.Id, trainer.Uid, trainer.Name, trainer.Gender, trainer.Loc,
		strings.Join(trainer.PokeIds, ","), trainer.Action, itemsJSON,
		strings.Join(trainer.Badges, ","), trainer.Belong, trainer.GroupId,
		clothJSON, trainer.Coin, belongInfoJSON, trainer.SpecialCoin,
		followPokeJSON, trainer.CreateTs, trainer.UpdateTs, trainer.OnlineTime,
		specialRightJSON, boxStatusJSON,
	).Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("failed to upsert trainer: %v", err)
	}

	return id, nil
}

func createTrainerTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	tableName := TableTrainerName

	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            uid VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            gender VARCHAR(5) NOT NULL,
            loc VARCHAR(255),
            poke_ids VARCHAR(255),
            action INT,
            items JSONB NOT NULL DEFAULT '{}'::jsonb,
            badges TEXT,
            belong VARCHAR(255),
            group_id VARCHAR(255),
            cloth JSONB NOT NULL DEFAULT '{}'::jsonb,
            coin BIGINT DEFAULT 0,
            belong_info JSONB NOT NULL DEFAULT '{}'::jsonb,
            special_coin BIGINT DEFAULT 0,
            follow_poke JSONB NOT NULL DEFAULT '{}'::jsonb,
            create_ts BIGINT,
            update_ts BIGINT,
            online_time BIGINT,
            special_right JSONB NOT NULL DEFAULT '{}'::jsonb,
            box_status JSONB NOT NULL DEFAULT '{}'::jsonb
        );
    `, tableName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("failed to create table %s: %v", tableName, err)
		return fmt.Errorf("failed to create table %s: %v", tableName, err)
	}
	logger.Info("Trainer table created successfully.")
	return nil
}

// scanTrainer 从数据库行中读取数据并解析为 Trainer 对象
func scanTrainer(scanner interface {
	Scan(dest ...interface{}) error
}) (*MainServer.Trainer, error) {
	var (
		trainer          MainServer.Trainer
		pokeIDs          string
		action           int32
		itemsJSON        []byte
		badges           string
		clothJSON        []byte
		belongInfoJSON   []byte
		followPokeJSON   []byte
		specialRightJSON []byte
		boxStatusJSON    []byte
	)

	err := scanner.Scan(
		&trainer.Id, &trainer.Uid, &trainer.Name, &trainer.Gender, &trainer.Loc,
		&pokeIDs, &action, &itemsJSON, &badges, &trainer.Belong,
		&trainer.GroupId, &clothJSON, &trainer.Coin, &belongInfoJSON,
		&trainer.SpecialCoin, &followPokeJSON, &trainer.CreateTs,
		&trainer.UpdateTs, &trainer.OnlineTime, &specialRightJSON, &boxStatusJSON,
	)
	if err != nil {
		return nil, err
	}

	// 解析 JSON 字段
	if err := json.Unmarshal(itemsJSON, &trainer.Items); err != nil {
		return nil, fmt.Errorf("failed to parse items JSON: %v", err)
	}
	if err := json.Unmarshal(clothJSON, &trainer.Cloth); err != nil {
		return nil, fmt.Errorf("failed to parse cloth JSON: %v", err)
	}
	if err := json.Unmarshal(belongInfoJSON, &trainer.BelongInfo); err != nil {
		return nil, fmt.Errorf("failed to parse belong_info JSON: %v", err)
	}
	if err := json.Unmarshal(followPokeJSON, &trainer.FollowPoke); err != nil {
		return nil, fmt.Errorf("failed to parse follow_poke JSON: %v", err)
	}
	if err := json.Unmarshal(specialRightJSON, &trainer.SpecialRight); err != nil {
		return nil, fmt.Errorf("failed to parse special_right JSON: %v", err)
	}
	if err := json.Unmarshal(boxStatusJSON, &trainer.BoxStatus); err != nil {
		return nil, fmt.Errorf("failed to parse box_status JSON: %v", err)
	}

	// 解析其他字段
	trainer.PokeIds = strings.Split(pokeIDs, ",")
	trainer.Action = MainServer.TrainerActionType(action)
	trainer.Badges = strings.Split(badges, ",")

	return &trainer, nil
}

// SelectTrainerProto 根据 trainerId 查询并返回单个 Trainer 的 Proto 对象
func SelectTrainerProto(ctx context.Context, db *sql.DB, trainerId int64) (*MainServer.Trainer, error) {
	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, loc, poke_ids, action, items, badges, belong,
               group_id, cloth, coin, belong_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status
        FROM %s WHERE id = $1`, TableTrainerName)

	row := db.QueryRowContext(ctx, query, trainerId)

	trainer, err := scanTrainer(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trainer with id %d not found", trainerId)
		}
		return nil, fmt.Errorf("failed to retrieve trainer: %v", err)
	}

	// 初始化 sessionInfo（不存储在数据库中的临时数据）
	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

	return trainer, nil
}

// 从数据库中查询并返回Trainer的Proto对象
// func SelectTrainerProto(ctx context.Context, db *sql.DB, trainerId int64) (*MainServer.Trainer, error) {
// 	query := fmt.Sprintf("SELECT id, uid, name, loc, poke_ids, action, items, badges, belong, group_id, cloth FROM %s WHERE id = $1", TableTrainerName)

// 	row := db.QueryRowContext(ctx, query, trainerId)
// 	var (
// 		trainer   MainServer.Trainer
// 		locJSON   []byte
// 		pokeIDs   string
// 		action    int32
// 		itemsJSON []byte
// 		badges    string
// 		clothJSON []byte
// 	)

// 	err := row.Scan(&trainer.Id, &trainer.Uid, &trainer.Name, &locJSON, &pokeIDs, &action, &itemsJSON, &badges, &trainer.Belong, &trainer.GroupId, &clothJSON)
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			return nil, fmt.Errorf("trainer with id %d not found", trainerId)
// 		}
// 		return nil, fmt.Errorf("failed to retrieve trainer: %v", err)
// 	}

// 	// 解析loc JSON数据
// 	if err := json.Unmarshal(locJSON, &trainer.Loc); err != nil {
// 		return nil, fmt.Errorf("failed to parse loc JSON: %v", err)
// 	}

// 	// 解析items JSON数据
// 	if err := json.Unmarshal(itemsJSON, &trainer.Items); err != nil {
// 		return nil, fmt.Errorf("failed to parse items JSON: %v", err)
// 	}

// 	// 解析cloth JSON数据
// 	if err := json.Unmarshal(clothJSON, &trainer.Cloth); err != nil {
// 		return nil, fmt.Errorf("failed to parse cloth JSON: %v", err)
// 	}

// 	// 设置其他字段
// 	trainer.PokeIds = strings.Split(pokeIDs, ",")
// 	trainer.Action = MainServer.TrainerActionType(action)
// 	trainer.Badges = strings.Split(badges, ",")

// 	return &trainer, nil
// }

// 从数据库中查询并返回Trainer的Map对象
//
//	func selectTrainerMap(ctx context.Context, db *sql.DB, trainerId string) ([]map[string]interface{}, error) {
//		query := fmt.Sprintf("SELECT * FROM %s WHERE id = $1", TableTrainerName)
//		return tool.RetrieveMap(db, query, trainerId)
//	}

// SelectTrainersByUID 根据 UID 查询并返回多个 Trainer 的 Proto 对象切片
func SelectTrainersByUID(ctx context.Context, db *sql.DB, uid string) ([]*MainServer.Trainer, error) {
	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, loc, poke_ids, action, items, badges, belong,
               group_id, cloth, coin, belong_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status
        FROM %s WHERE uid = $1`, TableTrainerName)

	rows, err := db.QueryContext(ctx, query, uid)
	if err != nil {
		return nil, fmt.Errorf("failed to query trainers: %v", err)
	}
	defer rows.Close()

	var trainers []*MainServer.Trainer
	for rows.Next() {
		trainer, err := scanTrainer(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trainer: %v", err)
		}
		// 初始化 sessionInfo（不存储在数据库中的临时数据）
		trainer.SessionInfo = &MainServer.TrainerSessionInfo{}
		trainers = append(trainers, trainer)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %v", err)
	}

	return trainers, nil
}

func selectTrainerMapByTrainerId(ctx context.Context, db *sql.DB, trainerId string) ([]map[string]interface{}, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE id = '%s'", TableTrainerName, trainerId)
	return tool.RetrieveMap(ctx, db, query)
}

func selectTrainerMapByUId(ctx context.Context, db *sql.DB, uid string) ([]map[string]interface{}, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE uid = '%s'", TableTrainerName, uid)
	return tool.RetrieveMap(ctx, db, query)
}
