package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"sort"
	"strconv"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TablePokeBoxName          = "poke_boxes"
	oneUserMaxBoxCount        = 21 // 最大盒子数量
	onePokeBoxVolume          = 54 // 每个盒子的最大容量
	oneUserSpecialMaxBoxCount = 10 // 特殊盒子最大数量
)

func initPokeBoxs(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createPokeBoxTableIfNotExists(ctx, logger, db)
}

// 创建表格时添加唯一约束 (tid, id)
func createPokeBoxTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	createTableSQL := fmt.Sprintf(`
	CREATE TABLE IF NOT EXISTS %s (
	    id BIGSERIAL PRIMARY KEY,
	    index INT NOT NULL,
	    tid BIGINT NOT NULL,
	    name VARCHAR(30) DEFAULT '',
	    type INT NOT NULL,
	    pokes JSONB NOT NULL DEFAULT '{}'::jsonb,
	    extra JSONB NOT NULL DEFAULT '{}'::jsonb,
	    create_ts BIGINT NOT NULL,
	    update_ts BIGINT NOT NULL,
	    UNIQUE (tid, id)  -- 移除 type 从唯一约束
	);
	CREATE INDEX IF NOT EXISTS idx_%s_tid ON %s (tid);
	CREATE INDEX IF NOT EXISTS idx_%s_update_ts ON %s (update_ts);
	CREATE INDEX IF NOT EXISTS idx_%s_type ON %s (type);
`, TablePokeBoxName, TablePokeBoxName, TablePokeBoxName, TablePokeBoxName, TablePokeBoxName, TablePokeBoxName, TablePokeBoxName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Error creating table %s: %v", TablePokeBoxName, err)
		return err
	}

	logger.Info("Table %s created or already exists", TablePokeBoxName)
	return nil
}

// isSpecialBoxType 判断是否是特殊类型盒子
func isSpecialBoxType(boxType MainServer.PokeBoxType) bool {
	return boxType >= MainServer.PokeBoxType_specialNormal
}

// isSingleBoxType 判断是否是单盒子类型
func isSingleBoxType(boxType MainServer.PokeBoxType) bool {
	return boxType != MainServer.PokeBoxType_normal &&
		boxType != MainServer.PokeBoxType_specialNormal
}

// 初始化 Trainer 的 PokeBox，支持事务
// func initTrainerBox(ctx context.Context, tid int64, tx *sql.Tx) error {
// 	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
// 		INSERT INTO %s (tid, index, type, pokes, create_ts, update_ts)
// 		VALUES ($1, $2, $3, $4, $5, $6)
// 		ON CONFLICT (tid, index, type) DO UPDATE
// 		SET pokes = EXCLUDED.pokes,
// 		    update_ts = EXCLUDED.update_ts
// 	`, TablePokeBoxName))
// 	if err != nil {
// 		return fmt.Errorf("failed to prepare statement: %v", err)
// 	}
// 	defer stmt.Close()

// 	now := time.Now().UnixMilli()

// 	// 初始化普通盒子
// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		if _, err := stmt.ExecContext(ctx, tid, i, MainServer.PokeBoxType_normal, "{}", now, now); err != nil {
// 			return fmt.Errorf("failed to insert normal box at index %d: %v", i, err)
// 		}
// 	}

// 	// 初始化单盒子类型
// 	singleBoxTypes := []MainServer.PokeBoxType{
// 		MainServer.PokeBoxType_hatch,
// 		MainServer.PokeBoxType_sale,
// 		MainServer.PokeBoxType_rent,
// 		MainServer.PokeBoxType_around,
// 	}

// 	for _, boxType := range singleBoxTypes {
// 		if _, err := stmt.ExecContext(ctx, tid, 0, boxType, "{}", now, now); err != nil {
// 			return fmt.Errorf("failed to insert box type %v: %v", boxType, err)
// 		}
// 	}

// 	return nil
// }

// 检查盒子访问权限
func checkBoxAccess(trainer *MainServer.Trainer, boxIndex int32, boxType MainServer.PokeBoxType, isWrite bool) error {
	if trainer == nil || trainer.BoxStatus == nil {
		return runtime.NewError("trainer or box status not initialized", 500)
	}

	// 检查特殊类型盒子权限
	if isSpecialBoxType(boxType) {
		// 写入操作需要特殊权限
		if isWrite && trainer.SpecialRight == MainServer.TrainerSpecialRight_TrainerNone {
			return runtime.NewError("no permission to modify special box", 403)
		}
		// 检查特殊盒子索引范围
		if boxType == MainServer.PokeBoxType_specialNormal && boxIndex >= trainer.BoxStatus.SpecialActiveBoxes {
			return runtime.NewError(fmt.Sprintf("special box index %d exceeds limit %d", boxIndex, trainer.BoxStatus.SpecialActiveBoxes), 403)
		}
	}

	// 检查单盒子类型
	if isSingleBoxType(boxType) && boxIndex != 0 {
		return runtime.NewError(fmt.Sprintf("box type %v only allows index 0", boxType), 403)
	}

	// 检查普通盒子的访问权限
	if boxType == MainServer.PokeBoxType_normal && boxIndex >= trainer.BoxStatus.ActiveBoxes {
		return runtime.NewError(fmt.Sprintf("normal box index %d exceeds limit %d", boxIndex, trainer.BoxStatus.ActiveBoxes), 403)
	}

	return nil
}

// 上架
func SalePokemon(ctx context.Context, tx *sql.Tx, boxIndex int, pokeId int64, price int32) (int64, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return 0, runtime.NewError("Not found tid", 404)
	}

	// 检查盒子访问权限
	if err := checkBoxAccess(trainer, int32(boxIndex), MainServer.PokeBoxType_normal, true); err != nil {
		return 0, err
	}

	// 查询是否存在对应的 Pokemon
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, pokeId)
	if pokemon == nil {
		return 0, runtime.NewError("pokemon not found", 400)
	}

	newpoke, _, err := RemovePokeFromBox(ctx, tx, trainer.Id, int32(boxIndex), pokeId, MainServer.PokeBoxType_normal)
	if err != nil {
		return 0, err
	}

	_, err = savePokeToBox(ctx, tx, trainer.Id, newpoke, MainServer.PokeBoxType_sale, 0)
	if err != nil {
		return 0, err
	}

	pokemon.Sale = true
	pokemon.SaleInfo.Price = price
	if err := poke.UpdatePokeData(ctx, tx, pokemon); err != nil {
		return 0, fmt.Errorf("failed to update Pokemon: %w", err)
	}

	return time.Now().UnixMilli(), nil
}

// func createPokeBoxTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
// 	// SQL 创建表格
// 	createTableSQL := `
// 	CREATE TABLE IF NOT EXISTS poke_boxs (
// 	    id BIGSERIAL PRIMARY KEY,
// 	    tid BIGINT NOT NULL,
// 	    index INT NOT NULL,
// 	    type INT NOT NULL,
// 	    pokes NOT NULL DEFAULT '{}'::jsonb,
// 	    extra NOT NULL DEFAULT '{}'::jsonb,
// 	    create_ts BIGINT NOT NULL,
// 	    update_ts BIGINT NOT NULL
// 	);
// 	CREATE INDEX IF NOT EXISTS idx_poke_boxs_tid ON poke_boxs (tid);
// 	CREATE INDEX IF NOT EXISTS idx_poke_boxs_update_ts ON poke_boxs (update_ts);
// 	CREATE INDEX IF NOT EXISTS idx_poke_boxs_type ON poke_boxs (type);
// 	CREATE INDEX IF NOT EXISTS idx_poke_boxs_tid_type ON poke_boxs (tid, type);
// 	`

// 	// 执行 SQL 语句
// 	_, err := db.ExecContext(ctx, createTableSQL)
// 	if err != nil {
// 		logger.Error("Error creating poke_boxs table: %v", err)
// 		return err
// 	}

// 	logger.Info("Table %s created or already exists", TablePokeBoxName)
// 	return nil
// }
// // 初始化 Trainer 的 PokeBox，支持事务
// func initTrainerBox(ctx context.Context, tid int64, tx *sql.Tx) error {
// 	// tid := tool.GetActiveTid(ctx)

// 	// 准备插入语句
// 	stmt, err := tx.PrepareContext(ctx, `
// 		INSERT INTO poke_boxs (tid, index, type, pokes, create_ts, update_ts)
// 		VALUES ($1, $2, $3, $4, $5, $6)
// 	`)
// 	if err != nil {
// 		return fmt.Errorf("failed to prepare statement: %v", err)
// 	}
// 	defer stmt.Close()

// 	// 循环插入每个 PokeBox
// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		_, err := stmt.ExecContext(ctx, tid, i, MainServer.PokeBoxType_normal, nil, time.Now().UnixMilli(), time.Now().UnixMilli())
// 		if err != nil {
// 			return fmt.Errorf("failed to insert PokeBox at index %d: %v", i, err)
// 		}
// 	}

// 	return nil
// }

//	func initTrainerBox(ctx context.Context, db *sql.DB) error {
//		tid := tool.GetActiveTid(ctx)
//		for i := 0; i < oneUserMaxBoxCount; i++ {
//		_, err := db.ExecContext(ctx, `
//						INSERT INTO poke_boxs (tid, index, type, pokes, create_ts, update_ts)
//						VALUES ($1, $2, $3, $4, $5, $6)
//					`, tid, i, MainServer.PokeBoxType_normal, nil, time.Now().UnixMilli(), time.Now().UnixMilli())
//		}
//		return err
//	}
//
// initTrainerBox 初始化玩家的所有 PokeBox
// func initTrainerBox(ctx context.Context, db *sql.DB) error {
// 	tid := tool.GetActiveTid(ctx)

// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return fmt.Errorf("failed to begin transaction: %v", err)
// 	}

// 	// 准备插入语句
// 	stmt, err := tx.PrepareContext(ctx, `
// 		INSERT INTO poke_boxs (tid, index, type, pokes, create_ts, update_ts)
// 		VALUES ($1, $2, $3, $4, $5, $6)
// 	`)
// 	if err != nil {
// 		tx.Rollback()
// 		return fmt.Errorf("failed to prepare statement: %v", err)
// 	}
// 	defer stmt.Close() // 确保语句关闭

// 	// 循环插入每个 PokeBox
// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		_, err := stmt.ExecContext(ctx, tid, i, MainServer.PokeBoxType_normal, nil, time.Now().UnixMilli(), time.Now().UnixMilli())
// 		if err != nil {
// 			tx.Rollback() // 回滚事务
// 			return fmt.Errorf("failed to insert PokeBox at index %d: %v", i, err)
// 		}
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		return fmt.Errorf("failed to commit transaction: %v", err)
// 	}

//		return nil
//	}
func savePokeToBox(ctx context.Context, tx *sql.Tx, tid int64, newpoke *MainServer.BoxPokeInfo, boxType MainServer.PokeBoxType, toIndex int32) (int64, error) {
	pokeBox, err := GetPokeBoxByIndexAnType(ctx, tx, tid, toIndex, boxType)
	if err != nil {
		return 0, err
	}
	if len(pokeBox.Pokes) >= onePokeBoxVolume {
		return 0, runtime.NewError("box is full", 500)
	}
	// if pokeExists(pokeBox.Pokes, newpoke) {
	// 	return 0, runtime.NewError("already sale this Pokemon", 500)
	// }
	//没有位置
	// loc := strconv.Itoa(int(newpoke.Loc))
	newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)
	if newLoc < 0 {
		return 0, runtime.NewError("no available location in box", 500)
	}
	pokeBox.Pokes[strconv.Itoa(newLoc)] = newpoke
	return UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox)
}
func savePokeToNormalBox(ctx context.Context, logger runtime.Logger, trainer *MainServer.Trainer, recordAroundPokeIds *[]string, tx *sql.Tx, pokemon *MainServer.Poke) (*MainServer.BoxPokeInfo, error) {
	saveAround := false
	isRecordAround := recordAroundPokeIds != nil
	if recordAroundPokeIds == nil {
		tmp := make([]string, len(trainer.PokeIds))
		copy(tmp, trainer.PokeIds)
		recordAroundPokeIds = &tmp
	}
	// oldPokeids := trainer.PokeIds
	// 优先保存到Around（随身）
	if len(*recordAroundPokeIds) < 6 {
		*recordAroundPokeIds = append(*recordAroundPokeIds, strconv.FormatInt(pokemon.Id, 10))
		saveAround = true
	}

	if saveAround {
		pokemon.Tid = trainer.Id
		if err := poke.UpdatePokeData(ctx, tx, pokemon); err != nil {
			return nil, fmt.Errorf("failed to update Pokemon: %w", err)
		}
		if !isRecordAround {
			if err := UpdateTrainerPokeIds(ctx, logger, tx, trainer, *recordAroundPokeIds); err != nil {
				return nil, fmt.Errorf("failed to upsert trainer: %w", err)
			}
		}
		return nil, nil
	}

	// 尝试保存到普通盒子
	for i := 0; i < oneUserMaxBoxCount; i++ {
		pokeBox, err := GetPokeBoxByIndexAnType(ctx, tx, trainer.Id, int32(i), MainServer.PokeBoxType_normal)
		if err != nil {
			return nil, err
		}
		if pokeBox == nil || len(pokeBox.Pokes) >= onePokeBoxVolume {
			continue
		}

		newPoke := &MainServer.BoxPokeInfo{
			Id:     pokemon.Id,
			Around: false,
			// Name:    pokemon.Name,
			// Lv:      pokemon.Level,
			// Item:    pokemon.ItemName,
			// Shiny:   pokemon.Shiny,
			ValueTs: 0,
		}

		newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)
		if newLoc < 0 {
			continue
		}
		// newPoke.Loc = int32(newLoc)
		loc := strconv.Itoa(newLoc)
		pokeBox.Pokes[loc] = newPoke

		if _, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox); err != nil {
			return nil, fmt.Errorf("failed to update poke box: %w", err)
		}

		pokemon.Tid = trainer.Id
		if err := poke.UpdatePokeData(ctx, tx, pokemon); err != nil {
			return nil, fmt.Errorf("failed to update Pokemon: %w", err)
		}

		return newPoke, nil
	}

	// 如果有特殊权限且普通盒子都满了，尝试保存到特殊盒子
	if trainer.SpecialRight != MainServer.TrainerSpecialRight_TrainerNone {
		for i := 0; i < oneUserMaxBoxCount; i++ {
			pokeBox, err := GetPokeBoxByIndexAnType(ctx, tx, trainer.Id, int32(i), MainServer.PokeBoxType_specialNormal)
			if err != nil {
				continue
			}
			if pokeBox == nil || len(pokeBox.Pokes) >= onePokeBoxVolume {
				continue
			}

			newPoke := &MainServer.BoxPokeInfo{
				Id: pokemon.Id,
				// Name:    pokemon.Name,
				// Lv:      pokemon.Level,
				// Item:    pokemon.ItemName,
				// Shiny:   pokemon.Shiny,
				ValueTs: 0,
			}

			newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)
			if newLoc < 0 {
				continue
			}
			loc := strconv.Itoa(newLoc)
			pokeBox.Pokes[loc] = newPoke

			if _, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox); err != nil {
				return nil, fmt.Errorf("failed to update poke box: %w", err)
			}

			pokemon.Tid = trainer.Id
			pokemon.Evs = &MainServer.PokeStat{} //清空努力值
			pokemon.SysExtra = &MainServer.PokeSysExtra{}
			if err := poke.UpdatePokeData(ctx, tx, pokemon); err != nil {
				return nil, fmt.Errorf("failed to update Pokemon: %w", err)
			}

			return newPoke, nil
		}
	}

	logger.Warn("All Poke Boxes are full for user %d", trainer.Id)
	return nil, runtime.NewError("all Poke Boxes are full", 500)
}

func SavePokeToRecordArroundOrNormalBox(ctx context.Context, logger runtime.Logger, trainer *MainServer.Trainer, recordAroundPokeIds *[]string, tx *sql.Tx, pokemon *MainServer.Poke) (*MainServer.BoxPokeInfo, error) {
	return savePokeToNormalBox(ctx, logger, trainer, recordAroundPokeIds, tx, pokemon)
}
func SavePokeToNormalBox(ctx context.Context, logger runtime.Logger, trainer *MainServer.Trainer, tx *sql.Tx, pokemon *MainServer.Poke) (*MainServer.BoxPokeInfo, error) {
	return savePokeToNormalBox(ctx, logger, trainer, nil, tx, pokemon)
}

// 创建新的BoxPokeInfo
func createNewBoxPokeInfo(pokemon *MainServer.Poke) *MainServer.BoxPokeInfo {
	return &MainServer.BoxPokeInfo{
		Id: pokemon.Id,
		// Name:    pokemon.Name,
		// Lv:      pokemon.Level,
		// Item:    pokemon.ItemName,
		// Shiny:   pokemon.Shiny,
		ValueTs: 0,
	}
}

// 保存宝可梦到指定盒子并设置位置
func savePokeToBoxWithLoc(ctx context.Context, tx *sql.Tx, pokeBox *MainServer.PokeBox, newPoke *MainServer.BoxPokeInfo) error {
	newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)
	if newLoc < 0 {
		return fmt.Errorf("no available location in box")
	}

	loc := strconv.Itoa(newLoc)
	pokeBox.Pokes[loc] = newPoke

	_, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox)
	return err
}

// 检查并扩容盒子
func checkAndExpandBoxes(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) error {
	return initializeTrainerBoxes(ctx, logger, tx, trainer, true)
}

// GetPokeBoxesByIndexAndType retrieves multiple boxes in a single query
func GetPokeBoxesByIndexAndType(ctx context.Context, tx *sql.Tx, tid int64, boxIndices []int32, boxType MainServer.PokeBoxType) (map[int32]*MainServer.PokeBox, error) {
	if len(boxIndices) == 0 {
		return make(map[int32]*MainServer.PokeBox), nil
	}

	// Build the query with IN clause
	query := fmt.Sprintf(`
		SELECT id, index, tid, name, type, pokes, create_ts, update_ts 
		FROM %s 
		WHERE tid = $1 AND type = $2 AND index = ANY($3)
	`, TablePokeBoxName)

	// Convert boxIndices to []int64 for the ANY clause
	indices := make([]int64, len(boxIndices))
	for i, idx := range boxIndices {
		indices[i] = int64(idx)
	}

	rows, err := tx.QueryContext(ctx, query, tid, boxType, indices)
	if err != nil {
		return nil, fmt.Errorf("failed to query boxes: %w", err)
	}
	defer rows.Close()

	boxes := make(map[int32]*MainServer.PokeBox)
	for rows.Next() {
		var box MainServer.PokeBox
		var pokesJSON []byte

		err := rows.Scan(
			&box.Id,
			&box.Index,
			&box.Tid,
			&box.Name,
			&box.Type,
			&pokesJSON,
			&box.CreateTs,
			&box.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan box: %w", err)
		}

		// Parse pokes JSON
		if err := json.Unmarshal(pokesJSON, &box.Pokes); err != nil {
			return nil, fmt.Errorf("failed to parse pokes JSON: %w", err)
		}

		boxes[box.Index] = &box
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}

	return boxes, nil
}

// GetPokeBoxByIndexAnType is kept for backward compatibility
func GetPokeBoxByIndexAnType(ctx context.Context, tx *sql.Tx, tid int64, boxIndex int32, boxType MainServer.PokeBoxType) (*MainServer.PokeBox, error) {
	boxes, err := GetPokeBoxesByIndexAndType(ctx, tx, tid, []int32{boxIndex}, boxType)
	if err != nil {
		return nil, err
	}
	return boxes[boxIndex], nil
}

func RemovePokeFromBox(ctx context.Context, tx *sql.Tx, tid int64, boxIndex int32, pokeID int64, boxType MainServer.PokeBoxType) (*MainServer.BoxPokeInfo, int64, error) {
	pokeBox, err := GetPokeBoxByIndexAnType(ctx, tx, tid, boxIndex, boxType)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get poke box: %w", err)
	}
	if pokeBox == nil {
		return nil, 0, fmt.Errorf("poke box not found for tid %d, box index %d", tid, boxIndex)
	}

	// 移除指定的 Poke
	var removePoke *MainServer.BoxPokeInfo
	for loc, poke := range pokeBox.Pokes {
		if poke.Id == pokeID {
			removePoke = poke
			delete(pokeBox.Pokes, loc)
			break
		}
	}

	if removePoke == nil {
		return nil, 0, fmt.Errorf("poke with id %d not found in box index %d", pokeID, boxIndex)
	}

	// 更新 poke_boxs 表
	ts, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, pokeBox)
	if err != nil {
		return nil, 0, err
	}

	return removePoke, ts, nil
}
func UpdatePokeBoxWithOptimisticLock(ctx context.Context, tx *sql.Tx, pokeBox *MainServer.PokeBox) (int64, error) {
	query := `
		UPDATE %s 
		SET pokes = $1, name = $2, update_ts = $3, extra = $4, create_ts = $5
		WHERE tid = $6 AND index = $7 AND type = $8 AND update_ts = $9
	`
	query = fmt.Sprintf(query, TablePokeBoxName)

	// 序列化 pokes 数据
	pokesJSON, err := json.Marshal(pokeBox.Pokes)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize pokes: %w", err)
	}

	// 序列化 extra 数据（假设 extra 可序列化）
	extraJSON, err := json.Marshal(pokeBox.Extra)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize extra: %w", err)
	}
	updateTs := time.Now().UnixMilli()
	// 执行更新语句
	result, err := tx.ExecContext(ctx, query,
		pokesJSON,        // 更新的 pokes 列
		pokeBox.Name,     // 更新的 name 列
		updateTs,         // 新的 update_ts
		extraJSON,        // 更新的 extra 列
		pokeBox.CreateTs, // 保留 create_ts，不会改变
		pokeBox.Tid,      // 条件: 训练师 ID
		pokeBox.Index,    // 条件: Box 索引
		pokeBox.Type,     // 条件: Box 类型
		pokeBox.UpdateTs, // 条件: 乐观锁检查的旧 update_ts
	)
	if err != nil {
		return 0, fmt.Errorf("failed to update poke box: %w", err)
	}

	// 检查是否有更新的行
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to fetch affected rows: %w", err)
	}
	if rowsAffected == 0 {
		return 0, fmt.Errorf("poke box update conflict, likely due to outdated update_ts")
	}

	return updateTs, nil
}
func pokeExists(pokes map[string]*MainServer.BoxPokeInfo, newPoke *MainServer.BoxPokeInfo) bool {
	for _, poke := range pokes {
		if poke.Id == newPoke.Id {
			return true
		}
	}
	return false
}

func findSmallestAvailableLoc(pokes map[string]*MainServer.BoxPokeInfo, maxVolume int) int {
	if len(pokes) == 0 {
		return 0
	}

	// Convert map keys to integers and find the smallest available
	usedLocs := make(map[int]bool)
	for locStr := range pokes {
		loc, _ := strconv.Atoi(locStr)
		usedLocs[loc] = true
	}

	// Find the smallest available location
	for i := 0; i < maxVolume; i++ {
		if !usedLocs[i] {
			return i
		}
	}

	return -1
}

func GetSinglePokeBox(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, boxType MainServer.PokeBoxType, boxIndex int32, updateTs int64) (*MainServer.SinglePokeBoxResult, error) {
	pokeIds := []int64{}
	singlePokeBoxResult := &MainServer.SinglePokeBoxResult{}
	if boxType == MainServer.PokeBoxType_around {
		ids, err := tool.ConvertStringsToInts(trainer.PokeIds)
		if err != nil {
			return nil, err
		}
		pokeIds = ids
	} else {
		box, err := GetPokeBoxByIndexAnType(ctx, tx, trainer.Id, boxIndex, boxType)
		if err != nil {
			logger.Error("failed to retrieve source box: %v", err)
			return nil, fmt.Errorf("failed to retrieve source box: %w", err)
		}
		pokeIds = make([]int64, 0, len(box.Pokes))
		for _, info := range box.Pokes {
			pokeIds = append(pokeIds, info.Id)
		}
		singlePokeBoxResult.Box = box
	}
	pokes, err := poke.QueryPokesByIdsAndUpdateTs(ctx, tx, trainer.Id, pokeIds, 0)
	if err != nil {
		return nil, err
	}
	if boxType == MainServer.PokeBoxType_around {
		sortPokesByIDs(pokes, pokeIds)
	}
	singlePokeBoxResult.Pokes = pokes
	return singlePokeBoxResult, nil
}
func sortPokesByIDs(pokes []*MainServer.Poke, pokeIds []int64) {
	// 创建一个 id 到顺序的映射
	idOrder := make(map[int64]int)
	for i, id := range pokeIds {
		idOrder[id] = i
	}

	sort.SliceStable(pokes, func(i, j int) bool {
		iRank, iOk := idOrder[pokes[i].Id]
		jRank, jOk := idOrder[pokes[j].Id]

		// 都在列表中，按顺序比
		if iOk && jOk {
			return iRank < jRank
		}
		// 只有一个在列表中，排前面
		if iOk {
			return true
		}
		if jOk {
			return false
		}
		// 都不在，保留原顺序
		return false
	})
}

func BatchExchangePokeBox(ctx context.Context, logger runtime.Logger, tx *sql.Tx, exchanges []*MainServer.PokeBoxExchange) (int64, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return 0, runtime.NewError("Not found tid", 404)
	}

	// 收集所有需要查询的盒子索引
	boxIndicesByType := make(map[MainServer.PokeBoxType][]int32)
	for _, exchange := range exchanges {
		if exchange.IsDelete {
			if exchange.SourceBoxType == MainServer.PokeBoxType_around && len(trainer.PokeIds) < 2 {
				return 0, runtime.NewError("around box cannot be empty", 400)
			}
			boxIndicesByType[exchange.SourceBoxType] = append(boxIndicesByType[exchange.SourceBoxType], exchange.SourceBox)
		} else {
			boxIndicesByType[exchange.SourceBoxType] = append(boxIndicesByType[exchange.SourceBoxType], exchange.SourceBox)
			boxIndicesByType[exchange.TargetBoxType] = append(boxIndicesByType[exchange.TargetBoxType], exchange.TargetBox)
		}
	}

	// 一次性获取所有需要的盒子
	boxesToUpdate := make(map[string]*MainServer.PokeBox)
	for boxType, indices := range boxIndicesByType {
		// 去重索引
		uniqueIndices := make(map[int32]bool)
		for _, idx := range indices {
			uniqueIndices[idx] = true
		}
		// 转换回切片
		uniqueIdxSlice := make([]int32, 0, len(uniqueIndices))
		for idx := range uniqueIndices {
			uniqueIdxSlice = append(uniqueIdxSlice, idx)
		}

		boxes, err := GetPokeBoxesByIndexAndType(ctx, tx, trainer.Id, uniqueIdxSlice, boxType)
		if err != nil {
			logger.Error("failed to retrieve boxes: %v", err)
			return 0, fmt.Errorf("failed to retrieve boxes: %w", err)
		}
		for index, box := range boxes {
			key := fmt.Sprintf("%d_%d_%d", trainer.Id, index, boxType)
			boxesToUpdate[key] = box
		}
	}
	_, aroundExists := boxIndicesByType[MainServer.PokeBoxType_around]
	aroundKey := fmt.Sprintf("%d_%d_%d", trainer.Id, 0, MainServer.PokeBoxType_around)
	if aroundExists {
		aroundBox := &MainServer.PokeBox{
			Tid:   trainer.Id,
			Type:  MainServer.PokeBoxType_around,
			Pokes: make(map[string]*MainServer.BoxPokeInfo),
		}
		for i := 0; i < len(trainer.PokeIds); i++ {
			id, err := strconv.ParseInt(trainer.PokeIds[i], 10, 64)
			if err != nil {
				return 0, err
			}
			aroundBox.Pokes[strconv.Itoa(i)] = &MainServer.BoxPokeInfo{
				Id: id,
			}
		}
		boxesToUpdate[aroundKey] = aroundBox
	}

	// 处理每个交换操作
	for _, exchange := range exchanges {
		sourceKey := fmt.Sprintf("%d_%d_%d", trainer.Id, exchange.SourceBox, exchange.SourceBoxType)
		sourceBox, exists := boxesToUpdate[sourceKey]
		if !exists {
			return 0, fmt.Errorf("source box not found: %s", sourceKey)
		}

		if exchange.IsDelete {
			delete(sourceBox.Pokes, strconv.Itoa(int(exchange.SourceLoc)))
			continue
		}

		targetKey := fmt.Sprintf("%d_%d_%d", trainer.Id, exchange.TargetBox, exchange.TargetBoxType)
		targetBox, exists := boxesToUpdate[targetKey]
		if !exists {
			return 0, fmt.Errorf("target box not found: %s", targetKey)
		}

		sourceLoc := strconv.Itoa(int(exchange.SourceLoc))
		targetLoc := strconv.Itoa(int(exchange.TargetLoc))

		// Check if source location exists
		sourcePoke, exists := sourceBox.Pokes[sourceLoc]
		if !exists {
			return 0, fmt.Errorf("source location %s not found in box %s", sourceLoc, sourceKey)
		}

		// Get target poke if exists
		targetPoke, exists := targetBox.Pokes[targetLoc]

		// Move source poke to target location
		targetBox.Pokes[targetLoc] = sourcePoke

		// If target location had a poke, move it to source location
		if exists {
			sourceBox.Pokes[sourceLoc] = targetPoke
		} else {
			delete(sourceBox.Pokes, sourceLoc)
		}
	}
	if aroundExists {
		aroundBox := boxesToUpdate[aroundKey]
		pokeEntries := make([]struct {
			KeyInt int
			Poke   *MainServer.BoxPokeInfo
		}, 0, len(aroundBox.Pokes))

		for k, v := range aroundBox.Pokes {
			keyInt, err := strconv.Atoi(k)
			if err != nil {
				return 0, fmt.Errorf("invalid poke key in aroundBox: %s", k)
			}
			pokeEntries = append(pokeEntries, struct {
				KeyInt int
				Poke   *MainServer.BoxPokeInfo
			}{KeyInt: keyInt, Poke: v})
		}

		// 按 key 升序排序
		sort.Slice(pokeEntries, func(i, j int) bool {
			return pokeEntries[i].KeyInt < pokeEntries[j].KeyInt
		})

		// 生成紧凑的 PokeIds
		pokeList := make([]string, 0, len(pokeEntries))
		for _, entry := range pokeEntries {
			pokeList = append(pokeList, strconv.FormatInt(entry.Poke.Id, 10))
		}
		delete(boxesToUpdate, aroundKey)
		if err := UpdateTrainerPokeIds(ctx, logger, tx, trainer, pokeList); err != nil {
			return 0, fmt.Errorf("failed to upsert trainer: %w", err)
		}
	}

	// 批量更新所有修改过的盒子
	var lastUpdateTs int64
	for _, box := range boxesToUpdate {
		ts, err := UpdatePokeBoxWithOptimisticLock(ctx, tx, box)
		if err != nil {
			logger.Error("failed to update box: %v", err)
			return 0, fmt.Errorf("failed to update box: %w", err)
		}
		lastUpdateTs = ts
	}

	logger.Info("Successfully exchanged pokes between boxes in batch")
	return lastUpdateTs, nil
}

// ExchangePokeBox now uses the batch function for consistency
func ExchangePokeBox(ctx context.Context, logger runtime.Logger, tx *sql.Tx, exchange *MainServer.PokeBoxExchange) (int64, error) {
	return BatchExchangePokeBox(ctx, logger, tx, []*MainServer.PokeBoxExchange{exchange})
}

func GetPokeBoxsByTs(ctx context.Context, logger runtime.Logger, db *sql.DB, boxType MainServer.PokeBoxType, updateTs float64) ([]*MainServer.PokeBox, error) {
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
		logger.Error("未找到用户的 active tid %s", userID)
		return nil, runtime.NewError("Not found tid", 404)
	}
	// 查询SQL，添加了 tid 的限制
	query := `
        SELECT id, index, tid, name, type, pokes, extra, create_ts, update_ts
        FROM %s
        WHERE type = $1 AND tid = $2 AND update_ts > $3
    `
	query = fmt.Sprintf(query, TablePokeBoxName)

	rows, err := db.QueryContext(ctx, query, boxType, trainer.Id, updateTs) // 传递 userID 参数
	if err != nil {
		logger.Error("Error fetching poke box: %v", err)
		return nil, err
	}
	defer rows.Close()

	var pokeBoxes []*MainServer.PokeBox
	for rows.Next() {
		var box MainServer.PokeBox
		var pokesJSON, extraJSON string

		err = rows.Scan(&box.Id, &box.Index, &box.Tid, &box.Name, &box.Type, &pokesJSON, &extraJSON, &box.CreateTs, &box.UpdateTs)
		if err != nil {
			logger.Error("Error scanning poke box row: %v", err)
			return nil, err
		}

		// 反序列化 pokes 和 extra
		err = json.Unmarshal([]byte(pokesJSON), &box.Pokes)
		if err != nil {
			return nil, fmt.Errorf("failed to deserialize pokes: %w", err)
		}
		if extraJSON != "" {
			err = json.Unmarshal([]byte(extraJSON), &box.Extra)
			if err != nil {
				return nil, fmt.Errorf("failed to deserialize extra: %w", err)
			}
		}

		pokeBoxes = append(pokeBoxes, &box)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error during rows iteration: %v", err)
		return nil, err
	}

	return pokeBoxes, nil
}

// func UpdatePokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, PokeBoxExchange exchange) error {
// 	//通过exchange里面的信息，读取当前用户的index为source_box和type为source_box_type的PokeBox，将pokes的loc为source_loc的BoxPokeInfo删除，取出，没有则抛异常
// 	//通过exchange里面的信息，读取当前用户的index为target_box和type为target_box_type的PokeBox，将pokes的loc为target_loc的BoxPokeInfo替换成source取出的BoxPokeInfo，要是target原本有值，则需要写回source的位置
// 	//交换成功
// }
// func GetPokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, PokeBoxType type, double update_ts) PokeBox[], error {
// 	//根据update_ts读取当前用户所有大于update_ts和type为type的PokeBox
// 	//poke的信息再调一次接口去sync
// }

// func savePokeToNormalBox(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, db *sql.DB, tid int64, pokeID int64, name string) error {
// 	trainer := tool.GetActiveTrainer(ctx)
// 	if len(trainer.PokeIds) < 6 {
// 		tx, err := db.BeginTx(ctx, nil)
// 		if err != nil {
// 			logger.Error("db BeginTx: %v", err)
// 			return runtime.NewError("db BeginTx", 500)
// 		}
// 		trainer.PokeIds = append(trainer.PokeIds, strconv.FormatInt(int64(pokeID), 10))
// 		_, err = UpsertTrainer(ctx, logger, tx, trainer)
// 		if err != nil {
// 			return runtime.NewError("UpsertTrainer error", 500)
// 		}
// 		return nil
// 	}
// 	// 查询是否存在对应的 Pokemon
// 	pokemon := poke.QueryPokeById(ctx, db, tid, pokeID)
// 	if pokemon == nil {
// 		return runtime.NewError("pokemon net found", 400)
// 	}

// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		logger.Error("db BeginTx: %v", err)
// 		return runtime.NewError("db BeginTx", 500)
// 	}
// 	defer func() {
// 		// 如果出错，则回滚事务
// 		if err != nil {
// 			tx.Rollback()
// 		} else {
// 			// 如果没有错误，则提交事务
// 			err = tx.Commit()
// 		}
// 	}()
// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		// 从 poke_boxs 表中读取数据
// 		var pokeBox MainServer.PokeBox
// 		var pokesJSON []byte
// 		err := tx.QueryRowContext(ctx, `
// 			SELECT pokes FROM $0 WHERE tid = $1 AND index = $2 AND type = $3
// 		`, TablePokeBoxName, tid, i, MainServer.PokeBoxType_normal).Scan(&pokesJSON)

// 		if err != nil {
// 			if err == sql.ErrNoRows {
// 				continue // 如果当前 Box 没有记录，跳到下一个 Box
// 			}
// 			return runtime.NewError("读取 Pokes 数据失败", 500)
// 		}

// 		// 将 JSON 反序列化成 PokeBox 对象
// 		if jsonErr := json.Unmarshal(pokesJSON, &pokeBox.Pokes); jsonErr != nil {
// 			return runtime.NewError("解析 Pokes JSON 失败", 500)
// 		}

// 		// 检查 Poke 是否已存在
// 		newPoke := &MainServer.BoxPokeInfo{
// 			Loc:      0,
// 			PokeId:   pokeID,
// 			PokeName: name,
// 			ValueTs:  0,
// 		}

// 		if pokeExists(pokeBox.Pokes, newPoke) {
// 			return runtime.NewError("已经捕捉到该 Pokemon", 500)
// 		}

// 		// 获取最小可用位置
// 		newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)
// 		if newLoc < 0 {
// 			continue // 尝试下一个 Box
// 		}

// 		// 设置新的位置
// 		newPoke.Loc = int32(newLoc)
// 		pokeBox.Pokes = append(pokeBox.Pokes, newPoke)

// 		// 更新 poke_boxs 表
// 		_, updateErr := tx.ExecContext(ctx, `
// 			UPDATE $0 SET pokes = $1, update_ts = $2 WHERE tid = $3 AND index = $4
// 		`, TablePokeBoxName, toJSONString(pokeBox.Pokes), time.Now().UnixMilli(), tid, i)

// 		if updateErr != nil {
// 			logger.Error("更新 Poke 失败: %v", updateErr)
// 			return runtime.NewError("更新 Poke 失败", 500)
// 		}

// 		// 更新 Pokemon 数据
// 		pokemon.Tid = tid
// 		if err := poke.UpdatePokeData(ctx, tx, pokemon); err != nil {
// 			return runtime.NewError("更新 Pokemon 失败", 500)
// 		}

// 		logger.Info("成功保存 Poke 到 Box，Box 索引：%d", i)
// 		return nil
// 	}

// 	logger.Warn("用户 %d 的所有 Poke Box 已满", tid)
// 	return runtime.NewError("所有 Poke Box 已满，无法保存", 500)
// }
// func hasTrainerBoxByIndex(ctx context.Context, db *sql.DB, index int) bool {
// 	tid := tool.GetActiveTid(ctx)
// 	err := db.QueryRowContext(ctx, `
// 			SELECT pokes FROM poke_boxs WHERE tid = $1 AND index = $2
// 		`, tid, index)
// }

// func SavePokeToNormalBox(ctx context.Context, logger runtime.Logger, db *sql.DB, ) error {

// }
// func savePokeToNormalBox(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, db *sql.DB, tid int64, pokeID int64, name string) error {
// 	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	pokemon := poke.QueryPokeById(ctx, db, tid, pokeID)
// 	if pokemon == nil {
// 		return runtime.NewError("pokemon 不存在", 400)
// 	}
// 	for i := 0; i < oneUserMaxBoxCount; i++ {
// 		collectionName := userPokesCollectionName + fmt.Sprintf("%d", i)

// 		// Step 1: 从 poke_boxs 表中读取数据
// 		var pokeBox MainServer.PokeBox
// 		var pokesJSON []byte
// 		err := db.QueryRowContext(ctx, `
// 			SELECT pokes FROM poke_boxs WHERE tid = $1 AND index = $2
// 		`, tid, i).Scan(&pokesJSON)

// 		if err != nil {
// 		// 	if err == sql.ErrNoRows {
// 		// 		// 如果没有数据，新建一个存储
// 		// 		newPokeData := []*MainServer.BoxPokeInfo{
// 		// 			{
// 		// 				Loc:      0, // 新的 Poke 位置是 0
// 		// 				PokeId:   pokeID,
// 		// 				PokeName: name,
// 		// 				ValueTs:  0, // 可以根据需要设置
// 		// 			},
// 		// 		}
// 		// 		// 插入新的 Poke 到表中
// 		// 		_, err := db.ExecContext(ctx, `
// 		// 			INSERT INTO poke_boxs (tid, index, type, pokes, create_ts, update_ts)
// 		// 			VALUES ($1, $2, $3, $4, $5, $6)
// 		// 		`, tid, i, MainServer.PokeBoxType_normal, toJSONString(newPokeData), time.Now().UnixMilli(), time.Now().UnixMilli())

// 		// 		if err != nil {
// 		// 			logger.Error("保存 Poke 失败: %v", err)
// 		// 			return runtime.NewError("保存 Poke 失败", 500)
// 		// 		}
// 		// 		pokemon.Tid = tid
// 		// 		err = poke.UpdatePokeData(ctx, db, pokemon)
// 		// 		if err != nil {
// 		// 			return runtime.NewError("更新 Pokemon 失败", 500)
// 		// 		}

// 		// 		logger.Info("成功保存 Poke 到新 Box: %s", collectionName)

// 		// 		// 如果是第一个 Box 并且 Metadata 中有 pokes 字段，需要同步
// 		// 		// if i == 0 {
// 		// 		// 	err = syncPokesToMetadata(ctx, logger, nk, userID, newPokeData)
// 		// 		// 	if err != nil {
// 		// 		// 		logger.Error("同步 Pokes 数据到 Metadata 失败: %v", err)
// 		// 		// 		return err
// 		// 		// 	}
// 		// 		// 	logger.Info("成功将 Pokes 数据同步到用户 Metadata")
// 		// 		// }

// 		// 		return nil
// 		// 	}
// 		// 	return runtime.NewError("读取 Pokes 数据失败", 500)
// 		// }
// 		if jsonErr := json.Unmarshal(pokesJSON, &pokeBox.Pokes); jsonErr != nil {
// 			return runtime.NewError("failed to parse pokes JSON: %v", 500)
// 		}
// 		newLoc := findSmallestAvailableLoc(pokeBox.Pokes, onePokeBoxVolume)

// 		// 如果已经达到了最大容量 //没有找到位置
// 		if newLoc < 0 {
// 			continue // 尝试下一个 Box
// 		}

// 		// Step 3: 添加新的 Poke 到现有列表
// 		newPoke := MainServer.BoxPokeInfo{
// 			Loc:      int32(newLoc), // 确定的位置
// 			PokeId:   pokeID,
// 			PokeName: name,
// 			ValueTs:  0, // 根据需要设置
// 		}
// 		if pokeExists(pokeBox.Pokes, &newPoke) {
// 			return runtime.NewError("已经捕捉到pokemon了", 500)
// 		}
// 		pokeBox.Pokes = append(pokeBox.Pokes, &newPoke)

// 		// Step 4: 更新 poke_boxs 表中的数据
// 		_, err = db.ExecContext(ctx, `
// 			UPDATE poke_boxs SET pokes = $1, update_ts = $2 WHERE tid = $3 AND index = $4
// 		`, toJSONString(pokeBox.Pokes), time.Now().UnixMilli(), tid, i)

// 		if err != nil {
// 			logger.Error("更新 Poke 失败: %v", err)
// 			return runtime.NewError("更新 Poke 失败", 500)
// 		}
// 		pokemon.Tid = tid
// 		err = poke.UpdatePokeData(ctx, db, pokemon)
// 		if err != nil {
// 			return runtime.NewError("更新 Pokemon 失败", 500)
// 		}
// 		logger.Info("成功保存 Poke 到 Box: %s", collectionName)

// 		// Step 5: 如果是第一个 Box 并且 Metadata 中有 pokes 字段，需要同步
// 		// if i == 0 {
// 		// 	err = syncPokesToMetadata(ctx, logger, nk, userID, pokeBox.Pokes)
// 		// 	if err != nil {
// 		// 		logger.Error("同步 Pokes 数据到 Metadata 失败: %v", err)
// 		// 		return err
// 		// 	}
// 		// 	logger.Info("成功将 Pokes 数据同步到用户 Metadata")
// 		// }

// 		return nil
// 	}

//		// 全部 Box 已满，保存失败
//		logger.Warn("用户 %s 的所有 Poke Box 已满", tid)
//		return runtime.NewError("所有 Poke Box 已满，无法保存", 500)
//	}
//
// initializeTrainerBoxes 统一处理盒子的初始化和扩容
func initializeTrainerBoxes(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, checkExpand bool) error {
	if trainer == nil {
		return runtime.NewError("trainer is nil", 500)
	}

	if trainer.BoxStatus == nil {
		trainer.BoxStatus = &MainServer.TrainerBoxStatus{
			ActiveBoxes:        0,
			SpecialActiveBoxes: 0, // 添加特殊盒子计数
		}
	}

	now := time.Now().UnixMilli()

	// 准备批量插入的语句
	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (tid, index, type, pokes, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (tid, id) DO UPDATE 
		SET type = EXCLUDED.type,
			update_ts = EXCLUDED.update_ts
	`, TablePokeBoxName))
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// 初始化普通盒子
	for i := trainer.BoxStatus.ActiveBoxes; i < oneUserMaxBoxCount; i++ {
		if _, err := stmt.ExecContext(ctx, trainer.Id, i, MainServer.PokeBoxType_normal, "{}", now, now); err != nil {
			return fmt.Errorf("failed to initialize/expand normal box at index %d: %v", i, err)
		}
	}
	trainer.BoxStatus.ActiveBoxes = oneUserMaxBoxCount

	// 如果有特殊权限，初始化特殊盒子
	if trainer.SpecialRight != MainServer.TrainerSpecialRight_TrainerNone {
		for i := trainer.BoxStatus.SpecialActiveBoxes; i < oneUserSpecialMaxBoxCount; i++ {
			if _, err := stmt.ExecContext(ctx, trainer.Id, i, MainServer.PokeBoxType_specialNormal, "{}", now, now); err != nil {
				return fmt.Errorf("failed to initialize special box at index %d: %v", i, err)
			}
		}
		trainer.BoxStatus.SpecialActiveBoxes = oneUserSpecialMaxBoxCount
	}

	if checkExpand {
		return nil
	}

	// 初始化单盒子类型
	singleBoxTypes := []MainServer.PokeBoxType{
		MainServer.PokeBoxType_hatch,
		MainServer.PokeBoxType_sale,
		MainServer.PokeBoxType_rent,
	}
	for _, boxType := range singleBoxTypes {
		if _, err := stmt.ExecContext(ctx, trainer.Id, 0, boxType, "{}", now, now); err != nil {
			return fmt.Errorf("failed to initialize single box type %v: %v", boxType, err)
		}
	}
	if trainer.SpecialRight != MainServer.TrainerSpecialRight_TrainerNone {
		singleBoxTypes = []MainServer.PokeBoxType{
			MainServer.PokeBoxType_specialHatch,
			MainServer.PokeBoxType_specialSale,
			MainServer.PokeBoxType_specialRent,
		}
		for _, boxType := range singleBoxTypes {
			if _, err := stmt.ExecContext(ctx, trainer.Id, 0, boxType, "{}", now, now); err != nil {
				return fmt.Errorf("failed to initialize special single box type %v: %v", boxType, err)
			}
		}
	}

	// 更新训练师状态
	if _, err := UpsertTrainer(ctx, logger, tx, trainer); err != nil {
		return fmt.Errorf("failed to update trainer after box initialization: %v", err)
	}

	return nil
}

// BatchGetPokeBoxes 批量获取指定类型和索引的盒子
func BatchGetPokeBoxes(ctx context.Context, tx *sql.Tx, tid int64, boxTypes []MainServer.PokeBoxType, indices []int32) (map[MainServer.PokeBoxType]map[int32]*MainServer.PokeBox, error) {
	query := fmt.Sprintf(`
		SELECT id, index, tid, name, type, pokes, create_ts, update_ts 
		FROM %s 
		WHERE tid = $1 AND type = ANY($2) AND index = ANY($3)
	`, TablePokeBoxName)

	// 转换参数类型
	boxTypeInts := make([]int32, len(boxTypes))
	for i, bt := range boxTypes {
		boxTypeInts[i] = int32(bt)
	}

	rows, err := tx.QueryContext(ctx, query, tid, boxTypeInts, indices)
	if err != nil {
		return nil, fmt.Errorf("failed to query boxes: %w", err)
	}
	defer rows.Close()

	result := make(map[MainServer.PokeBoxType]map[int32]*MainServer.PokeBox)
	for rows.Next() {
		var box MainServer.PokeBox
		var pokesJSON []byte

		err := rows.Scan(
			&box.Id,
			&box.Index,
			&box.Tid,
			&box.Name,
			&box.Type,
			&pokesJSON,
			&box.CreateTs,
			&box.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan box: %w", err)
		}

		if err := json.Unmarshal(pokesJSON, &box.Pokes); err != nil {
			return nil, fmt.Errorf("failed to parse pokes JSON: %w", err)
		}

		if result[box.Type] == nil {
			result[box.Type] = make(map[int32]*MainServer.PokeBox)
		}
		result[box.Type][box.Index] = &box
	}

	return result, nil
}
