package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/quest"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 表名常量
const TableTrainerQuest = "trainer_quest"

// InitTrainerQuestTable 初始化训练师任务表
func initTrainerQuestTable(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id SERIAL PRIMARY KEY,
			tid BIGINT NOT NULL,
			quest_id BIGINT NOT NULL,
			quest_type INTEGER NOT NULL DEFAULT 0,
			quest_status INTEGER NOT NULL DEFAULT 0,
			quest_current_info JSONB,
			quest_start_time BIGINT NOT NULL DEFAULT 0,
			quest_end_time BIGINT NOT NULL DEFAULT 0,
			quest_repeat_limit_time INTEGER NOT NULL DEFAULT 0,
			quest_info JSONB,
			create_ts BIGINT NOT NULL DEFAULT 0,
			update_ts BIGINT NOT NULL DEFAULT 0
		)
	`, TableTrainerQuest)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("Failed to create trainer_quest table: %v", err)
		return err
	}

	// 创建索引
	indexQueries := []string{
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_tid ON %s (tid)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_status ON %s (quest_status)", TableTrainerQuest),
		fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_trainer_quest_type ON %s (quest_type)", TableTrainerQuest),
	}

	for _, indexQuery := range indexQueries {
		_, err := db.ExecContext(ctx, indexQuery)
		if err != nil {
			logger.Error("Failed to create index: %v", err)
			return err
		}
	}

	// 初始化任务条件配置
	InitTrainerQuestConditions()

	logger.Info("Trainer quest table and conditions initialized successfully")
	return nil
}

// initializeQuestCurrentInfo 初始化任务当前信息结构
func initializeQuestCurrentInfo(questInfo *MainServer.QuestInfo) *MainServer.TrainerQuestCurrentInfo {
	if questInfo == nil {
		return &MainServer.TrainerQuestCurrentInfo{
			CurrentQuest:          nil,
			QuestDefaultCondition: make(map[string]int32),
			QuestProgress:         make(map[string]int32),
		}
	}

	// 确定当前任务
	currentQuest := determineCurrentQuest(questInfo)

	// 初始化默认条件
	defaultConditions := extractDefaultConditions(currentQuest)

	return &MainServer.TrainerQuestCurrentInfo{
		CurrentQuest:          currentQuest,
		QuestDefaultCondition: defaultConditions,
		QuestProgress:         make(map[string]int32),
	}
}

// determineCurrentQuest 确定当前任务（用于线性任务或随机任务）
func determineCurrentQuest(questInfo *MainServer.QuestInfo) *MainServer.QuestInfo {
	if questInfo == nil {
		return nil
	}

	// 如果是线性任务，取第一个任务作为当前任务
	if questInfo.LinearQuests != nil {
		// TODO: 根据实际的LinearQuests结构来获取第一个任务
		// 暂时返回questInfo本身
		return questInfo
	}

	// 如果是单个任务或其他类型，直接返回questInfo
	return questInfo
}

// extractDefaultConditions 从任务信息中提取默认完成条件
func extractDefaultConditions(questInfo *MainServer.QuestInfo) map[string]int32 {
	defaultConditions := make(map[string]int32)

	if questInfo == nil || questInfo.QuestCompleteInfo == nil {
		return defaultConditions
	}

	// 从完成条件中提取默认值
	for _, condition := range questInfo.QuestCompleteInfo.QuestCompleteConditions {
		if condition.QuestCondition != nil {
			if condition.QuestCompleteType == MainServer.QuestCompleteType_QuestCompleteType_battle_poke && condition.QuestCondition.ConditionNameId != "" {
				// key := fmt.Sprintf("%s_%s", condition.QuestCompleteType.String(), condition.QuestCondition.ConditionNameId)
				defaultConditions[condition.QuestCondition.ConditionNameId] = condition.QuestCondition.ConditionCount
			}
		}
	}

	return defaultConditions
}

// func getQuestCompleteTypeDefaultKey(completeType MainServer.QuestCompleteType) string {
// 	switch completeType {
// 	case MainServer.QuestCompleteType_QuestCompleteType_battle_poke:
// 		return
// 	}
// }

// AcceptQuest 接受任务
func AcceptQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questId int32) (*MainServer.TrainerQuest, error) {
	questInfo, err := quest.GetAndSimpleCheckQuestById(ctx, logger, tx, questId)
	if err != nil {
		return nil, fmt.Errorf("quest not found: %v", err)
	}
	if questInfo.QuestType == MainServer.QuestType_QuestType_once {
		questInfo, err := GetTrainerQuest(ctx, logger, tx, trainer.Id, questId)
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if questInfo != nil {
			return nil, fmt.Errorf("quest already accepted")
		}
	} else {
		sqlQuestInfo, err := GetTrainerQuest(ctx, logger, tx, trainer.Id, questId)
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if sqlQuestInfo != nil {
			change, err := tryUpdateQuestEnd(ctx, logger, tx, sqlQuestInfo)
			if err != nil {
				return nil, fmt.Errorf("failed to get trainer quest: %v", err)
			}
			if !change {
				return nil, fmt.Errorf("quest already accepted")
			}
		}
	}
	if questInfo.QuestRepeatLimit > 0 {
		// GetTrainerQuestList
		startTs := int64(0)
		interval := int64(0)
		if questInfo.QuestType == MainServer.QuestType_QuestType_daily {
			//今天0点0分0秒
			startTs = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local).Unix()
			interval = 24 * 60 * 60 // 24小时
			// if sqlQuest != nil && sqlQuest.QuestStartTime >= todayStartTs {
			// 	return nil, fmt.Errorf("daily quest already accepted today")
			// }
		} else if questInfo.QuestType == MainServer.QuestType_QuestType_weekly {
			// 本周的开始时间（假设周一为一周的开始）
			startTs = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()-int(time.Now().Weekday())+1, 0, 0, 0, 0, time.Local).Unix()
			interval = 7 * 24 * 60 * 60 // 7天
			// if sqlQuest != nil && sqlQuest.QuestStartTime >= weekStartTs {
			// 	return nil, fmt.Errorf("weekly quest already accepted this week")
			// }
		}
		sqlQuests, err := GetTrainerQuestsByTime(ctx, logger, tx, trainer.Id, questId, startTs, interval)
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if len(sqlQuests) >= int(questInfo.QuestRepeatLimit) {
			return nil, fmt.Errorf("quest repeat limit reached: %d", questInfo.QuestRepeatLimit)
		}
	}
	if questInfo.QuestRepeatInterval > 0 {
		sqlQuests, err := GetTrainerQuestsByTime(ctx, logger, tx, trainer.Id, questId, time.Now().Unix(), int64(questInfo.QuestRepeatInterval))
		if err != nil {
			return nil, fmt.Errorf("failed to get trainer quest: %v", err)
		}
		if len(sqlQuests) > 0 {
			return nil, fmt.Errorf("quest repeat interval not met, please wait %d seconds", questInfo.QuestRepeatInterval)
		}
	}
	checked, err := CheckQuestUnlockConditions(ctx, logger, tx, trainer, questInfo.QuestUnlockInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to check quest unlock conditions: %v", err)
	}
	if !checked {
		return nil, fmt.Errorf("quest unlock conditions not met")
	}
	// 暂时跳过任务验证
	// var questInfo *MainServer.QuestInfo

	// 检查任务时间和解锁条件
	nowTs := time.Now().Unix()
	if questInfo != nil {
		if questInfo.QuestStartTime > 0 && nowTs < questInfo.QuestStartTime {
			return nil, fmt.Errorf("quest not started yet")
		}
		if questInfo.QuestEndTime > 0 && nowTs > questInfo.QuestEndTime {
			return nil, fmt.Errorf("quest has ended")
		}

		// 检查解锁条件
		unlocked, err := CheckQuestUnlockConditions(ctx, logger, tx, trainer, questInfo.QuestUnlockInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to check unlock conditions: %v", err)
		}
		if !unlocked {
			return nil, fmt.Errorf("quest unlock conditions not met")
		}
	}

	// 允许重复接受任务，不再检查是否已经接受过

	// 创建训练师任务记录
	trainerQuest := &MainServer.TrainerQuest{
		Id:                   0, // 自动生成
		Tid:                  trainer.Id,
		QuestId:              int64(questId),
		QuestType:            questInfo.QuestType,
		QuestStatus:          MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
		QuestCurrentInfo:     initializeQuestCurrentInfo(questInfo),
		QuestStartTime:       nowTs,
		QuestEndTime:         0,
		QuestRepeatLimitTime: questInfo.QuestRepeatLimitTime,
		QuestInfo:            questInfo,
		UpdateTs:             nowTs,
	}

	// 插入数据库
	_, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return nil, fmt.Errorf("failed to add trainer quest: %v", err)
	}

	logger.Info("Trainer %d accepted quest %d", trainer.Id, questId)
	return trainerQuest, nil
}

func tryUpdateQuestEnd(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainerQuest *MainServer.TrainerQuest) (bool, error) {
	change := false
	if trainerQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		if trainerQuest.QuestInfo.QuestRepeatLimitTime > 0 && int64(trainerQuest.QuestInfo.QuestRepeatLimitTime)+trainerQuest.QuestStartTime < time.Now().Unix() {
			trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_timeout
			change = true
		}
		if trainerQuest.QuestEndTime > 0 && trainerQuest.QuestEndTime < time.Now().Unix() {
			trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_close
			change = true
		}
	}
	if change {
		// 更新数据库
		_, err := UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
		if err != nil {
			return change, fmt.Errorf("failed to update trainer quest: %v", err)
		}
	}
	return change, nil
}

func checkLastQuestIsEnd(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, trainerQuest *MainServer.TrainerQuest) bool {
	if trainerQuest.QuestStatus == MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		if trainerQuest.QuestInfo.QuestRepeatLimitTime > 0 && int64(trainerQuest.QuestInfo.QuestRepeatLimitTime)+trainerQuest.QuestStartTime < time.Now().Unix() {
			return true
		}
		if trainerQuest.QuestEndTime > 0 && trainerQuest.QuestEndTime < time.Now().Unix() {
			return true
		}
		return false
	}
	return true
}

// UpdateQuestProgress 更新任务进度
func UpdateQuestProgress(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, progressKey string, progressValue int32) error {
	// 获取活跃的训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return fmt.Errorf("quest is not in progress")
	}

	// 更新进度
	err = updateQuestProgressData(trainerQuest, progressKey, progressValue)
	if err != nil {
		return fmt.Errorf("failed to update progress data: %v", err)
	}

	// 检查是否完成
	completed, err := checkQuestCompletion(trainerQuest)
	if err != nil {
		return fmt.Errorf("failed to check quest completion: %v", err)
	}

	if completed {
		trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_finish
		trainerQuest.QuestEndTime = time.Now().Unix()
	}

	// 更新数据库
	_, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return fmt.Errorf("failed to update trainer quest: %v", err)
	}

	logger.Info("Updated quest progress for trainer %d, quest %d: %s=%d", tid, questId, progressKey, progressValue)
	return nil
}

// updateQuestProgressData 更新任务进度数据
func updateQuestProgressData(trainerQuest *MainServer.TrainerQuest, progressKey string, progressValue int32) error {
	// 确保QuestCurrentInfo存在
	if trainerQuest.QuestCurrentInfo == nil {
		trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{
			CurrentQuest:          nil,
			QuestDefaultCondition: make(map[string]int32),
			QuestProgress:         make(map[string]int32),
		}
	}

	// 确保QuestProgress存在
	if trainerQuest.QuestCurrentInfo.QuestProgress == nil {
		trainerQuest.QuestCurrentInfo.QuestProgress = make(map[string]int32)
	}

	// 更新进度值
	trainerQuest.QuestCurrentInfo.QuestProgress[progressKey] = progressValue

	// 更新时间戳
	trainerQuest.UpdateTs = time.Now().Unix()

	return nil
}

// checkQuestCompletion 检查任务是否完成
func checkQuestCompletion(trainerQuest *MainServer.TrainerQuest) (bool, error) {
	// 检查当前任务的完成条件
	if trainerQuest.QuestInfo == nil || trainerQuest.QuestInfo.QuestCompleteInfo == nil {
		return false, nil // 没有完成条件，不能自动完成
	}

	// 从QuestCurrentInfo中提取进度数据
	progressMap := make(map[string]interface{})
	if trainerQuest.QuestCurrentInfo != nil && trainerQuest.QuestCurrentInfo.QuestProgress != nil {
		for k, v := range trainerQuest.QuestCurrentInfo.QuestProgress {
			progressMap[k] = v
		}
	}

	// 检查完成条件
	// 这里需要根据current_quest来检查具体的完成条件
	// 暂时返回false，需要具体实现
	return false, nil
}

// CompleteQuest 完成任务
func CompleteQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) error {
	// 获取活跃的训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_finish {
		return fmt.Errorf("quest is not completed")
	}

	// 标记为已完成
	trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward
	trainerQuest.QuestEndTime = time.Now().Unix()

	// 更新数据库
	_, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return fmt.Errorf("failed to update trainer quest: %v", err)
	}

	logger.Info("Trainer %d completed quest %d", tid, questId)
	return nil
}

// ClaimQuestReward 领取任务奖励
func ClaimQuestReward(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) error {
	// 获取活跃的训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, tid, questId)
	if err != nil {
		return fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_reward {
		return fmt.Errorf("quest reward not available")
	}

	// TODO: 发放奖励 - 需要避免循环导入
	// err = quest.GrantQuestReward(ctx, logger, tx, tid, trainerQuest.QuestInfo.QuestRewardInfo)
	// if err != nil {
	//	return fmt.Errorf("failed to grant quest reward: %v", err)
	// }

	// 标记奖励已领取
	trainerQuest.QuestStatus = MainServer.TrainerQuestStatus_TrainerQuestStatus_reward // TODO: 确认正确的枚举值

	// 更新数据库
	_, err = UpsertTrainerQuest(ctx, logger, tx, trainerQuest)
	if err != nil {
		return fmt.Errorf("failed to update trainer quest: %v", err)
	}

	logger.Info("Trainer %d claimed reward for quest %d", tid, questId)
	return nil
}

// AddTrainerQuest 添加训练师任务记录
// func AddTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, trainerQuest *MainServer.TrainerQuest) error {
// 	nowTs := time.Now().Unix()

// 	query := fmt.Sprintf(`
// 		INSERT INTO %s (
// 			tid, quest_id, quest_type, quest_status,
// 			quest_start_time, quest_end_time, quest_repeat_limit_time,
// 			create_ts, update_ts
// 		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
// 		ON CONFLICT (tid, quest_id) DO UPDATE SET
// 			quest_status = EXCLUDED.quest_status,
// 			quest_start_time = EXCLUDED.quest_start_time,
// 			quest_end_time = EXCLUDED.quest_end_time,
// 			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
// 			update_ts = EXCLUDED.update_ts
// 	`, TableTrainerQuest)

// 	_, err := tx.ExecContext(ctx, query,
// 		tid, trainerQuest.QuestId, int32(trainerQuest.QuestType), int32(trainerQuest.QuestStatus),
// 		trainerQuest.QuestStartTime, trainerQuest.QuestEndTime,
// 		trainerQuest.QuestRepeatLimitTime, nowTs, nowTs,
// 	)

// 	if err != nil {
// 		logger.Error("Failed to add trainer quest: %v", err)
// 		return err
// 	}

//		return nil
//	}

// UpsertTrainerQuest 插入或更新训练师任务（参考UpsertTrainer模式）
func UpsertTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainerQuest *MainServer.TrainerQuest) (int64, error) {
	if trainerQuest == nil {
		return 0, fmt.Errorf("trainerQuest is nil")
	}
	if trainerQuest.QuestId <= 0 {
		return 0, fmt.Errorf("invalid quest id: %d", trainerQuest.QuestId)
	}

	nowTs := time.Now().Unix()

	// 如果ID小于等于0，生成新的ID
	var createTs int64
	if trainerQuest.Id <= 0 {
		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", TableTrainerQuest)).Scan(&trainerQuest.Id)
		if err != nil {
			return 0, fmt.Errorf("failed to generate new id: %v", err)
		}
		createTs = nowTs
	} else {
		// 如果是更新，保持原有的创建时间
		createTs = trainerQuest.QuestStartTime // 使用QuestStartTime作为创建时间的替代
	}

	// 序列化JSONB字段
	var questCurrentInfoJSON, questInfoJSON []byte
	var err error

	if trainerQuest.QuestCurrentInfo != nil {
		questCurrentInfoJSON, err = json.Marshal(trainerQuest.QuestCurrentInfo)
		if err != nil {
			return 0, fmt.Errorf("failed to marshal quest_current_info: %v", err)
		}
	}

	if trainerQuest.QuestInfo != nil {
		questInfoJSON, err = json.Marshal(trainerQuest.QuestInfo)
		if err != nil {
			return 0, fmt.Errorf("failed to marshal quest_info: %v", err)
		}
	}

	query := fmt.Sprintf(`
		INSERT INTO %s (
			id, tid, quest_id, quest_type, quest_status, quest_current_info,
			quest_start_time, quest_end_time, quest_repeat_limit_time, quest_info,
			create_ts, update_ts
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		ON CONFLICT (id) DO UPDATE SET
			tid = EXCLUDED.tid,
			quest_id = EXCLUDED.quest_id,
			quest_type = EXCLUDED.quest_type,
			quest_status = EXCLUDED.quest_status,
			quest_current_info = EXCLUDED.quest_current_info,
			quest_start_time = EXCLUDED.quest_start_time,
			quest_end_time = EXCLUDED.quest_end_time,
			quest_repeat_limit_time = EXCLUDED.quest_repeat_limit_time,
			quest_info = EXCLUDED.quest_info,
			update_ts = EXCLUDED.update_ts
		RETURNING id
	`, TableTrainerQuest)

	var id int64
	err = tx.QueryRowContext(ctx, query,
		trainerQuest.Id, trainerQuest.Tid, trainerQuest.QuestId, int32(trainerQuest.QuestType),
		int32(trainerQuest.QuestStatus), questCurrentInfoJSON,
		trainerQuest.QuestStartTime, trainerQuest.QuestEndTime,
		trainerQuest.QuestRepeatLimitTime, questInfoJSON, createTs, nowTs,
	).Scan(&id)

	if err != nil {
		logger.Error("Failed to upsert trainer quest: %v", err)
		return 0, err
	}

	logger.Debug("Trainer quest upsert successful: id=%d, tid=%d, quest_id=%d", id, trainerQuest.Tid, trainerQuest.QuestId)
	return id, nil
}

// TrainerQuestFilter 训练师任务查询过滤器
type TrainerQuestFilter struct {
	Tid              *int64                          // 训练师ID
	QuestId          *int32                          // 任务ID
	QuestStatus      []MainServer.TrainerQuestStatus // 任务状态列表
	QuestType        *MainServer.QuestType           // 任务类型
	StartTimeAfter   *int64                          // 开始时间之后
	StartTimeBefore  *int64                          // 开始时间之前
	UpdateTimeAfter  *int64                          // 更新时间之后
	UpdateTimeBefore *int64                          // 更新时间之前
	OrderBy          string                          // 排序字段 (update_ts, quest_start_time, id)
	OrderDesc        bool                            // 是否降序
	Limit            *int                            // 限制数量
}

// QueryTrainerQuests 通用的训练师任务查询函数
func QueryTrainerQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, filter *TrainerQuestFilter) ([]*MainServer.TrainerQuest, error) {
	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, quest_id, quest_type, quest_status, quest_current_info,
		       quest_start_time, quest_end_time, quest_repeat_limit_time, quest_info,
		       create_ts, update_ts
		FROM %s WHERE 1=1`, TableTrainerQuest)

	var args []interface{}
	argIndex := 1

	// 添加过滤条件
	if filter.Tid != nil {
		query += fmt.Sprintf(" AND tid = $%d", argIndex)
		args = append(args, *filter.Tid)
		argIndex++
	}

	if filter.QuestId != nil {
		query += fmt.Sprintf(" AND quest_id = $%d", argIndex)
		args = append(args, *filter.QuestId)
		argIndex++
	}

	if len(filter.QuestStatus) > 0 {
		statusPlaceholders := make([]string, len(filter.QuestStatus))
		for i, status := range filter.QuestStatus {
			statusPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, int32(status))
			argIndex++
		}
		query += fmt.Sprintf(" AND quest_status IN (%s)", strings.Join(statusPlaceholders, ","))
	}

	if filter.QuestType != nil {
		query += fmt.Sprintf(" AND quest_type = $%d", argIndex)
		args = append(args, int32(*filter.QuestType))
		argIndex++
	}

	if filter.StartTimeAfter != nil {
		query += fmt.Sprintf(" AND quest_start_time > $%d", argIndex)
		args = append(args, *filter.StartTimeAfter)
		argIndex++
	}

	if filter.StartTimeBefore != nil {
		query += fmt.Sprintf(" AND quest_start_time < $%d", argIndex)
		args = append(args, *filter.StartTimeBefore)
		argIndex++
	}

	if filter.UpdateTimeAfter != nil {
		query += fmt.Sprintf(" AND update_ts > $%d", argIndex)
		args = append(args, *filter.UpdateTimeAfter)
		argIndex++
	}

	if filter.UpdateTimeBefore != nil {
		query += fmt.Sprintf(" AND update_ts < $%d", argIndex)
		args = append(args, *filter.UpdateTimeBefore)
		argIndex++
	}

	// 添加排序
	orderBy := "update_ts" // 默认按更新时间排序
	if filter.OrderBy != "" {
		switch filter.OrderBy {
		case "update_ts", "quest_start_time", "id", "create_ts":
			orderBy = filter.OrderBy
		default:
			logger.Warn("Invalid order by field: %s, using default: update_ts", filter.OrderBy)
		}
	}

	orderDirection := "DESC"
	if !filter.OrderDesc {
		orderDirection = "ASC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDirection)

	// 添加限制
	if filter.Limit != nil && *filter.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filter.Limit)
	}

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		logger.Error("Failed to query trainer quests: %v", err)
		return nil, err
	}
	defer rows.Close()

	var trainerQuests []*MainServer.TrainerQuest
	for rows.Next() {
		trainerQuest := &MainServer.TrainerQuest{}
		var questStatus, questType int32
		var questCurrentInfoJSON, questInfoJSON []byte
		var createTs int64

		err := rows.Scan(
			&trainerQuest.Id, &trainerQuest.QuestId, &questType,
			&questStatus, &questCurrentInfoJSON,
			&trainerQuest.QuestStartTime, &trainerQuest.QuestEndTime, &trainerQuest.QuestRepeatLimitTime,
			&questInfoJSON, &createTs, &trainerQuest.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan trainer quest: %v", err)
			continue
		}

		// 设置枚举类型
		trainerQuest.QuestStatus = MainServer.TrainerQuestStatus(questStatus)
		trainerQuest.QuestType = MainServer.QuestType(questType)

		// 反序列化JSONB字段
		if questCurrentInfoJSON != nil {
			trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{}
			if err := json.Unmarshal(questCurrentInfoJSON, trainerQuest.QuestCurrentInfo); err != nil {
				logger.Error("Failed to unmarshal quest_current_info: %v", err)
				trainerQuest.QuestCurrentInfo = nil
			}
		}

		if questInfoJSON != nil {
			trainerQuest.QuestInfo = &MainServer.QuestInfo{}
			if err := json.Unmarshal(questInfoJSON, trainerQuest.QuestInfo); err != nil {
				logger.Error("Failed to unmarshal quest_info: %v", err)
				trainerQuest.QuestInfo = nil
			}
		}

		trainerQuests = append(trainerQuests, trainerQuest)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error iterating trainer quest rows: %v", err)
		return nil, err
	}

	return trainerQuests, nil
}

// GetTrainerQuest 获取训练师任务信息（获取最新的一个）
func GetTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (*MainServer.TrainerQuest, error) {
	limit := 1
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestId:   &questId,
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     &limit,
	}

	quests, err := QueryTrainerQuests(ctx, logger, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(quests) == 0 {
		return nil, fmt.Errorf("trainer quest not found")
	}

	return quests[0], nil
}

// GetActiveTrainerQuest 获取训练师特定任务的活跃记录（未完成的最新记录）
func GetActiveTrainerQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) (*MainServer.TrainerQuest, error) {
	limit := 1
	filter := &TrainerQuestFilter{
		Tid:     &tid,
		QuestId: &questId,
		QuestStatus: []MainServer.TrainerQuestStatus{
			MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
			MainServer.TrainerQuestStatus_TrainerQuestStatus_finish,
		},
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     &limit,
	}

	quests, err := QueryTrainerQuests(ctx, logger, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(quests) == 0 {
		return nil, fmt.Errorf("active trainer quest not found")
	}

	return quests[0], nil
}

// GetTrainerQuestHistory 获取训练师任务历史记录
func GetTrainerQuestHistory(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestId:   &questId,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestsByTime 获取指定时间范围内的训练师任务记录
// 查询条件: startTimeTs - interval < quest_start_time < startTimeTs
func GetTrainerQuestsByTime(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questId int32, startTimeTs int64, interval int64) ([]*MainServer.TrainerQuest, error) {
	minTimeTs := startTimeTs - interval

	filter := &TrainerQuestFilter{
		Tid:             &tid,
		QuestId:         &questId,
		StartTimeAfter:  &minTimeTs,
		StartTimeBefore: &startTimeTs,
		OrderBy:         "quest_start_time",
		OrderDesc:       true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// CheckQuestCanComplete 检查任务是否可以完成
func CheckQuestCanComplete(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questId int32) (bool, error) {
	// 获取活跃的训练师任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, trainer.Id, questId)
	if err != nil {
		return false, fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查任务状态
	if trainerQuest.QuestStatus != MainServer.TrainerQuestStatus_TrainerQuestStatus_accept {
		return false, fmt.Errorf("quest is not in progress")
	}

	// 检查完成条件
	if trainerQuest.QuestInfo != nil && trainerQuest.QuestInfo.QuestCompleteInfo != nil {
		// 从quest_current_info中获取进度数据
		progress := make(map[string]interface{})
		if trainerQuest.QuestCurrentInfo != nil && trainerQuest.QuestCurrentInfo.QuestProgress != nil {
			for k, v := range trainerQuest.QuestCurrentInfo.QuestProgress {
				progress[k] = v
			}
		}

		completed, err := CheckQuestCompleteConditions(ctx, logger, tx, trainer, trainerQuest.QuestInfo.QuestCompleteInfo, progress)
		if err != nil {
			return false, fmt.Errorf("failed to check complete conditions: %v", err)
		}
		return completed, nil
	}

	// 如果没有完成条件，默认可以完成
	return true, nil
}

// GetCurrentQuest 获取当前任务信息（从quest_progress中的current_quest字段）
func GetCurrentQuest(trainerQuest *MainServer.TrainerQuest) *MainServer.QuestInfo {
	// 根据新的数据结构，current_quest应该是TrainerQuest的一个字段
	// 如果proto中有CurrentQuest字段，直接返回
	// 否则返回QuestInfo作为当前任务
	return trainerQuest.QuestInfo
}

// UpdateCurrentQuest 更新当前任务（用于线性任务的推进）
func UpdateCurrentQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainerQuest *MainServer.TrainerQuest, newCurrentQuest *MainServer.QuestInfo) error {
	// 如果是线性任务，需要更新current_quest字段
	// 这里需要根据实际的proto结构来实现

	// 暂时更新QuestInfo字段
	trainerQuest.QuestInfo = newCurrentQuest

	// 更新数据库
	_, err := UpsertTrainerQuest(ctx, logger, tx, trainerQuest) // tid会从trainerQuest中获取
	if err != nil {
		return fmt.Errorf("failed to update current quest: %v", err)
	}

	logger.Info("Updated current quest for trainer quest %d", trainerQuest.Id)
	return nil
}

// AdvanceLinearQuest 推进线性任务到下一个阶段
func AdvanceLinearQuest(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, questId int32) error {
	// 获取当前任务
	trainerQuest, err := GetActiveTrainerQuest(ctx, logger, tx, trainer.Id, questId)
	if err != nil {
		return fmt.Errorf("active trainer quest not found: %v", err)
	}

	// 检查是否是线性任务
	if trainerQuest.QuestInfo == nil || trainerQuest.QuestInfo.LinearQuests == nil {
		return fmt.Errorf("not a linear quest")
	}

	// TODO: 根据实际的LinearQuests结构来获取下一个任务
	// 这里需要实现线性任务的推进逻辑

	logger.Info("Advanced linear quest for trainer %d, quest %d", trainer.Id, questId)
	return nil
}

// 便利查询函数

// GetTrainerQuestsByStatus 根据状态获取训练师任务
func GetTrainerQuestsByStatus(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, statuses []MainServer.TrainerQuestStatus) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:         &tid,
		QuestStatus: statuses,
		OrderBy:     "update_ts",
		OrderDesc:   true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerActiveQuests 获取训练师所有活跃任务
func GetTrainerActiveQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerQuest, error) {
	return GetTrainerQuestsByStatus(ctx, logger, tx, tid, []MainServer.TrainerQuestStatus{
		MainServer.TrainerQuestStatus_TrainerQuestStatus_accept,
	})
}

// GetTrainerCompletedQuests 获取训练师已完成任务
func GetTrainerCompletedQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, limit *int) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid: &tid,
		QuestStatus: []MainServer.TrainerQuestStatus{
			MainServer.TrainerQuestStatus_TrainerQuestStatus_reward,
		},
		OrderBy:   "update_ts",
		OrderDesc: true,
		Limit:     limit,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestsByType 根据任务类型获取训练师任务
func GetTrainerQuestsByType(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, questType MainServer.QuestType) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		QuestType: &questType,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetRecentTrainerQuests 获取训练师最近的任务记录
func GetRecentTrainerQuests(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, hours int64, limit *int) ([]*MainServer.TrainerQuest, error) {
	recentTime := time.Now().Unix() - (hours * 3600)

	filter := &TrainerQuestFilter{
		Tid:             &tid,
		UpdateTimeAfter: &recentTime,
		OrderBy:         "update_ts",
		OrderDesc:       true,
		Limit:           limit,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// GetTrainerQuestList 获取训练师任务列表（兼容旧接口）
func GetTrainerQuestList(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) ([]*MainServer.TrainerQuest, error) {
	filter := &TrainerQuestFilter{
		Tid:       &tid,
		OrderBy:   "update_ts",
		OrderDesc: true,
	}

	return QueryTrainerQuests(ctx, logger, tx, filter)
}

// 便利函数：处理TrainerQuestCurrentInfo

// GetCurrentQuestFromTrainerQuest 从TrainerQuest中获取当前任务信息
func GetCurrentQuestFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) *MainServer.QuestInfo {
	if trainerQuest == nil || trainerQuest.QuestCurrentInfo == nil {
		return nil
	}
	return trainerQuest.QuestCurrentInfo.CurrentQuest
}

// GetQuestProgressFromTrainerQuest 从TrainerQuest中获取任务进度
func GetQuestProgressFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) map[string]int32 {
	if trainerQuest == nil || trainerQuest.QuestCurrentInfo == nil {
		return make(map[string]int32)
	}
	if trainerQuest.QuestCurrentInfo.QuestProgress == nil {
		return make(map[string]int32)
	}
	return trainerQuest.QuestCurrentInfo.QuestProgress
}

// GetQuestDefaultConditionFromTrainerQuest 从TrainerQuest中获取默认完成条件
func GetQuestDefaultConditionFromTrainerQuest(trainerQuest *MainServer.TrainerQuest) map[string]int32 {
	if trainerQuest == nil || trainerQuest.QuestCurrentInfo == nil {
		return make(map[string]int32)
	}
	if trainerQuest.QuestCurrentInfo.QuestDefaultCondition == nil {
		return make(map[string]int32)
	}
	return trainerQuest.QuestCurrentInfo.QuestDefaultCondition
}

// UpdateQuestCurrentInfo 更新TrainerQuest的当前信息
func UpdateQuestCurrentInfo(trainerQuest *MainServer.TrainerQuest, currentQuest *MainServer.QuestInfo, defaultConditions map[string]int32, progress map[string]int32) {
	if trainerQuest == nil {
		return
	}

	if trainerQuest.QuestCurrentInfo == nil {
		trainerQuest.QuestCurrentInfo = &MainServer.TrainerQuestCurrentInfo{}
	}

	trainerQuest.QuestCurrentInfo.CurrentQuest = currentQuest

	if defaultConditions != nil {
		trainerQuest.QuestCurrentInfo.QuestDefaultCondition = defaultConditions
	}

	if progress != nil {
		trainerQuest.QuestCurrentInfo.QuestProgress = progress
	}

	// 更新时间戳
	trainerQuest.UpdateTs = time.Now().Unix()
}
